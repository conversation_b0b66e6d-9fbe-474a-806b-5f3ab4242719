#pragma once

#include "CrossBase/Template/TypeTraits.hpp"
#include "Resource/Animation/Sequence/AnimSequence.h"
#include "CEAnimation/AnimationForward.h"
#include "CEAnimation/AnimBase.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "CEAnimation/Notify/AnimNotify.h"

#include <thread>
#include <chrono>
#include <mutex>

using namespace cross::anim::time;

namespace cross::anim
{
    struct AnimSection;
    using SectionHandle = THandle<AnimSection, UInt32>;

    struct Animation_API SlotTrackResSegment
    {
        /** Segment's tag */
        DEFRES_VARIABLE_TO_STRING(CEName, Name, NAME_NONE)
        /** Resource reltv path */
        DEFRES_VARIABLE_TO_STRING(CEName, ReltvPath, NAME_NONE)
        /** Time to start playing AnimSequence at. */
        DEFRES_VARIABLE_TO_STRING(time::RawH, StartPos, time::RawH::InvalidHandle())
        /** Time to end playing the AnimSequence at. */
        DEFRES_VARIABLE_TO_STRING(time::RawH, EndPos, time::RawH::InvalidHandle())
        /** Playback speed of this animation. If you'd like to reverse, set -1*/
        DEFRES_VARIABLE_TO_STRING(float, PlayRate, 0.f)
        /** Less than or equal 0 means current Segment is invalid, loop behavior not handled by segment but section only **/
        DEFRES_VARIABLE_TO_STRING(UInt32, LoopingCount, 1)
        /*  */
        DEFRES_VARIABLE_TO_STRING(SInt32, PriorityDelta, 0)

        /** JSON -- Latest Deserialize struct version holding in struct, others got central management */
        bool Deserialize(const DeserializeNode& node);


        /** JSON -- Latest Serialize struct version holding in struct, others got central management */
        void Serialize(SerializeNode& node) const;
    };

    struct SlotTrackResSection
    {
        DEFRES_VARIABLE_TO_STRING(CEName, Name, NAME_NONE)
        DEFRES_VARIABLE_TO_STRING(SInt32, SegmentStartIndex, -1)
        DEFRES_VARIABLE_TO_STRING(SInt32, SegmentEndIndex, -1)
        DEFRES_VARIABLE_TO_STRING(bool, Loop, false)

        // Non-serialized, grab after deserialize from json
        CEName NextName = "";
        // Non-serialized, grab after deserialize from json
        SectionHandle NextIndex = SectionHandle::InvalidHandle();

        bool IsOverlapped(SlotTrackResSection const& other)
        {
            if (other.SegmentStartIndex >= SegmentStartIndex && other.SegmentStartIndex <= SegmentEndIndex)
                return true;

            if (other.SegmentEndIndex >= SegmentStartIndex && other.SegmentEndIndex <= SegmentEndIndex)
                return true;

            return false;
        }

        /** JSON -- Latest Deserialize struct version holding in struct, others got central management */
        bool Deserialize(const DeserializeNode& node);


        /** JSON -- Latest Serialize struct version holding in struct, others got central management */
        void Serialize(SerializeNode& node) const;
    };

    struct Animation_API SlotTrackRes
    {
        /* slot name for each track */
        DEFRES_VARIABLE_TO_STRING(CEName, SlotName, AnimSlotGroup::sDefaultSlotName)

        /* segments from Disk File */
        DEFRES_VARIABLE_TO_STRING(std::vector<SlotTrackResSegment>, SegmentsDesc, {});

        /* sections from Disk File */
        DEFRES_VARIABLE_TO_STRING(std::vector<SlotTrackResSection>, SectionsDesc, {});

    public:
        /** JSON -- Latest Deserialize struct version holding in struct, others got central management */
        bool Deserialize(const DeserializeNode& node);


        /** JSON -- Latest Serialize struct version holding in struct, others got central management */
        void Serialize(SerializeNode& node) const;
        SerializeNode Serialize() const;


        inline bool IsAnySectionExists() const
        {
            return SectionsDesc.size() > 0;
        }

        size_t GetSegmentCount() const
        {
            return SegmentsDesc.size();
        }

        const SlotTrackResSegment& GetSegmentDesc(SInt32 segIndex) const
        {
            Assert(segIndex >= 0 && segIndex < SegmentsDesc.size());
            return SegmentsDesc[segIndex];
        }
    };

    /* Struct defining a RootMotionExtractionStep.
     * When extracting RootMotion we can encounter looping animations (wrap around), or different animations.
     * We break those up into different steps, to help with RootMotion extraction,
     * as we can only extract a contiguous range per AnimSequence.
     */
    struct RootMotionExtractionStep
    {
        /* IAnimSequence ptr, NOT AnimSequence ptr.
         * Because we have AnimComposite inherited from IAnimSequence.
         * By this, we can unify AnimSequence and AnimComposite.
         */
        IAnimSequence* IAnimSeqPtr{ nullptr };

        /** Start position to extract root motion from. */
        RawH StartPos{ 0.f };

        /** End position to extract root motion to. */
        RawH EndPos{ 0.f };

        RootMotionExtractionStep() {}

        RootMotionExtractionStep(IAnimSequence* inIAnimSeqPtr, const RawH& inStartPos, const RawH& inEndPos)
            : IAnimSeqPtr(inIAnimSeqPtr)
            , StartPos(inStartPos)
            , EndPos(inEndPos)
        {}
    };

    /* this is anim segment that defines a section for particular animation sequence
     *  All the TIME-UNIT involved in parameter is second.
     */
    struct AnimSegment
    {
        /* Anim Reference to play - only allow AnimSequence or AnimComposite */
        IAnimSequence* SequencePtr{ nullptr };

        /* Resource reference */
        SlotTrackResSegment const& Reference;

        /* Start Pos within Virtual Animation while combining multi-anim together, single-anim got 0 forever */
        TrackUnWrapperH StartPos = {0.f};

        AnimSegment() = delete;

        ~AnimSegment() {}

        AnimSegment(const AnimSegment& inSegment)
            : SequencePtr(inSegment.SequencePtr)
            , StartPos(inSegment.StartPos)
            , Reference(inSegment.Reference)
        {}

        AnimSegment(IAnimSequence* inSequence, SlotTrackResSegment const& inReference, TrackUnWrapperH inStartPos)
            : SequencePtr(inSequence)
            , Reference(inReference)
            , StartPos(inStartPos)
        {}

        inline friend bool operator== (const AnimSegment& lhs, const AnimSegment& rhs) noexcept
        {
            return lhs.SequencePtr != nullptr && lhs.SequencePtr == rhs.SequencePtr;
        }

        inline friend bool operator== (const AnimSegment& lhs, const IAnimSequence* rhs) noexcept
        {
            return lhs.SequencePtr != nullptr && lhs.SequencePtr == rhs;
        }


        AnimSegment& operator=(AnimSegment const& obj)
        {
            this->~AnimSegment();        
            return *this;
        }


        /* Ensure that PlayRate is non-zero */
        inline float GetValidPlayRate() const { return (std::abs)(Reference.PlayRate) < 0.0001f ? 1.f : Reference.PlayRate; }

        inline float GetRawSingleLength() const { return Reference.EndPos - Reference.StartPos; }

        inline float GetRawLoopingLength() const { return GetRawSingleLength() * Reference.LoopingCount; }

        inline float GetRunSingleLength() const { return GetRawSingleLength() / GetValidPlayRate(); }

        inline float GetRunLoopingLength() const { return GetRunSingleLength() * Reference.LoopingCount; }

        inline TrackUnWrapperH GetTrackUnWrapperEndPos() const { return { StartPos + GetRunLoopingLength() }; }

        inline SInt32 GetSingleNumberOfFrames() const { return (SInt32)(SequencePtr->GetFrameCount() * GetRawSingleLength() / SequencePtr->GetRunLength()); }

        inline SInt32 GetLoopingNumberOfFrames() const { return GetSingleNumberOfFrames() * Reference.LoopingCount; }

        // return true if track pos is within segment range
        inline bool IsInRange(const TrackUnWrapperH& inTrackPos) const { return ((inTrackPos >= StartPos) && (inTrackPos <= GetTrackUnWrapperEndPos())); }

        // Track time combined by segments-time APPLYING segment-play-rate
        inline SegmentUnWrapperH ConvertToSegUnWrapper(const TrackUnWrapperH& inTrackPos) const { return { (inTrackPos - StartPos) * GetValidPlayRate() }; }
        inline SegmentWrapperH ConvertToSegWrapper(const TrackUnWrapperH& inTrackPos) const { return {inTrackPos - StartPos}; }

        // SegmentUnWrapperH combined by RAW which NOT APPLYING play-rate 
        inline RawH ConvertToRaw(const SegmentUnWrapperH& inSegmentPos) const { return {(std::fmod)(inSegmentPos, GetRawSingleLength())}; }

        // return true if track range [inStartPos, inEndPos] overlaps segment.
        // supports playing backwards.
        inline bool IsTrackRangeOverlap(const TrackUnWrapperH& startTrackPos, const TrackUnWrapperH& endTrackPos) const
        {
            const bool bTrackPlayingBwd = (startTrackPos > endTrackPos);
            const auto segEndPosInTrack = GetTrackUnWrapperEndPos();

            return (bTrackPlayingBwd
                ? ((endTrackPos < segEndPosInTrack) && (startTrackPos > StartPos))
                : ((startTrackPos < segEndPosInTrack) && (endTrackPos > StartPos)));
        }

        /* Converts 'Track Position' to position on AnimSequence.
         *  InTrackPos: The time played in second in AnimTrack which assembled from multi-AnimSequence,
         *  'Track Position' could exceed the range of 'SegEndTime - SegStartTime' while Loop Count > 1
         *  caused by 'Track Position' is the running time, not the turn back Cursor in Anim-Track Tunnel
         */
        RawH ConvertTrackPosToAnimPos(const TrackUnWrapperH& inTrackPos) const;

        auto GetAnimationCursor(const TrackUnWrapperH& inTrackPos) const->std::pair<IAnimSequence*, RawH>;

        /*
         * Retrieves specified TypeData between a track range [startTrackPos, endTrackPos]
         * Between startTrackPos (exclusive) and endTrackPos (inclusive).
         * Supports playing backwards (endTrackPos < startTrackPos).
         * Only supports contiguous range, does NOT support looping and wrapping over.
         * TypeData is AnimNotifyEventReference or RootMotionExtractionStep.
         */
        template<typename TypeData>
        void GetTypeDataFromTrackRange(const TrackUnWrapperH& startTrackPos, const TrackUnWrapperH& endTrackPos, std::vector<TypeData>& outData) const;

        /* return true if anim notify is available */
        inline bool IsNotifyAvailable() const { return SequencePtr && SequencePtr->IsNotifyAvailable(); }

        /*
         * return true if valid, false otherwise, only invalid if we contains recursive reference
         */
        inline bool IsValid() const { return SequencePtr != nullptr; }

        /*
         * Get Animation Pose for the Time given, relative to Parent for all RequiredBones
         * AnimPlayDirection could change direction while segment play-rate got negative sign
         */
        void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const;

        void ExtractAnimCurves(const TrackUnWrapperH& curPose, AnimCurveData& outAnimCurves) const;
    };

    /* This is list of anim segments for this track, length should be confirmed after create
     *  For now this is only one Array
     *  All the TIME-UNIT involved in parameter is second.
     */
    struct Animation_API AnimTrack
    {
        std::vector<AnimSegment> AnimSegments;
        std::set<IAnimSequence*> AnimSequences;
        CEName SlotName = AnimSlotGroup::sDefaultSlotName;

    public:
        AnimTrack& operator=(const AnimTrack& other) noexcept;

        virtual bool Deserialize(const SlotTrackRes& slotTrack, const std::vector<AnimSeqPtr>& inAnimSeqs, const std::vector<AnimNotifyEvent *>& Notifies);


        /* Track data serialized by json only no matter what type it is */
        virtual void Serialize(SerializeNode& node) const;


        /* section should only occurs in SectionedTrack, all track holding by composite got no section at all */
        virtual bool IsAnySectionExist() const { return false; }

        inline float GetRunLength() const
        {
            float totalLength = 0.f;

            for (const AnimSegment& segment : AnimSegments)
            {
                float endFrame = segment.GetTrackUnWrapperEndPos();
                if (endFrame > totalLength)
                    totalLength = endFrame;
            }

            return totalLength;
        }

        inline SInt32 GetRunFrames() const
        {
            SInt32 totalFrames = 0;

            for (const AnimSegment& segment : AnimSegments)
            {
                totalFrames += segment.GetLoopingNumberOfFrames();
            }
            return totalFrames;
        }

        /* Returns whether any of the animation sequences this track uses has root motion */
        inline bool HasRootMotion() const
        {
            for (const AnimSegment& segment : AnimSegments)
            {
                if (segment.SequencePtr != nullptr && segment.SequencePtr->HasRootMotion())
                    return true;
            }

            return false;
        }

        /* Get the index of the segment at the given absolute playing time. */
        SInt32 GetSegmentIndexAtTime(TrackUnWrapperH inTime) const;

        /* Get the segment at the given absolute Animatrix time */
        AnimSegment* GetSegmentAtTime(TrackUnWrapperH inTime);
        const AnimSegment* GetSegmentAtTime(TrackUnWrapperH inTime) const;

        /* Get animation pose function
         * Animation toward determined by sign of track & particular running segment
         */
        virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const;

        // Extract anim curves
        virtual void ExtractAnimCurves(const TrackUnWrapperH& curPose, AnimCurveData& outAnimCurves) const;

        /*
         * Retrieves AnimNotifies between	a track range [startTrackPos, endTrackPos]
         * Between startTrackPos (exclusive) and endTrackPos (inclusive).
         * Supports playing backwards (endTrackPos < startTrackPos).
         * Only supports contiguous range, does NOT support looping and wrapping over.
         */
        virtual void GetAnimNotifiesFromTrackRange(
            const TrackUnWrapperH& startTrackPos,
            const TrackUnWrapperH& endTrackPos,
            std::vector<AnimNotifyEventReference>& OutActiveNotifies) const;

        /*
         * Given a track range [startTrackPos, endTrackPos]
         * See if this AnimSegment overlaps any of it, and if it does, break it up into RootMotionExtractionSteps.
         * Supports animation playing forward and backward.
         * Track segment should be a contiguous range, not wrapping over due to looping.
         */
        virtual void GetRootMotionExtractionStepsFromTrackRange(
            const TrackUnWrapperH& startTrackPos,
            const TrackUnWrapperH& endTrackPos,
            std::vector<RootMotionExtractionStep>& outRootMotionExtractionSteps) const;

        void GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const;

        /// Guarantee for each segment have no recursive asset like this:
        /// asset compA got one track --> track --> seg-0,1,2
        /// track is invalid no matter which segment = compA

        /* Moves anim segments so that there are no gaps between one finishing
         *  and the next starting, preserving the order of AnimSegments
         */
        void CollapseAnimSegments() {}

        /* Ensure segment times are correctly formed (no gaps and no extra time at the end of the anim reference) */
        void ValidateSegmentTimes();

        // this is to prevent anybody adding recursive asset to anim composite
        // as a result of anim composite being a part of anim sequence base
        void InvalidateRecursiveAsset(class AnimCompositeBase* inCheckAnim);

        // this is recursive function that look thorough internal assets
        // and clear the reference if recursive is found.
        // We're going to remove the top reference if found
        bool ContainRecursive(std::vector<const IAnimSequence*>& inAccumulatedList) const;

        /* return true if anim notify is available */
        bool IsNotifyAvailable() const;
    };

    class Animation_API AnimReferenceTrackBase
    {
    public:
        virtual bool IsCompleted(float inBlendTime = 0.001f, bool isLoopAllowed = false) const = 0;

        virtual float GetRunLength() const = 0;

        virtual float GetCurSectionedLength() const = 0;

        virtual AdvanceAnim::Type Advance(float deltaTime, bool isLoopAllowed = false) = 0;

        virtual void RatioScaleCursor(float preRatio, float curRatio);

        virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const = 0;

        virtual void ExtractAnimCurves(AnimCurveData& outAnimCurves) const = 0;

        sync::SyncMarkerUpdateRecord& GetSyncMarkerUpdateRecord() { return mMarkerUpdateRecord; }

        sync::AnimSyncMarkerData& GetSyncTrackMarkerData() { return mTrackMarkersData; }

        sync::AnimSyncMarkerData const& GetSyncTrackMarkerData() const { return mTrackMarkersData; }

        // discard pre pos for now
        void Decline() { mCurPos = mPrePos; }

        inline void Reset() 
        {
            mCurPos = { 0 };
            mPrePos = { 0 };
        }

        inline TrackUnWrapperH& GetCursor() { return mCurPos; }
        inline TrackUnWrapperH& GetPreCursor() { return mPrePos; }

        inline TrackUnWrapperH const& GetCursor() const { return mCurPos; }
        inline TrackUnWrapperH const& GetPreCursor() const { return mPrePos; }

        void SetCursor(TrackUnWrapperH prePosition, TrackUnWrapperH curPosition);

        inline float GetDelta() { return mDeltaTime; }

    protected:
        /*  */
        sync::AnimSyncMarkerData mTrackMarkersData;
        // Store data about current marker position when using marker based sync.
        sync::SyncMarkerUpdateRecord mMarkerUpdateRecord;

        mutable TrackUnWrapperH mCurPos = {0};
        mutable TrackUnWrapperH mPrePos = {0};
    
        float mDeltaTime = 0.f;
    };

    /* Instance of track got only segments */
    class Animation_API AnimReferenceDefTrack final : public AnimReferenceTrackBase
    {
    public:
        explicit AnimReferenceDefTrack(AnimTrack const& inTrack)
            : mReference(inTrack)
        {
            mTrackMarkersData.RefreshDataFromAnimTrack(inTrack);
        }

        virtual AdvanceAnim::Type Advance(float deltaTime, bool isLoopAllowed = false) override;

        virtual bool IsCompleted(float inBlendTime = 0.001f, bool isLoopAllowed = false) const override;

        virtual float GetRunLength() const override { return mReference.GetRunLength(); }

        virtual float GetCurSectionedLength() const override { return GetRunLength(); }

        virtual void GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const override;

        virtual void ExtractAnimCurves(AnimCurveData& outAnimCurves) const override;

        inline AnimTrack const& GetReference() const { return mReference; }
        
    private:
        mutable bool mIsComplete = false;

        AnimTrack const& mReference;
    };

}
