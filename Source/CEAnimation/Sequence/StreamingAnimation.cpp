#include "EnginePrefix.h"
#include "CEAnimation/Sequence/StreamingAnimation.h"

namespace cross::anim
{
	/////////////////////////////////////////////
	// LinearReductionStreamableData
	//
	/////////////////////////////////////////////
	bool LinearReductionStreamableData::Deserialize(const CrossSchema::ImportAnimation* inImportAnim)
	{
        if (!inImportAnim->tracks_ani_buffer())
        {
            LOG_ERROR("Animation Deserialize error for track buffer nullptr");
            return false;
        }

        auto& boneTracksBuffer = *(inImportAnim->tracks_ani_buffer());
        Assert(boneTracksBuffer.size() > 0);
        auto boneNum = boneTracksBuffer.size();

        std::vector<Track> tracksForAllBones;
        tracksForAllBones.resize(boneNum);
        // Grab each bone RTS
        for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
        {
            auto& fb_track = *boneTracksBuffer[boneIdx];
            Track track;

            // Grab Translate Keys
            auto& fb_track_pos = *fb_track.pos();
            auto& fb_track_pos_t = *fb_track.pos_t();
            for (int index = 0, size = fb_track_pos.size(); index < size; ++index)
            {
                Float3A elem(fb_track_pos[index]->x(), fb_track_pos[index]->y(), fb_track_pos[index]->z());
                track.TranslationKeys.emplace_back(fb_track_pos_t[index], elem);
            }

            // Grab Rotate Keys
            auto& fb_track_rot = *fb_track.rot();
            auto& fb_track_rot_t = *fb_track.rot_t();
            for (int index = 0, size = fb_track_rot.size(); index < size; ++index)
            {
                QuaternionA elem(fb_track_rot[index]->x(), fb_track_rot[index]->y(), fb_track_rot[index]->z(), fb_track_rot[index]->w());
                track.RotationKeys.emplace_back(fb_track_rot_t[index], elem);
            }

            // Grab Scale Keys
            auto& fb_track_scl = *fb_track.scl();
            auto& fb_track_scl_t = *fb_track.scl_t();
            for (int index = 0, size = fb_track_scl.size(); index < size; ++index)
            {
                Float3A elem(fb_track_scl[index]->x(), fb_track_scl[index]->y(), fb_track_scl[index]->z());
                track.ScaleKeys.emplace_back(fb_track_scl_t[index], elem);
            }

            tracksForAllBones[boneIdx] = std::move(track);
        }


        mTrackForAllBones = tracksForAllBones;


		FillAnimPropertyKeys<Translation>(tracksForAllBones);
		FillAnimPropertyKeys<Rotation>(tracksForAllBones);
		FillAnimPropertyKeys<Scale>(tracksForAllBones);

		return true;
	}


    void LinearReductionStreamableData::Serialize(CrossSchema::ImportAnimationT& csAnim)
    {
        csAnim.cpr_type = CrossSchema::ImportAnimCompressType::LinearReduction;
        
        Assert(mTrackForAllBones.size() == csAnim.skelt_num);

        std::vector<std::unique_ptr<CrossSchema::ImportAnimTrackPerBoneT>> csAnimTracks;
        for (int boneIndex = 0; boneIndex < csAnim.skelt_num; ++boneIndex) 
        {
            std::unique_ptr<CrossSchema::ImportAnimTrackPerBoneT> csAnimTrack(new CrossSchema::ImportAnimTrackPerBoneT);
            auto const& curAnimTrack = mTrackForAllBones[boneIndex];

            // grab translate key frames in particular bone
            auto const& curTransTrack = curAnimTrack.TranslationKeys;
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.TranslationKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& translate = curTransTrack[frameIndex].Data;
                auto& time = curTransTrack[frameIndex].Time;

                csAnimTrack->pos.emplace_back(translate.x, translate.y, translate.z);
                csAnimTrack->pos_t.emplace_back(time);
            }

            // grab rotation key frames in particular bone 
            auto const& curRotTrack = curAnimTrack.RotationKeys;
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.RotationKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& rotation = curRotTrack[frameIndex].Data;
                auto& time = curRotTrack[frameIndex].Time;

                csAnimTrack->rot.emplace_back(rotation.x, rotation.y, rotation.z, rotation.w);
                csAnimTrack->rot_t.emplace_back(time);
            }

            // grab scale key frames in particular bone
            auto const& curSclTrack = curAnimTrack.ScaleKeys;
            for (int frameIndex = 0, frameCount = static_cast<int>(curAnimTrack.ScaleKeys.size()); frameIndex < frameCount; ++frameIndex)
            {
                auto& scale = curSclTrack[frameIndex].Data;
                auto& time = curSclTrack[frameIndex].Time;

                csAnimTrack->scl.emplace_back(scale.x, scale.y, scale.z);
                csAnimTrack->scl_t.emplace_back(time);
            }

            csAnimTracks.emplace_back(std::move(csAnimTrack));
        }

        csAnim.tracks_ani_buffer = std::move(csAnimTracks);
    }

	template<typename T>
	void LinearReductionStreamableData::FillAnimPropertyKeys(const std::vector<Track>& tracksForAllBones)
	{
		const auto boneNum = tracksForAllBones.size();

		if constexpr (std::is_same_v<T, Translation>)
		{
			for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
			{
				auto& currBoneTrack = tracksForAllBones[boneIdx];
				const auto keyNum = currBoneTrack.TranslationKeys.size();

				for (UInt32 keyIdx = 0; keyIdx < keyNum; ++keyIdx)
				{
					TranslationPropKey curKey;
					curKey.BoneIndex = boneIdx;
					curKey.PrevTime = (keyIdx == 0) ? -1.0f : currBoneTrack.TranslationKeys[keyIdx - 1].Time;
					curKey.NextTime = (keyIdx == keyNum - 1) ? FLT_MAX : currBoneTrack.TranslationKeys[keyIdx + 1].Time;
					curKey.Time = currBoneTrack.TranslationKeys[keyIdx].Time;
					auto& rawData = currBoneTrack.TranslationKeys[keyIdx].Data;
					curKey.Value = Float3A(rawData.x, rawData.y, rawData.z);
					mTranslationKeys.emplace_back(curKey);
				}
			}

			std::sort(mTranslationKeys.begin(), mTranslationKeys.end(), &TranslationPropKey::PropertyKeyCmp);

			// do after raw reduced key data being sorted by PrevTime
			for (UInt32 keyIdx = 0; keyIdx < mTranslationKeys.size(); ++keyIdx)
			{
				TranslationBwdKey curKeyBwd;
				curKeyBwd.BoneIndex = mTranslationKeys[keyIdx].BoneIndex;
				curKeyBwd.NextTime = mTranslationKeys[keyIdx].NextTime;
				curKeyBwd.PropertyKeyIndex = keyIdx;

				mTranslationKeysBwd.emplace_back(curKeyBwd);
			}

			std::sort(mTranslationKeysBwd.begin(), mTranslationKeysBwd.end(), &TranslationBwdKey::KeyBwdCmp);
		}

		if constexpr (std::is_same_v<T, Rotation>)
		{
			for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
			{
				auto& currBoneTrack = tracksForAllBones[boneIdx];
				const auto keyNum = currBoneTrack.RotationKeys.size();

				for (UInt32 keyIdx = 0; keyIdx < keyNum; ++keyIdx)
				{
					RotationPropKey curKey;
					curKey.BoneIndex = boneIdx;
					curKey.PrevTime = (keyIdx == 0) ? -1.0f : currBoneTrack.RotationKeys[keyIdx - 1].Time;
					curKey.NextTime = (keyIdx == keyNum - 1) ? FLT_MAX : currBoneTrack.RotationKeys[keyIdx + 1].Time;
					curKey.Time = currBoneTrack.RotationKeys[keyIdx].Time;
					curKey.Value = currBoneTrack.RotationKeys[keyIdx].Data;
					mRotationKeys.emplace_back(curKey);
				}
			}

			std::sort(mRotationKeys.begin(), mRotationKeys.end(), &RotationPropKey::PropertyKeyCmp);

			// do after raw reduced key data being sorted by PrevTime
			for (UInt32 keyIdx = 0; keyIdx < mRotationKeys.size(); ++keyIdx)
			{
				RotationBwdKey curKeyBwd;
				curKeyBwd.BoneIndex = mRotationKeys[keyIdx].BoneIndex;
				curKeyBwd.NextTime = mRotationKeys[keyIdx].NextTime;
				curKeyBwd.PropertyKeyIndex = keyIdx;

				mRotationKeysBwd.emplace_back(curKeyBwd);
			}

			std::sort(mRotationKeysBwd.begin(), mRotationKeysBwd.end(), &RotationBwdKey::KeyBwdCmp);
		}

		if constexpr (std::is_same_v<T, Scale>)
		{
			for (UInt32 boneIdx = 0; boneIdx < boneNum; ++boneIdx)
			{
				auto& currBoneTrack = tracksForAllBones[boneIdx];
				const auto keyNum = currBoneTrack.ScaleKeys.size();

				for (UInt32 keyIdx = 0; keyIdx < keyNum; ++keyIdx)
				{
					ScalePropKey curKey;
					curKey.BoneIndex = boneIdx;
					curKey.PrevTime = (keyIdx == 0) ? -1.0f : currBoneTrack.ScaleKeys[keyIdx - 1].Time;
					curKey.NextTime = (keyIdx == keyNum - 1) ? FLT_MAX : currBoneTrack.ScaleKeys[keyIdx + 1].Time;
					curKey.Time = currBoneTrack.ScaleKeys[keyIdx].Time;
					auto& rawData = currBoneTrack.ScaleKeys[keyIdx].Data;
					curKey.Value = Float3A(rawData.x, rawData.y, rawData.z);
					mScaleKeys.emplace_back(curKey);
				}
			}

			std::sort(mScaleKeys.begin(), mScaleKeys.end(), &ScalePropKey::PropertyKeyCmp);

			// do after raw reduced key data being sorted by PrevTime
			for (UInt32 keyIdx = 0; keyIdx < mScaleKeys.size(); ++keyIdx)
			{
				ScaleBwdKey curKeyBwd;
				curKeyBwd.BoneIndex = mScaleKeys[keyIdx].BoneIndex;
				curKeyBwd.NextTime = mScaleKeys[keyIdx].NextTime;
				curKeyBwd.PropertyKeyIndex = keyIdx;

				mScaleKeysBwd.emplace_back(curKeyBwd);
			}

			std::sort(mScaleKeysBwd.begin(), mScaleKeysBwd.end(), &ScaleBwdKey::KeyBwdCmp);
		}
	}

	/////////////////////////////////////////////
	// UniformSampleStreamableData
	//
	/////////////////////////////////////////////
	UniformSampleStreamableData::~UniformSampleStreamableData()
	{
		// need deallocate cprAnimBuffer
		AnsiAllocator::Instance().deallocate(AclCompressedAnim, AclCompressedAnim->get_size());
	}

	bool UniformSampleStreamableData::Deserialize(const CrossSchema::ImportAnimation* inImportAnim)
	{
		if (!inImportAnim->cpr_ani_buffer())
		{
			LOG_ERROR("Animation Deserialize error for track buffer nullptr");
			return false;
		}

		auto& cprAnimBuffer = *(inImportAnim->cpr_ani_buffer());
		Assert(cprAnimBuffer.size() > 0);

		char* alignBuffer = static_cast<char*>(AnsiAllocator::Instance().allocate(cprAnimBuffer.size(), alignof(CompressedTracks)));
		memcpy(alignBuffer, cprAnimBuffer.data(), cprAnimBuffer.size());
		AclCompressedAnim = reinterpret_cast<CompressedTracks*>(alignBuffer);

		return true;
	}


    
    void UniformSampleStreamableData::Serialize(CrossSchema::ImportAnimationT& csAnim)
    {
        csAnim.cpr_type = CrossSchema::ImportAnimCompressType::UniformSample;

        csAnim.cpr_ani_buffer.resize(AclCompressedAnim->get_size());
        memcpy(csAnim.cpr_ani_buffer.data(), reinterpret_cast<char*>(AclCompressedAnim), sizeof(char) * AclCompressedAnim->get_size());
    }


	/////////////////////////////////////////////
	// StreamingAnimation
	//
	/////////////////////////////////////////////
	bool StreamingAnimation::Deserialize(const CrossSchema::ImportAnimation* inImportAnim)
	{
		if (inImportAnim == nullptr)
		{
			LOG_ERROR("Animation Deserialize error for fb struct nullptr");
			return false;
		}

		if (!RefSkeleton.Deserialize(inImportAnim->ref_skelt()))
		{
			LOG_ERROR("Animation Deserialize error for ref-skelt extract failed");
			return false;
		}

		AssetName = flatbuffers::GetCstring(inImportAnim->name());
		AssetRefPath = flatbuffers::GetCstring(inImportAnim->ref_path());
		DurationInSec = inImportAnim->duration_sec();
		BonesCount = inImportAnim->skelt_num();
		FramesCount = inImportAnim->frame_num();
		CprType = static_cast<StreamingAnimCompressType>(inImportAnim->cpr_type());

		Assert(BonesCount == RefSkeleton.GetRawBoneNum());
		
		if (CprType == LinearKeyReduction)
		{
			auto linearReductionStreamingData = std::make_unique<LinearReductionStreamableData>();
			if (linearReductionStreamingData->Deserialize(inImportAnim))
				StreamableAnimDataPtr = std::move(linearReductionStreamingData);
			else
				return false;
		}

        if (CprType == UniformlySampled)
        {
            auto uniformSampleStreamingData = std::make_unique<UniformSampleStreamableData>();
            if (uniformSampleStreamingData->Deserialize(inImportAnim))
                StreamableAnimDataPtr = std::move(uniformSampleStreamingData);
            else
                return false;
        }

        // deserialize root motion control parameters
        HasRootMotion = inImportAnim->has_rootmotion();
        RootLockType = static_cast<RootMotionRootLock::Type>(inImportAnim->root_lock_type());

        NotifyTrack.Deserialize(inImportAnim->notifies());

        // deserialize sync markers if needed
        if (inImportAnim->sync_markers())
            SyncMarkerTrack.Deserialize(inImportAnim->sync_markers());

        // deserialize additive anim info if needed
        if (inImportAnim->additive_animspace() != CrossSchema::ImportAdditiveSpaceType::None) 
        {
            AdditiveSpace = static_cast<AdditiveAnimSpace::Type>(inImportAnim->additive_animspace());
            BasePoseType = static_cast<AdditiveBasePose::Type>(inImportAnim->additive_basepose());
            BasePoseSeq = flatbuffers::GetCstring(inImportAnim->additive_path());
            BaseFrameIndex = inImportAnim->additive_baseframe();
        }

        if (inImportAnim->curve_set())
        {
            CurveList.Deserialize(inImportAnim->curve_set());
        }

        return true;
    }
}
