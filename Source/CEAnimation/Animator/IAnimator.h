#pragma once
#include "Delegate/CrossDelegate.h"

namespace cross
{
    class IGameWorld;
}

namespace cross::anim {
    class AnimCompositeBase;
    DECLARE_DELEGATE_OneParam(FOnAnimCmpBaseStarted, cross::anim::AnimCompositeBase*)
    DECLARE_DELEGATE_TwoParams(FOnAnimCmpBaseEnded, cross::anim::AnimCompositeBase*, bool /*bInterrupted*/)
    DECLARE_DELEGATE_TwoParams(FOnAnimCmpBaseBlendingOutStarted, cross::anim::AnimCompositeBase*, bool /*bInterrupted*/)
    DECLARE_DELEGATE_OneParam(FOnAnimCmpBaseBlendedInEnded, anim::AnimCompositeBase*)

    /** Helper struct to store a Queued AnimCmpBase Ended event. */
    struct FQueuedAnimCmpBaseEndedEvent
    {
        AnimCompositeBase* AnimCmpBase;
        int AnimCmpBaseInstanceID;
        bool bInterrupted;
        FOnAnimCmpBaseEnded Delegate;

        FQueuedAnimCmpBaseEndedEvent()
            : AnimCmpBaseInstanceID(INDEX_NONE)
            , bInterrupted(false)
        {
        }

        FQueuedAnimCmpBaseEndedEvent(anim::AnimCompositeBase* InAnimCmpBase, int InInstanceID, bool InbInterrupted, FOnAnimCmpBaseEnded InDelegate)
            : AnimCmpBase(InAnimCmpBase)
            , AnimCmpBaseInstanceID(InInstanceID)
            , bInterrupted(InbInterrupted)
            , Delegate(InDelegate)
        {
        }
    };

    /** Helper struct to store a Queued AnimCmpBase BlendingOut event. */
    struct FQueuedAnimCmpBaseBlendingOutEvent
    {
        AnimCompositeBase* AnimCmpBase;
        bool bInterrupted;
        FOnAnimCmpBaseBlendingOutStarted Delegate;

        FQueuedAnimCmpBaseBlendingOutEvent()
            : bInterrupted(false)
        {
        }

        FQueuedAnimCmpBaseBlendingOutEvent(AnimCompositeBase* InAnimCmpBase, bool InbInterrupted, FOnAnimCmpBaseBlendingOutStarted InDelegate)
            : AnimCmpBase(InAnimCmpBase)
            , bInterrupted(InbInterrupted)
            , Delegate(InDelegate)
        {
        }
    };

    struct FQueuedAnimCmpBaseBlendedInEvent
    {
        AnimCompositeBase* AnimCmpBase;
        FOnAnimCmpBaseBlendedInEnded Delegate;

        FQueuedAnimCmpBaseBlendedInEvent(AnimCompositeBase* InAnimCmpBase, FOnAnimCmpBaseBlendedInEnded InDelegate)
            : AnimCmpBase(InAnimCmpBase)
            , Delegate(InDelegate)
        {
        }
    };

    class IAnimator
    {
    public:
        virtual IGameWorld* GetWorld() const = 0;

        /** Queue a AnimCmpBase BlendingOut Event to be triggered. */
        virtual void QueueAnimCmpBaseBlendingOutEvent(const FQueuedAnimCmpBaseBlendingOutEvent& AnimCmpBaseBlendingOutEvent) = 0;

        /** Queue a AnimCmpBase BlendedIn Event to be triggered. */
        virtual void QueueAnimCmpBaseBlendedInEvent(const FQueuedAnimCmpBaseBlendedInEvent& AnimCmpBaseBlendedInEvent) = 0;

        /** Queue a AnimCmpBase Ended Event to be triggered. */
        virtual void QueueAnimCmpBaseEndedEvent(const FQueuedAnimCmpBaseEndedEvent& AnimCmpBaseEndedEvent) = 0;

    };
}