
#pragma once

#include <initializer_list>
#include <vector>
#include "reflection/config.hpp"
#include "reflection/objects/value.hpp"

namespace gbf {
namespace reflection {

////class Value;

/**
 * \brief Wrapper for packing an arbitrary number of arguments into a single object
 *
 * reflection::Args is defined as a list of arguments of any type (wrapped in reflection::Value
 * instances), which can be passed to all the Ponder entities which may need
 * an arbitrary number of arguments in a uniform way.
 *
 * Arguments lists can be constructed on the fly:
 *
 * \code
 * reflection::Args args(1, true, "hello", 5.24, &myObject);
 * \endcode
 *
 * or appended one by one using the + and += operators:
 *
 * \code
 * reflection::Args args;
 * args += 1;
 * args += true;
 * args += "hello";
 * args += 5.24;
 * args = args + myObject;
 * \endcode
 *
 */
class GBF_REFLECTION_API Args {
 public:
  Args() {}

  Args(const Args& other) { 
    values_ = other.values_;
  }

  Args(Args&& other) { 
    values_.swap(other.values_);
  }
  /////**
  //// * \brief Construct the list with variable arguments.
  //// *
  //// * \param args Parameter pack to be used.
  //// */
  ////template <typename... V>
  ////Args(V&&... args) : Args(std::initializer_list<Value>({std::forward<V>(args)...})) {}

  /**
   * \brief Initialise the list with an initialisation list.
   *
   * \param il Arguments to put in the list.
   */
  Args(std::initializer_list<Value> il) { values_ = il; }

  ~Args() {}

  /**
   * \brief Return the number of arguments contained in the list
   *
   * \return Size of the arguments list
   */
  size_t GetCount() const;

  /**
   * \brief Overload of operator [] to access an argument from its index
   *
   * \param index Index of the argument to get
   * \return Value of the index-th argument
   * \throw OutOfRange index is out of range
   */
  const Value& operator[](size_t index) const;

  /**
   * \brief Overload of operator + to concatenate a list and a new argument
   *
   * \param arg Argument to concatenate to the list
   * \return New list
   */
  Args operator+(const Value& arg) const;

  /**
   * \brief Overload of operator += to append a new argument to the list
   *
   * \param arg Argument to append to the list
   * \return Reference to this
   */
  Args& operator+=(const Value& arg);

  /**
   * \brief Insert an argument into the list at a given index
   *
   * \param index Index at which to insert the argument
   * \param arg Argument to append to the list
   * \return Reference to this
   */
  Args& Insert(size_t index, const Value& arg);

  Value Pop();

  void Resize(size_t total);

  void Clear() { values_.clear(); }
 public:
  /**
   * \brief Special instance representing an empty set of arguments
   */
  static const Args empty;

 private:
  std::vector<Value> values_;  // List of the values
};

}  // namespace reflection
}  // namespace gbf
