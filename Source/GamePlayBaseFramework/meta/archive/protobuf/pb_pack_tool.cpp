#include "archive/protobuf/pb_pack_tool.hpp"

#include "archive/protobuf/pb_common.h"
#include "archive/protobuf/pb_decode.h"
#include "archive/protobuf/pb_encode.h"
#include "reflection/meta/meta_class.hpp"
////#include "reflection/meta/cpp/meta_class_clang.h"

#include "archive/coder/coder_lua_table.h"
#include "archive/coder/coder_xml.h"

namespace protobuf {
PackResult LurapbPackTool::PackToArray(const PbMsgDesc* metaMessage, const void* pMessage, void* data, int size) {
  protobuf::PackResult ret;
  protobuf::PbOStream stream = protobuf::PbOstreamFromBuffer((protobuf::pb_byte_t*)data, size);
  ret.issuc = protobuf::PbEncode(&stream, metaMessage, pMessage);
  ret.bytes_written = stream.bytes_written;
  ret.errmsg = stream.errmsg;
  return ret;
}

UnpackResult LurapbPackTool::UnpackFromArray(const PbMsgDesc* metaMessage, void* pMessage, const void* data,
                                             int size) {
  protobuf::UnpackResult ret;
  protobuf::PbIStream istream = protobuf::PbIStreamFromBuffer((const protobuf::pb_byte_t*)data, size);
  ret.issuc = protobuf::PbDecode(&istream, metaMessage, pMessage);
  ret.errmsg = istream.errmsg;
  ret.bytes_left = istream.bytes_left;
  return ret;
}

PackResult LurapbPackTool::PackToBuffer(const PbMsgDesc* metaMessage, const void* pMessage,
                                        gbf::ByteBufferWriter& buf) {
  auto ret = PackToArray(metaMessage, pMessage, (protobuf::pb_byte_t*)buf.WritePtr(), buf.Space());
  buf.WritePosition(buf.WritePosition() + ret.bytes_written);
  return ret;
}

UnpackResult LurapbPackTool::UnpackFromBuffer(const PbMsgDesc* metaMessage, void* pMessage,
                                              gbf::ByteBufferView buf) {
  size_t total = buf.Size();
  auto ret = UnpackFromArray(metaMessage, pMessage, buf.Contents(), buf.Size());
  buf.ReadPosition(total - ret.bytes_left);
  return ret;
}

std::string LurapbPackTool::PackToHexString(const PbMsgDesc* metaMessage, const void* pMessage,
                                            size_t capacity /*= 100 * 1024*/) {
  gbf::ByteBufferWriter buf(capacity);
  auto ret = PackToArray(metaMessage, pMessage, buf.WritePtr(), buf.Space());
  buf.WritePosition(ret.bytes_written);

  return buf.CreateView().GetAsHexString();
}

UnpackResult LurapbPackTool::UnpackFromHexString(const PbMsgDesc* metaMessage, void* pMessage,
                                                 const std::string_view hexstr) {
  gbf::ByteBufferWriter buf;
  buf.SetAsHexString(hexstr);

  return UnpackFromBuffer(metaMessage, pMessage, buf.CreateView());
}

std::string LurapbPackTool::PackToLuaCode(const PbMsgDesc* metaMessage, const void* pMessage) {
  const auto* metaClass = static_cast<const gbf::reflection::MetaClass*>(metaMessage->extra_reflection_data);
  auto uo = metaClass->GetUserObjectFromPointer(const_cast<void*>(pMessage));
  return gbf::reflection::CoderLuaTable::WriteUserObjectToLuaCode(uo);
}

std::string LurapbPackTool::PackToXml(const PbMsgDesc* metaMessage, const void* pMessage, bool compact, int depth) {
  const auto* metaClass = static_cast<const gbf::reflection::MetaClass*>(metaMessage->extra_reflection_data);
  auto uo = metaClass->GetUserObjectFromPointer(const_cast<void*>(pMessage));
  return gbf::reflection::CoderXml::WriteValueToXmlString(make_value(uo), metaClass->name(), compact, depth);
}

////bool LurapbPackTool::UnpackFromXml(const PbMsgDesc* metaMessage, void* pMessage, const std::string_view xmlstr)
////{
////	const auto* metaClass = static_cast<const gbf::reflection::Class*>(metaMessage->extra_reflection_data);
////	auto uo = metaClass->getUserObjectFromPointer(const_cast<void*>(pMessage));
////	return gbf::reflection::CoderXml::ReadValueFromXmlString(xmlstr, uo);
////}

}  // namespace protobuf
