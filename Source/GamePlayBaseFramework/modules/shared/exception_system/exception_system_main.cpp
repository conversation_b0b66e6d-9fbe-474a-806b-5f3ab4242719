#include "exception_system/exception_system_main.h"
#include "core/modules/iinterface_manager.h"
#include "exception_system/exception_system_module.h"
#include "imod_shared/shared_global.hpp"

extern "C" {
gbf::IModule* DynlibCreateModule(gbf::IInterfaceMgr* interfaceMgr) {
  auto* tmodule = new gbf::ExceptionSystemModule();
  GExceptionSystem = tmodule;
  if (interfaceMgr) {
    interfaceMgr->RegisterModule(gbf::kModuleExceptionSystemName, tmodule);
  }

  return tmodule;
}

void DynlibDestroyModule(gbf::IModule* tmodule) {}
}
