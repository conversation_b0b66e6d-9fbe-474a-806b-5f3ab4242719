#pragma once

#include <cstdint>
#include <cstddef>
#include "core/export.hpp"

namespace gbf {

class GBF_CORE_API bitutil {
 public:
  static size_t bits_count(std::uint32_t value);
  static size_t first_valid_bit_from_low(std::uint32_t value);
  static size_t first_valid_bit_from_high(std::uint32_t value);

#if GBF_CORE_ARCH_TYPE == GBF_CORE_ARCHITECTURE_64
  static size_t bits_count(std::uint64_t value);
  static size_t first_valid_bit_from_low(std::uint64_t value);
  static size_t first_valid_bit_from_high(std::uint64_t value);
#endif
};

}  // namespace gbf
