#pragma once

#include <cstring>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "core/config.hpp"
#include "core/modules/imodule.h"

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  #define strcasecmp _stricmp
#endif

enum LOG_LEVEL {
  LL_TRACEDETAIL = 0,
  LL_TRACE,
  LL_DEBUG,
  LL_INFO,
  LL_WARN,
  LL_ERROR,
  LL_FATAL,
  LL_ANY,
  LL_NOLOG,
};

enum LOG_MODULE {
  LOG_MODULE_NONE = 0,
  LOG_MODULE_STATISTIC,
  LOG_MODULE_PHYSX,
  LOG_MODULE_LUA,
  LOG_MODULE_PYTHON,
  LOG_MODULE_V8,
  LOG_MODULE_MOVE,
  LOG_MODULE_MAINLAND,
  LOG_<PERSON>ODULE_MAP,
  LOG_MODULE_CONNECTION,
  LOG_MODULE_DEBUG,
  LOG_MODULE_TASK,
  LOG_MODULE_NAVMESH,
  LOG_MODULE_STARTING,
  LOG_MODULE_GM,
  LOG_MODULE_COROUTINE_STACKLESS,
  LOG_MODULE_LOGIN,
  LOG_MODULE_MATCH,
  LOG_MODULE_TIME_TRIGGER,
  LOG_MODULE_PAY,
  LOG_MODULE_HOUSE,
  LOG_MODULE_SVR,
  LOG_MODULE_TASKNPCSHOW,
  LOG_MODULE_SVR_RECOVER,
  LOG_MODULE_AI,
  LOG_MODULE_BTREE,
  LOG_MODULE_REBOUND,
  LOG_MODULE_ROBOT,
  LOG_MODULE_ROUTER_ENTITY,
  LOG_MODULE_RELFECTION,
  LOG_MODULE_RESOURCE,
  LOG_MODULE_MAX,
};

enum class LOG_CHANNEL : int {
  LOG_CHANNEL_NORMAL = 0,
  LOG_CHANNEL_EXTRA1 = 1,
  LOG_CHANNEL_EXTRA2 = 2,
  LOG_CHANNEL_TOTAL_DEFINES,
};
using CrossEngineLogDelegate = void(*)(int, char const*);
namespace gbf {

// 日志初始化参数
class LogInitParm {
 public:
  LogInitParm() {}

  // 快速设置初始化参数，兼容已有逻辑
  LogInitParm(LOG_LEVEL level, bool is_reload, bool console_output, const std::string& log_path,
              const std::string& normal_logfile, const std::string& extra1_logfile, const std::string& extra2_logfile) {
    is_reload_ = is_reload;

    // LOG_CHANNEL::LOG_CHANNEL_NORMAL
    {
      ChannelCfg normal_channel_cfg;
      normal_channel_cfg.level = level;

      WriterCfg writer;
      writer.type = "RotateFileWriter";
      writer.cfg.emplace("path", log_path);
      writer.cfg.emplace("filename", normal_logfile);
      writer.cfg.emplace("max_file_size_m", "200");
      writer.cfg.emplace("rotate_type", "index");
      normal_channel_cfg.writers.emplace(writer.type, std::move(writer));

      if (console_output) {
        WriterCfg writer2;
        writer2.type = "ConsoleWriter";
        writer2.cfg.emplace("color", "true");
        normal_channel_cfg.writers.emplace(writer2.type, std::move(writer2));
      }

      channel_cfg_.emplace(LOG_CHANNEL::LOG_CHANNEL_NORMAL, std::move(normal_channel_cfg));
    }

    // LOG_CHANNEL::LOG_CHANNEL_EXTRA1
    if (extra1_logfile.size())
    {
      ChannelCfg thread_channel_cfg;
      thread_channel_cfg.level = level;

      WriterCfg writer;
      writer.type = "RotateFileWriter";
      writer.cfg.emplace("path", log_path);
      writer.cfg.emplace("filename", extra1_logfile);
      writer.cfg.emplace("max_file_size_m", "200");
      writer.cfg.emplace("rotate_type", "index");
      thread_channel_cfg.writers.emplace(writer.type, std::move(writer));

      channel_cfg_.emplace(LOG_CHANNEL::LOG_CHANNEL_EXTRA1, std::move(thread_channel_cfg));
    }

    // LOG_CHANNEL::LOG_CHANNEL_EXTRA2
    if (extra2_logfile.size())
    {
      ChannelCfg statistic_channel_cfg;
      statistic_channel_cfg.level = LL_TRACEDETAIL;

      WriterCfg writer;
      writer.type = "RotateFileWriter";
      writer.cfg.emplace("path", log_path);
      writer.cfg.emplace("filename", extra2_logfile);
      writer.cfg.emplace("max_file_size_m", "200");
      writer.cfg.emplace("rotate_type", "index");
      statistic_channel_cfg.writers.emplace(writer.type, std::move(writer));

      channel_cfg_.emplace(LOG_CHANNEL::LOG_CHANNEL_EXTRA2, std::move(statistic_channel_cfg));
    }
  }

 public:
  bool is_reload_ = false;

  struct WriterCfg {
    std::string type;
    std::map<std::string, std::string> cfg;
  };

  struct ChannelCfg {
    LOG_LEVEL level = LL_TRACEDETAIL;
    std::map<LOG_MODULE, LOG_LEVEL> module_level;
    std::map<std::string, WriterCfg> writers;
  };

  std::map<LOG_CHANNEL, ChannelCfg> channel_cfg_;
};

}  // namespace gbf

namespace gbf {

static const char kModuleLogName[] = "LogModule";

using LogCtx = std::map<std::string, std::string>;
using LogCtxPtr = std::shared_ptr<LogCtx>;

struct LogData {
  int mod;
  int lvl;
  int thread_id;
  ////const char* svr_id;
  std::string time;
  LogCtxPtr ctx;
  std::string msg;
};

using LogWriteFunction = std::function<void(const LogData&)>;

class ILogModule : public IModule {
 public:
  // method from IModule
  ModuleCallReturnStatus Init() override = 0;

  ModuleCallReturnStatus Start() override = 0;

  ModuleCallReturnStatus Update() override = 0;

  ModuleCallReturnStatus Stop() override = 0;

  ModuleCallReturnStatus Release() override = 0;

  void Free() override = 0;

 public:
  virtual void SetEditorLogDelegate(CrossEngineLogDelegate LogDelegate) = 0;
  virtual void SetInitParm(std::shared_ptr<LogInitParm> parm) = 0;

  virtual int Trace(LOG_CHANNEL logChannel, LOG_MODULE logModule, LOG_LEVEL logLevel, const char* pszFmt, ...) = 0;

  virtual int TraceWithString(LOG_CHANNEL logChannel, LOG_MODULE logModule, LOG_LEVEL logLevel, std::string& content) = 0;

  virtual int TraceWithCtx(LOG_CHANNEL logChannel, LOG_MODULE logModule, LOG_LEVEL logLevel, const LogCtxPtr& ctx,
                           const char* pszFmt, ...) = 0;

  virtual void AddChannelWriter(LOG_CHANNEL logChannel, LogWriteFunction&& func) = 0;

  virtual void SetChannelLogLevel(LOG_CHANNEL logChannel, LOG_LEVEL logLevel) = 0;

  virtual LOG_LEVEL GetChannelLogLevel(LOG_CHANNEL logChannel) const = 0;

  virtual void SetChannelModuleLogLevel(LOG_CHANNEL logChannel, LOG_MODULE modid, LOG_LEVEL logLevel) = 0;

  virtual LOG_LEVEL GetChannelModuleLogLevel(LOG_CHANNEL logChannel, LOG_MODULE modid) const = 0;

  virtual void PushLogContextInfo(const LogCtxPtr& ctx) = 0;

  virtual void PopLogContextInfo() = 0;

  virtual void FlushChannel(LOG_CHANNEL log_channel) = 0;
  
  virtual void FlushAllChannels() = 0;

  static const char** GetLogLevelNameArray() {
    static const char* arr[] = {
        "TRACEDETAIL",  // LL_TRACEDETAIL,
        "TRACE",        // LL_TRACE,
        "DEBUG",        // LL_DEBUG,
        "INFO",         // LL_INFO,
        "WARN",         // LL_WARN,
        "ERROR",        // LL_ERROR,
        "FATAL",        // LL_FATAL,
        "ANY",          // LL_ANY,
        "None",         // LL_NOLOG,
    };
    static_assert(sizeof(arr) / sizeof(arr[0]) == LL_NOLOG + 1, "");
    return arr;
  }
  static const char* GetLogLevelName(LOG_LEVEL n) { return GetLogLevelNameArray()[n]; }
  static LOG_LEVEL GetLogLevelFromName(const char* name) {
    const char** arr = GetLogLevelNameArray();
    for (int i = 0; i <= LL_NOLOG; ++i) {
      if (strcasecmp(name, arr[i]) == 0) return (LOG_LEVEL)i;
    }
    return LL_NOLOG;
  }

  static const char** GetLogModuleNameArray() {
    static const char* arr[] = {
        "",              // LOG_MODULE_NONE
        "statistic",     // LOG_MODULE_STATISTIC,
        "physx",         // LOG_MODULE_PHYSX,
        "lua",           // LOG_MODULE_LUA,
        "python",        // LOG_MODULE_PYTHON,
        "v8",            // LOG_MODULE_V8,
        "move",          // LOG_MODULE_MOVE,
        "mainland",      // LOG_MODULE_MAINLAND,
        "map",           // LOG_MODULE_MAP,
        "connection",    // LOG_MODULE_CONNECTION,
        "debug",         // LOG_MODULE_DEBUG,
        "task",          // LOG_MODULE_TASK,
        "navmesh",       // LOG_MODULE_NAVMESH,
        "starting",      // LOG_MODULE_STARTING,
        "gm",            // LOG_MODULE_GM,
        "costackless",   // LOG_MODULE_COROUTINE_STACKLESS,
        "login",         // LOG_MODULE_LOGIN,
        "match",         // LOG_MODULE_MATCH,
        "time_trigger",  // LOG_MODULE_TIME_TRIGGER,
        "pay",           // LOG_MODULE_PAY,
        "house",         // LOG_MODULE_HOUSE,
        "server",        // LOG_MODULE_SVR,
        "tasknpcshow",   // LOG_MODULE_TASKNPCSHOW,
        "svrrecover",    // LOG_MODULE_SVR_RECOVER,
        "ai",            // LOG_MODULE_AI,
        "btree",         // LOG_MODULE_BTREE,
        "msgrebound",    // LOG_MODULE_REBOUND,
        "robot",         // LOG_MODULE_ROBOT,
        "router_entity", // LOG_MODULE_ROUTER_ENTITY,
        "reflection",    // LOG_MODULE_RELFECTION,
        "resource",      // LOG_MODULE_RESOURCE,
        "max",           // LOG_MODULE_MAX,
    };
    static_assert(sizeof(arr) / sizeof(arr[0]) == LOG_MODULE_MAX + 1, "");
    return arr;
  }
  static const char* GetLogModuleName(LOG_MODULE n) { return GetLogModuleNameArray()[n]; }
  static LOG_MODULE GetLogModuleFromName(const char* name) {
    const char** arr = GetLogModuleNameArray();
    for (int i = 0; i <= LOG_MODULE_MAX; ++i) {
      if (strcasecmp(name, arr[i]) == 0) return (LOG_MODULE)i;
    }
    return LOG_MODULE_MAX;
  }
};

// LogCtx guard
class LogCtxGuard {
 public:
  LogCtxGuard(ILogModule* p, const LogCtxPtr& ctx) : plm(p) { plm->PushLogContextInfo(ctx); }

  ~LogCtxGuard() { plm->PopLogContextInfo(); }

 private:
  ILogModule* plm;
};

}  // namespace gbf

#include "core/imodules/ilog_module.inl"
