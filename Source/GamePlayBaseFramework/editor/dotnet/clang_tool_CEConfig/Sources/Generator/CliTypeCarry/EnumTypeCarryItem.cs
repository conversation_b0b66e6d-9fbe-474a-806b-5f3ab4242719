using Clangen.Model;
using System;

namespace Clangen
{
    public class EnumTypeCarrayItem : CppAst.General.TypeCarryItem
    {
        public EnumTypeCarrayItem()
        {
            mNativeTypeName = "enum";
        }

        public override string ToTypeName(TypeConverterType convType, CppAst.General.TypeContentOffer typeContent)
        {
            EnumModel edrop;
            var fullname = Clangen.NamespaceTool.CppNamespaceToCookNamespace("Cli" + typeContent.FullCookName);
            CliGenerator.mCollectEnumNodeMap.TryGetValue(fullname, out edrop);
            switch (convType)
            {
                case TypeConverterType.BridgeCliInput:
                case TypeConverterType.BridgeCliReturn:
                    if (edrop != null)
                    {
                        return Clangen.NamespaceTool.CookNamespaceToCppNamespace(fullname);
                    }
                    else
                    {
                        return "int";
                    }

                    return "int";
            }
            throw new NotImplementedException();
        }
        public override string DoCallCovert(CallConverterType callConvType, string callExpr, CppAst.General.TypeContentOffer typeContent)
        {
            if (callConvType == CallConverterType.Bridge2Native)
            {
                return string.Format("static_cast<{0}>({1})", typeContent.FullCppName, callExpr);
            }
            else if (callConvType == CallConverterType.Native2Bridge)
            {
                EnumModel enumdrop;
                var fullname = Clangen.NamespaceTool.CppNamespaceToCookNamespace("Cli" + typeContent.FullCookName);
                CliGenerator.mCollectEnumNodeMap.TryGetValue(fullname, out enumdrop);
                if (enumdrop != null)
                {
                    return string.Format("({1})((int){0})", callExpr, Clangen.NamespaceTool.CookNamespaceToCppNamespace(fullname));
                }
                else
                {
                    return string.Format("((int){0})", callExpr);
                }
            }
            ////else if(callConvType == CallConverterType.CsApi2Bridge)
            ////{
            ////    return string.Format("(int){0}", callExpr);
            ////}
            ////else if(callConvType == CallConverterType.CsBridge2Api)
            ////{
            ////    var enumdrop = Exporter.Instance.TryGetCollectEnumDrop(typeContent.FullCookName);
            ////    if(enumdrop != null)
            ////    {
            ////        return string.Format("({0}){1}", enumdrop.ExportCookName, callExpr);
            ////    }
            ////}

            return callExpr;
        }

        public override bool IsValidForExport(CppAst.General.TypeContentOffer typeContent)
        {
            return true;
        }

        public override string DoAssignment(CppAst.General.TypeCarryItem.CallConverterType callType, string leftExpr, string rightExpr, CppAst.General.TypeContentOffer typeContent)
        {

            CppAst.General.EnumTypeContentOffer stringTypeOffer = typeContent as CppAst.General.EnumTypeContentOffer;

            if (callType == CallConverterType.Bridge2Native)
            {
                return string.Format("{0} = static_cast<{2}>({1})", leftExpr, rightExpr, typeContent.FullCppName);
            }


            return base.DoAssignment(callType, leftExpr, rightExpr, typeContent);
        }
    }
}
