using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;


using AI.Model.Json.Chat;
using AI.Model.Services;
using AI.Services;
using ChatGPT.Model.Services;
using ChatGPT.Services;
using ChatGPT.ViewModels.Chat;
using ChatGPT.ViewModels.Settings;
using CommunityToolkit.Mvvm.DependencyInjection;
using CppAst;
using Microsoft.Extensions.DependencyInjection;

namespace Clangen
{
    public class ChatGptInstance
    {
        public ChatSettingsViewModel ChatSettings { get; private set; }
        public ChatViewModel ChatInstance { get; private set; }
        public int TimeoutMs { get; private set; }
        public string MainDirection { get; set; }

        ////public List<ChatMessage> MessageList { get; private set; } = new List<ChatMessage>();

        public List<string> ExtraDirectionList { get; private set; } = new List<string>();

        public List<string> AssistMessageList { get; private set; } = new List<string>();

        public static ChatGptInstance Instance { get; private set; }

        static ChatGptInstance()
        {
            ConfigureServices();
        }

        public ChatGptInstance()
        {
            Debug.Assert(Instance == null);

            Instance = this;
        }


        public bool Init(string token, int timeoutMs = 5000)
        {
            if (ChatInstance != null) return false;
            TimeoutMs = timeoutMs;

            ChatSettings = new ChatSettingsViewModel
            {
                Temperature = 0.7m,
                TopP = 1m,
                MaxTokens = 2000,
                Model = "gpt-3.5-turbo",
                Directions = "",
                ApiKey = token,
            };

            ChatInstance = new ChatViewModel
            {
                Settings = ChatSettings,
            };

            return true;
        }

        public void ResetChatContents()
        {
            ChatInstance = new ChatViewModel
            {
                Settings = ChatSettings,
            };
            ChatInstance.AddSystemMessage(ChatSettings.Directions);
        }

        public string ChatToAi(string input, bool addResponseToAssistant)
        {
            if (ChatInstance == null)
            {
                Log.Error("Please init the ChatGpt first!");
                return "";
            }

            Log.Info($"Chat input: {input}");
            Log.Info("-----------------------------------------------------");
            Log.Info("Wating...");

            try
            {
                List<ChatMessage> messages = new List<ChatMessage>();
                messages.Add(new ChatMessage {
                    Role = "system",
                    Content = MainDirection,
                });

                foreach(var extraDirection in ExtraDirectionList) {
                    messages.Add(new ChatMessage
                    {
                       Role = "system",
                       Content = extraDirection,
                    });
                }

                foreach(var assist in AssistMessageList)
                {
                    messages.Add(new ChatMessage
                    {
                        Role = "assistant",
                        Content = assist,
                    });
                }

                messages.Add(new ChatMessage
                {
                    Role = "user",
                    Content = input,
                });

                using var cts = new CancellationTokenSource();
                var task = ChatInstance.SendAsync(messages.ToArray(), cts.Token);

                task.Wait(TimeoutMs);
                var result = task.Result;

                if (addResponseToAssistant)
                {
                    ChatInstance.AddAssistantMessage(result?.Message);
                }

                Log.Info("-----------------------------------------------------");
                Log.Info($"Chat response: {result?.Message}");
                return result?.Message;
            }
            catch (Exception ex)
            {
                Log.Error("ChatGpt Error: " + ex.Message);
            }

            return "";
        }


        static void ConfigureServices()
        {
            IServiceCollection serviceCollection = new ServiceCollection();

            serviceCollection.AddSingleton<IStorageFactory, IsolatedStorageFactory>();
            serviceCollection.AddSingleton<IChatService, ChatService>();

            serviceCollection.AddTransient<ChatMessageViewModel>();
            serviceCollection.AddTransient<ChatSettingsViewModel>();
            serviceCollection.AddTransient<ChatResultViewModel>();
            serviceCollection.AddTransient<ChatViewModel>();
            serviceCollection.AddTransient<PromptViewModel>();

            Ioc.Default.ConfigureServices(serviceCollection.BuildServiceProvider());
        }
    }
}

