//constructor export here
{%- if this_class.is_override -%}
    {%- if this_class.has_constructor -%}
        {%- for sig in this_class.constructor.signatures -%}
{{ sig.render_bridge_body }}
        {%- endfor -%}
    {%- elsif this_class.has_default_constructor -%}
{{ this_class.no_namespace_name }}::{{ this_class.unscoped_name }}(): {{ this_class.no_namespace_name }}(new {{this_class.override_delegate_name}}(this), true) {}
    {%- endif -%}
{%- else -%}
    {%- unless this_class.is_abstract -%}
        {%- if this_class.has_constructor -%}
            {%- for sig in this_class.constructor.signatures -%}
{{ sig.render_bridge_body }}
            {%- endfor -%}
        {%- elsif this_class.has_default_constructor -%}
{{ this_class.no_namespace_name }}::{{ this_class.unscoped_name }}(): {{ this_class.no_namespace_name }}(new {{this_class.name}}(), true) {}
        {%- endif -%}
    {%- endunless -%}
{%- endif -%}
