#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/extend/btree/node/attachment/btree_attachment.h"

#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"

namespace gbf {
namespace logic {
class BLUEPRINT_API UBtreeEffector : public UBtreeAttachment {
  using base = UBtreeAttachment;
 public:
  // Nested Types

 public:
  UBtreeEffector();
  ~UBtreeEffector();

  BTEffectPhaseType get_phase_type() const noexcept { return m_phase_type; }
  void set_phase_type(BTEffectPhaseType phase_type) { m_phase_type = phase_type; }


  bool IsNeedWork(BTEffectPhaseType phase_type) const noexcept;

  static UBtreeEffectorPtr CreateFromJson(machine::IVMStreamReadNode& node);
  void SerializeToJson(machine::IVMStreamWriter& writer) override;
 protected:
  void DeserializeFromJson(machine::IVMStreamReadNode& node) override;

 protected:
  BTEffectPhaseType m_phase_type = BTEffectPhaseType::OnSuccess;
 public:  
  static void __meta_auto_register() {
    __register_cxx_type<UBtreeEffector>()
        .base<UBtreeAttachment>()
        .constructor<>();
  }         
};
}  // namespace logic
}  // namespace gbf
