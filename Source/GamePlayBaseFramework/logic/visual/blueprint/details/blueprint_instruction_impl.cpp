#include "visual/blueprint/details/blueprint_instruction_impl.h"

#include "visual/blueprint/details/node/blueprint_action_node.h"
#include "visual/blueprint/details/node/blueprint_event_node.h"
#include "visual/blueprint/details/node/blueprint_exec_slot.h"
#include "visual/virtual_machine/runtime/run_scope.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf { namespace logic {
    NGInstructionImpl::NGInstructionImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot_)
        : IInstruction(coroutine_->GetTopScope().get())
        , m_slot(slot_)
    {}

    NGInstructionImpl::~NGInstructionImpl() {}

    machine::VMRunStepStatus NGInstructionImpl::RunImpl(machine::VCoroutine* coroutine_)
    {
        auto* action_node = (UBlueprintActionNode*)(m_slot->node());
        UBlueprintActionNode::ProcessingInfo info = action_node->RtActivate(coroutine_, m_slot);
        // ToDo: add error info here
        // node.ErrorMessage = info.ErrorMessage;

        if (info.State == UBlueprintActionNode::LogicState::Error)
        {
            ERR_DEF("NodeGraphVM: %s \n", info.ErrorMessage.c_str());
            return machine::VMRunStepStatus::Error;
        }
        else if (info.State == UBlueprintActionNode::LogicState::Warning)
        {
            WRN_DEF("NodeGraphVM: %s \n", info.ErrorMessage.c_str());
            return machine::VMRunStepStatus::Succeed;
        }
        else
        {
            return machine::VMRunStepStatus::Succeed;
        }
    }

    NGEventInstruction::NGEventInstruction(machine::VCoroutine* coroutine, UBlueprintEventNode* node, const BlueprintEventParamList& param_list, size_t activeSlot)
        : IInstruction(coroutine->GetTopScope().get())
        , mEventNode(node)
        , mEventParam(param_list)
        , mActiveSlot(activeSlot)
    {}

    machine::VMRunStepStatus NGEventInstruction::RunImpl(machine::VCoroutine* coroutine)
    {
        mEventNode->RtTriggered(coroutine, mActiveSlot, mEventParam);
        return machine::VMRunStepStatus::Succeed;
    }

}}   // namespace gbf::logic
