#pragma once

#include <functional>
#include <map>
#include <unordered_map>
#include "runtime_resource/runtime_resource_fwd.hpp"

namespace gbf {
namespace logic {

struct ProfilerResInfoDetail;

class RUNTIME_RESOURCE_API RuntimeResourceManager {
 public:
  using VisitRuntimeResourceFunction = std::function<void(URuntimeResource*)>;
  using ResourceMap = std::unordered_map<std::string, RuntimeResourcePtr>;

 public:
  RuntimeResourceManager();
  ~RuntimeResourceManager();

 public:
  //@ Create function, Only called from main thread.
  RuntimeResourcePtr CreateManual(std::string_view resName, std::string_view resGroup, RuntimeResourceType hint_type = RuntimeResourceType::Unknown,
                                  RuntimeResourceKeepMode keepMode = RuntimeResourceKeepMode::Reference);

  RuntimeResourcePtr CreateOrRetrieve(std::string_view resName, std::string_view resGroup,
                                      RuntimeResourceType hint_type = RuntimeResourceType::Unknown,
                                      RuntimeResourceKeepMode keepMode = RuntimeResourceKeepMode::Reference);

  ////RuntimeResourcePtr CreateFromStream(ByteBufferPtr data_stream, RuntimeResourceType hint_type, RuntimeResourceKeepMode keep_mode);

  RuntimeResourcePtr GetByName(std::string_view resName);
  int GetResCount();

  void Remove(URuntimeResource* rawPtr);
  void RemoveAll();
  void RemoveUnusedResources();
  void Unload(std::string_view resName);
  void UnloadAll();

  // virtual bool Reload(std::string_view path, bool background);
  void VisitAllItems(VisitRuntimeResourceFunction&& visitFunc) const;

  double getLoadingOrder(void) const { return loading_order_value_; }

  virtual void OutputStatusToLog();

  uint32_t GetCahcedPeriod() const { return cached_period_ms_; }
  void SetCachedPeriod(uint32_t cachedPeriod) { cached_period_ms_ = cachedPeriod; }

  void Update();

  void AddResourceExternalCreate(RuntimeResourcePtr res) { AddResource(res); }

  bool RegisterRuntimeResource(RuntimeResourceType res_type, RuntimeResourceCreator&& creator);

  static std::string_view ResourceTypeToExtensionName(RuntimeResourceType res_type);
  static RuntimeResourceType ExtensionNameToResourceType(std::string_view ext_name);
 protected:
  // @ Only for Internal create function. it can be called from work thread.
  RuntimeResourcePtr CreateImpl(std::string_view resName, std::string_view resGroup, RuntimeResourceKeepMode keepMode, RuntimeResourceType hint_type);

  void AddResource(RuntimeResourcePtr res);
  void RemoveImpl(URuntimeResource* rawPtr);

  void DoResourceCheck(uint32_t frameTime, bool forceRemove);
  void _AddToDelayRemovedMap(uint32_t removeTime, const RuntimeResourcePtr& fileResPtr);
  virtual void DoCustomUpdate(){};

 protected:
  // @ Keep Error files
  ResourceMap error_resource_map_;

  double loading_order_value_ = 0.0;

  // @ For archive cache operation
  uint32_t round_extra_time_ms_ = 0;
  uint32_t next_check_time_ms_ = 0;
  uint32_t cached_period_ms_ = 10000;  // ms

  using ResourceWeakedNameMap = std::unordered_map<std::string, RuntimeResourceWeakPtr>;
  ResourceWeakedNameMap weak_named_map_;

  using ResourceHandleMap = std::unordered_map<uint64_t, RuntimeResourcePtr>;
  ResourceHandleMap cached_map_;
  ResourceHandleMap always_alive_map_;

  using DelayHandledWeakMap = std::multimap<uint32_t, RuntimeResourceWeakPtr>;
  DelayHandledWeakMap delay_removed_map_;

  using ResourceCreatorMap = std::map<RuntimeResourceType, RuntimeResourceCreator>;
  ResourceCreatorMap resource_creator_map_;
};

}  // namespace logic
}  // namespace gbf
