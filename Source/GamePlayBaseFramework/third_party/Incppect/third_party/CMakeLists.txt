set(OPENSSL_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/OpenSSL/include)
set(OPENSSL_LIBRARIES ${CMAKE_CURRENT_SOURCE_DIR}/OpenSSL/libs/libcrypto-1_1-x64.lib
    ${CMAKE_CURRENT_SOURCE_DIR}/OpenSSL/libs/libssl-1_1-x64.lib)
set(LIBUV_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/libuv/include)
if(ANDROID)
    set(LIBUV_LIBRARIES ${CMAKE_CURRENT_SOURCE_DIR}/libuv/lib/libuv.a)
elseif(WIN32)
    set(LIBUV_LIBRARIES ${CMAKE_CURRENT_SOURCE_DIR}/libuv/lib/libuv-win-x64.lib)
endif()
if (WIN32)
 list(APPEND LIBUV_LIBRARIES
       psapi
       user32
       advapi32
       iphlpapi
       userenv
       ws2_32)
endif()
add_library(uWS SHARED
        uWebSockets/uSockets/src/context.c
        uWebSockets/uSockets/src/loop.c
        uWebSockets/uSockets/src/socket.c
        uWebSockets/uSockets/src/crypto/openssl.c
        uWebSockets/uSockets/src/eventing/epoll_kqueue.c
        uWebSockets/uSockets/src/eventing/gcd.c
        uWebSockets/uSockets/src/eventing/libuv.c
        uWebSockets/uSockets/src/libusockets.h
        )

    target_include_directories(uWS PRIVATE
        uWebSockets/uSockets/src
        ${OPENSSL_INCLUDE_DIR}
        ${LIBUV_INCLUDE_DIR}
        )

    target_include_directories(uWS INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/uWebSockets/uSockets/src>
        )
    
    target_include_directories(uWS INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/uWebSockets/src>
        )
if(ANDROID)
find_library (zlib z)
target_link_libraries(uWS PUBLIC
        ${zlib}
        ${CMAKE_THREAD_LIBS_INIT}
        ${LIBUV_LIBRARIES}
        memory
        )
else()
    target_link_libraries(uWS PUBLIC
        ${OPENSSL_LIBRARIES}
        ${ZLIB_LIBRARIES}
        ${CMAKE_THREAD_LIBS_INIT}
        ${LIBUV_LIBRARIES}
        memory
        )
endif()
    target_include_directories(uWS PRIVATE ${PROJECT_SOURCE_DIR}/base/)
    target_compile_definitions(uWS PRIVATE LIBUS_USE_LIBUV=1)

if (INCPPECT_NO_SSL)
    target_compile_options(uWS PRIVATE -DLIBUS_NO_SSL=1)
else()
    target_compile_options(uWS PRIVATE -DLIBUS_USE_OPENSSL=1)
endif ()
