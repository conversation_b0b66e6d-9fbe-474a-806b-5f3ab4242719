#pragma once
#include "PlatformPrefix.h"
#include "Platform/PlatformTypes.h"
#include <assert.h>
#include "DawnSettings.h"
#include "BakingTypeDefine.h"
#include "TLBSDataProtocol/BakingJobDefine.h"

NS_GPUBAKING_BEGIN


GPUBAKING_NS_NAME::LightingBakeConfig& LightingBakeConfig::GetInstance()
{
	static LightingBakeConfig sInstance;
	return sInstance;
}

const GPUBAKING_NS_NAME::LightingBakeConfig::BakeMatInfo* GPUBAKING_NS_NAME::LightingBakeConfig::QueryBakeMaterial(const std::string& fxFileName)
{
    for (const auto& [matKey, matValue] : mBakeMaterialsMap) 
    {
        if (fxFileName.find(matKey) != std::string::npos)
        {
            return &matValue;
        }
    }
    return nullptr;
}

GPUBAKING_NS_NAME::LightingBakeConfig::~LightingBakeConfig()
{
	for (auto *dawnSetting : mDawnSettings)
	{
		delete dawnSetting;
	}
	mDawnSettings.clear();

	delete LevelSettings;

	delete ShadowSettingParameter;
}

GPUBAKING_NS_NAME::LightingBakeConfig::LightingBakeConfig()
{
	assert(EDawnQualityLevel::Preview == 0);
	mDawnSettings.push_back(new UDawnSettingsPreview);

	assert(EDawnQualityLevel::Medium == 1);
	mDawnSettings.push_back(new UDawnSettingsMedium);

	assert(EDawnQualityLevel::High == 2);
	mDawnSettings.push_back(new UDawnSettingsHigh);

	assert(EDawnQualityLevel::Production == 3);
	mDawnSettings.push_back(new UDawnSettingsProduction);

	LevelSettings = new FLightmassWorldInfoSettings();

	ShadowSettingParameter = new FShadowSettingParameters();
}


NS_GPUBAKING_END
