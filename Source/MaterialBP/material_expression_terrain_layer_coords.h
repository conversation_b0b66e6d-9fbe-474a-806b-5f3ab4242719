#pragma once

#include "material_expression.h"

namespace cross {
enum CEMeta(Cli) TerrainCoordMappingType : int32_t
{
    TCMT_Auto,
    TCMT_XY,
    TCMT_XZ,
    TCMT_YZ,
    TCMT_MAX,
};

enum CEMeta(Cli) TerrainCustomizedCoordType : int32_t
{
	LCCT_None,
	LCCT_CustomUV0,
	LCCT_CustomUV1,
	LCCT_CustomUV2,
	LCCT_WeightMapUV,
	LCCT_MAX,
};

class CEMeta(Cli) MaterialExpressionTerrainLayerCoords : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Terrain Layer Coords";
    }

public:
    CEProperty(Reflect)
    TerrainCoordMappingType m_MappingType = TerrainCoordMappingType::TCMT_Auto;

    CEProperty(Reflect)
    TerrainCustomizedCoordType m_CustomUVType = TerrainCustomizedCoordType::LCCT_None;

    CEProperty(Reflect)
    float m_MappingScale = 0.f;

    CEProperty(Reflect)
    float m_MappingRotation = 0.f;

    CEProperty(Reflect)
    float m_MappingPanU = 0.f;

    CEProperty(Reflect)
    float m_MappingPanV = 0.f;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross