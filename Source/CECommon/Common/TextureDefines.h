#pragma once
//#include "pch/CrossBasePCHPublic.h"
#include "EnginePrefix.h"
#include "NativeGraphicsInterface/NGI.h"

#pragma push_macro("None")
#undef None
enum class CEMeta(Editor) TextureType : UInt32
{
    ImageTexture = 0,
    RectangularCubeMap,
    NormalTexture,
    DataTexture,
    Texture3D,
    LUT_Cube,
    TEXTURE_UDIM
};

enum class CEMeta(Editor) TextureDimension : UInt32
{
    TextureDimension_None = 0,

    Tex2D,
    Tex3D,
    TexCube,
    Tex2DArray,
    TexCubeArray,
};

enum class CEMeta(Editor) TextureSize : UInt32
{
    None = 0,
    SQUARE_32 = 1 << 5,
    SQUARE_64 = 1 << 6,
    SQUARE_128 = 1 << 7,
    SQUARE_256 = 1 << 8,
    SQUARE_512 = 1 << 9,
    SQUARE_1024 = 1 << 10,
    SQUARE_2048 = 1 << 11,
    SQUARE_4096 = 1 << 12,
    SQUARE_8192 = 1 << 13,
};

CE<PERSON>eta(Editor)
inline bool IsCubeType(TextureDimension t)
{
    return t == TextureDimension::TexCube || t == TextureDimension::TexCubeArray;
}

CEMeta(Editor)
inline bool IsArrayType(TextureDimension t)
{
    return t == TextureDimension::Tex2DArray || t == TextureDimension::TexCubeArray;
}



enum class CEMeta(Editor) TextureFormat : UInt32
{
    TextureFormat_None = 0,

    A8 = 1,            INT_FIRST = A8,   // In memory: A8U
    R8 = 2,               // In memory: R8U
    RG16 = 3,           // In memory: R8U,G8U
    RGBX32 = 4,        // In memory: R8U,G8U,B8U,X8U; 32bit but without alpha info. Dont use RGB24 because DX has no R8G8B8 format..
    RGBA32 = 5,        // In memory: R8U,G8U,B8U,A8U; 0xAABBGGRR if viewed as 32 bit word on little-endian. Generally preferred for 32 bit uncompressed data.
    RGB565 = 6,        // In memory: R5U,G6U,B5U; 0xBGR if viewed as 16 bit word on little-endian, equivalent to VK_FORMAT_R5G6B5_UNORM_PACK16, Pixel layout depends on endianness, R5;G6;B5 going from high to low bits
    RGBA4444 = 7,      // In memory: A4U,R4U,G4U,B4U; 0xARGB if viewed as 16 bit word on little-endian, Pixel layout depends on endianness, R4;G4;B4;A4 going from high to low bits
    R16 = 8,        INT_LAST = R16,   // In memory: R16U


    // DX BC
    BC1 = 15,  /*BC_FIRST = BC1,*/          // aka BC1
    BC2 = 16,                            // aka BC2
    BC3 = 17,                            // aka BC3
    BC4 = 18,                            // One-component compressed format
    BC5 = 19,                            // Two-component compressed format
    BC6H = 20,                            // RGB HDR compressed format, unsigned.
    BC7 = 21,  /*BC_LAST = BC1,*/            // HQ RGB(A) compressed format.


    // float/half texture formats
    RHalf = 30,        FLOAT_FIRST = RHalf,        // In memory: R16F
    RGHalf = 31,                                // In memory: R16F,G16F
    RGBAHalf = 32,                                // In memory: R16F,G16F,B16F,A16F
    RFloat = 33,                                  // In memory: R32F
    RGFloat = 34,                                // In memory: R32F,G32F
    RGBFloat = 35,                                // In memory: R16F,G16F,B16F
    RGBAFloat = 36,                                // In memory: R32F,G32F,B32F,A32F
    RGB9e5Float = 37,                           // In memory: unsigned!! Three partial-precision floating-point numbers encoded into a single 32-bit value all sharing the same5-bit exponent.
    R11G11B10Float = 38, FLOAT_LAST = R11G11B10Float,

    // video formats 
    YUY2 = 45,            // YUV format, can be used for video streams.

    //--- Begin of Compress Format

    // PowerVR / iOS PVRTC compression
    PVRTC_RGB2 = 60,  PVRTC_FIRST = PVRTC_RGB2,
    PVRTC_RGBA2 = 61,
    PVRTC_RGB4 = 62,
    PVRTC_RGBA4 = 63, PVRTC_LAST = PVRTC_RGBA4,

    // OpenGL ES 2.0 ETC
    ETC_RGB4 = 65, ETC_FIRST = ETC_RGB4, ETC = ETC_FIRST,

    // EAC and ETC2 compressed formats, in OpenGL ES 3.0
    EAC_R = 66,
    EAC_R_SIGNED = 67,
    EAC_RG = 68,
    EAC_RG_SIGNED = 69,
    ETC2_RGB = 70,
    ETC2_RGBA1 = 71,
    ETC2_RGBA8 = 72, ETC_LAST = ETC2_RGBA8,


    // ASTC. The RGB and RGBA formats are internally identical.
    ASTC_4x4 = 80, ASTC_FIRST = ASTC_4x4, ASTC_LDR_FIRST = ASTC_4x4, ASTC = ASTC_FIRST,
    ASTC_5x5 = 81,
    ASTC_5x4 = 82,
    ASTC_6x6 = 83,
    ASTC_6x5 = 84,
    ASTC_8x8 = 85,
    ASTC_8x5 = 86,
    ASTC_8x6 = 87,
    ASTC_10x10 = 88,
    ASTC_10x8 = 89,
    ASTC_10x6 = 90,
    ASTC_10x5 = 91,
    ASTC_12x10 = 92,
    ASTC_12x12 = 93, ASTC_LDR_LAST = ASTC_12x12,


    ASTC_HDR_4x4 = 100, ASTC_HDR_FIRST = ASTC_4x4,
    ASTC_HDR_5x5 = 101,
    ASTC_HDR_5x4 = 102,
    ASTC_HDR_6x6 = 103,
    ASTC_HDR_6x5 = 104,
    ASTC_HDR_8x8 = 105,
    ASTC_HDR_8x5 = 106,
    ASTC_HDR_8x6 = 107,
    ASTC_HDR_10x10 = 108,
    ASTC_HDR_10x8 = 109,
    ASTC_HDR_10x6 = 110,
    ASTC_HDR_10x5 = 111,
    ASTC_HDR_12x10 = 112,
    ASTC_HDR_12x12 = 113, ASTC_LAST = ASTC_HDR_12x12, ASTC_HDR_LAST = ASTC_4x4,

    BASIS_UNIVERSAL = 114
    //--- End of Compress Format
};


#define TextureStringType(x) {TextureFormat::x, #x}

inline std::string ToString(TextureFormat format)
{
    static std::unordered_map<TextureFormat, std::string> Names = {
        TextureStringType(TextureFormat_None),
        TextureStringType(A8),
        TextureStringType(R8),
        TextureStringType(RG16),
        TextureStringType(RGBX32),
        TextureStringType(RGBA32),
        TextureStringType(RGB565),
        TextureStringType(RGBA4444),
        TextureStringType(R16),
        TextureStringType(BC1),
        TextureStringType(BC2),
        TextureStringType(BC3),
        TextureStringType(BC4),
        TextureStringType(BC5),
        TextureStringType(BC6H),
        TextureStringType(BC7),
        TextureStringType(RHalf),
        TextureStringType(RGHalf),
        TextureStringType(RGBAHalf),
        TextureStringType(RFloat),
        TextureStringType(RGFloat),
        TextureStringType(RGBFloat),
        TextureStringType(RGBAFloat),
        TextureStringType(RGB9e5Float),
        TextureStringType(R11G11B10Float),
        TextureStringType(YUY2),
        TextureStringType(PVRTC_RGB2),
        TextureStringType(PVRTC_RGBA2),
        TextureStringType(PVRTC_RGB4),
        TextureStringType(PVRTC_RGBA4),
        TextureStringType(ETC_RGB4),
        TextureStringType(EAC_R),
        TextureStringType(EAC_R_SIGNED),
        TextureStringType(EAC_RG),
        TextureStringType(EAC_RG_SIGNED),
        TextureStringType(ETC2_RGB),
        TextureStringType(ETC2_RGBA1),
        TextureStringType(ETC2_RGBA8),
        TextureStringType(ASTC_4x4),
        TextureStringType(ASTC_5x5),
        TextureStringType(ASTC_5x4),
        TextureStringType(ASTC_6x6),
        TextureStringType(ASTC_6x5),
        TextureStringType(ASTC_8x8),
        TextureStringType(ASTC_8x5),
        TextureStringType(ASTC_8x6),
        TextureStringType(ASTC_10x10),
        TextureStringType(ASTC_10x8),
        TextureStringType(ASTC_10x6),
        TextureStringType(ASTC_10x5),
        TextureStringType(ASTC_12x10),
        TextureStringType(ASTC_12x12),

        TextureStringType(ASTC_HDR_4x4),
        TextureStringType(ASTC_HDR_5x5),
        TextureStringType(ASTC_HDR_5x4),
        TextureStringType(ASTC_HDR_6x6),
        TextureStringType(ASTC_HDR_6x5),
        TextureStringType(ASTC_HDR_8x8),
        TextureStringType(ASTC_HDR_8x5),
        TextureStringType(ASTC_HDR_8x6),
        TextureStringType(ASTC_HDR_10x10),
        TextureStringType(ASTC_HDR_10x8),
        TextureStringType(ASTC_HDR_10x6),
        TextureStringType(ASTC_HDR_10x5),
        TextureStringType(ASTC_HDR_12x10),
        TextureStringType(ASTC_HDR_12x12),

        TextureStringType(BASIS_UNIVERSAL),
    };

    return Names[format];
}

CEMeta(Editor)
inline bool IsIntFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::INT_FIRST && fmt <= TextureFormat::INT_LAST;
}

CEMeta(Editor)
inline bool IsFloatFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::FLOAT_FIRST && fmt <= TextureFormat::FLOAT_LAST;
}

CEMeta(Editor)
inline bool IsCompressFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::PVRTC_FIRST && fmt <= TextureFormat::ASTC_LAST;
}

CEMeta(Editor)
inline bool IsETCFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::ETC_FIRST && fmt <= TextureFormat::ETC_LAST;
}

CEMeta(Editor)
inline bool IsBCFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::BC1 && fmt <= TextureFormat::BC7;
}

CEMeta(Editor)
inline bool IsASTCFormat(TextureFormat fmt)
{
    return fmt >= TextureFormat::ASTC_FIRST && fmt <= TextureFormat::ASTC_LAST;
}

CEMeta(Editor)
inline UInt32 GetPixelByteSize(TextureFormat fmt)
{
    if (IsASTCFormat(fmt))
        return 16;
    switch (fmt)
    {
    case TextureFormat::A8:
    case TextureFormat::R8:
        return 1;
    case TextureFormat::RG16:
    case TextureFormat::RGB565:
    case TextureFormat::R16:
    case TextureFormat::RHalf:
    case TextureFormat::RGBA4444:
        return 2;
    case TextureFormat::RGBX32:
    case TextureFormat::RGBA32:
    case TextureFormat::RFloat:
    case TextureFormat::RGHalf:
    case TextureFormat::RGB9e5Float:
    case TextureFormat::R11G11B10Float:
        return 4;
    case TextureFormat::RGFloat:
    case TextureFormat::RGBAHalf:
        return 8;
    case TextureFormat::RGBFloat:
        return 12;
    case TextureFormat::RGBAFloat:
        return 16;
    default:
        AssertMsg(false, "None Pixel Format");
        return 0;
    }
}

CEMeta(Editor)
inline UInt32 GetPixelBitSize(TextureFormat fmt)
{
    return GetPixelByteSize(fmt) * 8;
}

CEMeta(Editor)
inline cross::Vector2u GetBlockSize(TextureFormat fmt)
{
    if (!IsCompressFormat(fmt))
        return { 1, 1 };
    else if (IsBCFormat(fmt) || IsETCFormat(fmt))
        return { 4, 4 };
    else
    {
        switch (fmt)
        {
        case TextureFormat::ASTC_4x4:
        case TextureFormat::ASTC_HDR_4x4:
            return { 4, 4 };
        case TextureFormat::ASTC_5x5:
        case TextureFormat::ASTC_HDR_5x5:
            return { 5, 5 };
        case TextureFormat::ASTC_6x6:
        case TextureFormat::ASTC_HDR_6x6:
            return { 6, 6 };
        case TextureFormat::ASTC_8x8:
        case TextureFormat::ASTC_HDR_8x8:
            return { 8, 8 };
        case TextureFormat::ASTC_10x10:
        case TextureFormat::ASTC_HDR_10x10:
            return { 10, 10 };
        case TextureFormat::ASTC_12x12:
        case TextureFormat::ASTC_HDR_12x12:
            return { 12, 12 };
        default:
            AssertMsg(false, "None Pixel Format");
            return {0, 0};
        }
    }
}

#define DECLARE_RENDER_TEXTURE_FORMAT(FORMAT) FORMAT = cross::ToUnderlying(cross::GraphicsFormat::FORMAT)

enum class CEMeta(Editor) RenderTextureFormat : UInt32
{
    DECLARE_RENDER_TEXTURE_FORMAT(Unknown),
    DECLARE_RENDER_TEXTURE_FORMAT(R8_UNorm),
    DECLARE_RENDER_TEXTURE_FORMAT(R8G8B8A8_UNorm),
    DECLARE_RENDER_TEXTURE_FORMAT(R8G8B8A8_SRGB),
    DECLARE_RENDER_TEXTURE_FORMAT(A2B10G10R10_UNormPack32),
    DECLARE_RENDER_TEXTURE_FORMAT(R32_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(R16G16_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(R32G32_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(R16G16B16A16_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(R32G32B32A32_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(R11G11B10_UFloatPack32),
    DECLARE_RENDER_TEXTURE_FORMAT(D32_SFloat),
    DECLARE_RENDER_TEXTURE_FORMAT(D24_UNorm_S8_UInt),
};

enum class CEMeta(Editor) DepthTextureFormat : UInt32
{
    DepthTextureFormat_None = 0,

    D32Float,
    D24S8,
};

enum class CEMeta(Editor) TextureFilterMode : UInt32
{
    Nearest,
    Bilinear,
    Trilinear,

    Count
};

enum class CEMeta(Editor) TextureWrapMode : UInt32
{
    Repeat,
    Clamp,
    Mirror,
    MirrorOnce,

    Count
};



enum class CEMeta(Editor) RenderTextureReadWrite : UInt32
{
    ReadWriteLinear,            // No sRGB read / write
    ReadWriteSRGB,            // sRGB read / write

    Count
};

enum CEMeta(Editor) MipmapGenerateSetting : UInt32
{
    Initial = 0,
    Sharpen0 ,
    Sharpen1 ,
    Sharpen2 ,
    Sharpen3 ,
    Sharpen4 ,
    Sharpen5 ,
    Sharpen6 ,
    Sharpen7 ,
    Sharpen8 ,
    Sharpen9 ,
    Sharpen10 ,
    Blur1,
    Blur2,
    Blur3,
    Blur4,
    Blur5,
};
enum CEMeta(Editor) TextureGroup : UInt32
{
    Default = 0,
    Heightmap,
    UI,
    Character,
    VFX,
    Lightmap,
    Terrain,
};
#pragma pop_macro("None")
