
#pragma once
#include <vector>
#include <assert.h>
#include "CECommonForward.h"
#include "CECommon/Common/WorldInternalSystem.h"

namespace cross
{

class RenderWorld;

struct AddToRenderWorldEventData
{
};

using OnSystemAddToRenderWorldEvent = SystemEvent<AddToRenderWorldEventData>;

class  RenderSystemBase : public WorldInternalSystem
{
    CEMetaInternal(Reflect) 
public:
    inline SystemCatalog GetSystemCatalog() const final { return SystemCatalog::SystemCatalogRender; }

    CECOMMON_API void SetRenderWorld(RenderWorld* renderWorld);

    CECOMMON_API RenderWorld* GetRenderWorld() const
    {
        return mRenderWorld;
    }

    CECOMMON_API void BeginFrame(FrameParam* frameParam);

    CECOMMON_API void FirstUpdate(FrameParam* frameParam);

    CECOMMON_API void Update(FrameTickStage stage, FrameParam* frameParam);

    CECOMMON_API void EndFrame(FrameParam* frameParam);

    CECOMMON_API virtual void BuildTasks(FrameTickStage stage, class FrameParam* frameParam) override final;

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) {}
    virtual void OnFirstUpdate(FrameParam* frameParam) {}
    virtual void OnEndFrame(FrameParam* frameParam) {}
    RenderWorld* mRenderWorld{ nullptr };
};

struct RenderWorldSystemChangedData
{
    RenderWorldSystemChangedData() = default;

    RenderWorldSystemChangedData(WorldSystemEventFlag flag, RenderSystemBase* system) :mFlag(flag), mRenderSystem(system) {}

    WorldSystemEventFlag mFlag{ WorldSystemEventFlag::None };
    RenderSystemBase* mRenderSystem{ nullptr };
};

using RenderWorldSystemChangedEvent = SystemEvent<RenderWorldSystemChangedData>;

}
