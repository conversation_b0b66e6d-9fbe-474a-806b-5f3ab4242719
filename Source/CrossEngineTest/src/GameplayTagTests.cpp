#include <gtest/gtest.h>
#include <string>
#include <vector>
#include <sstream>
#include <iostream>
#include <chrono>
#include "GameEngine.h"
#include "GameplayAbilities/GameplayTags/GameplayTagContainer.h"
#include "GameplayAbilities/GameplayTags/GameplayTagsManager.h"
#include "GameplayAbilities/Utils/DataTable.h"
// --------------------------------------------------------

class ScopeLogTime
{
public:
    ScopeLogTime(std::string Label, int32_t Iterations)
        : Label_(std::move(Label))
        , Iters_(Iterations)
        , Start_(std::chrono::steady_clock::now())
    {}
    ~ScopeLogTime()
    {
        auto End = std::chrono::steady_clock::now();
        auto Ms = std::chrono::duration_cast<std::chrono::milliseconds>(End - Start_).count();
        std::cout << Label_ << " : " << Ms << " ms (" << Iters_ << ")\n";
    }

private:
    std::string Label_;
    int32_t Iters_;
    std::chrono::steady_clock::time_point Start_;
};

// ---------- GTest Fixture ----------
class FGameplayTagTest : public ::testing::Test
{
protected:
    static void SetUpTestSuite()
    {
        ::cross::CrossEngine* gCrossEngine = nullptr;
        if (!gCrossEngine)
        {
            gCrossEngine = dynamic_cast<::cross::CrossEngine*>(CreateCrossEngine());

            ::cross::InitInfo info{
                .AssetPath = "C:/Project/AssetExamples",
                .EngineResourcePath = "C:/Project/CrossEngine/Resource",
                .StartupType = ::cross::AppStartUpType::AppStartUpTypeHeadless,
            };
            gCrossEngine->Init(info);
            gCrossEngine->PostInit();
            gCrossEngine->PreTick();
        }
        try
        {
            // auto DataTable = CreateGameplayDataTable();
            auto Table = new cegf::CEDataTable();
            cegf::UGameplayTagsManager::Get().PopulateTreeFromDataTable(Table);
        }
        catch (const std::exception& e)
        {
            std::cout << "Exception in SetUpTestSuite: " << e.what() << std::endl;
        }
    }
    static cegf::CEDataTable* CreateGameplayDataTable()
    {
        /*std::vector<std::string> Tags = {"Effect.Damage",
                                         "Effect.Damage.Basic",
                                         "Effect.Damage.Type1",
                                         "Effect.Damage.Type2",
                                         "Effect.Damage.Reduce",
                                         "Effect.Damage.Buffable",
                                         "Effect.Damage.Buff",
                                         "Effect.Damage.Physical",
                                         "Effect.Damage.Fire",
                                         "Effect.Damage.Buffed.FireBuff",
                                         "Effect.Damage.Mitigated.Armor",
                                         "Effect.Lifesteal",
                                         "Effect.Shield",
                                         "Effect.Buff",
                                         "Effect.Immune",
                                         "Effect.FireDamage",
                                         "Effect.Shield.Absorb",
                                         "Effect.Protect.Damage",
                                         "Stackable",
                                         "Stack.DiminishingReturns",
                                         "GameplayCue.Burning"};*/
        std::vector<std::string> Tags = {};
        /*for (int i = 1; i <= 40; ++i)
            Tags.emplace_back("Expensive.Status.Tag.Type." + std::to_string(i));*/

        auto Table = new cegf::CEDataTable();
        Table->RowStruct = const_cast<gbf::reflection::MetaClass*>(cegf::QueryMetaClass<cegf::FGameplayTagTableRow>());

        std::unordered_map<std::string, std::shared_ptr<cegf::CETableRowBase>> RowMap;
        for (int i = 0; i < Tags.size(); i++)
        {
            RowMap.insert({std::to_string(i), std::make_shared<cegf::FGameplayTagTableRow>(Tags[i])});
        }

        Table->SetRowMap(RowMap);

        /*auto Table = new cegf::CEDataTable();
        std::string filePath = R"(C:\Project\CrossEngine\Resource\NewDataTable.json)";
        Table->InitFromJson(filePath);*/

        auto basePtr = Table->GetRowMap().at("0"); 

        auto rowPtr = std::dynamic_pointer_cast<cegf::FGameplayTagTableRow>(basePtr);

        EXPECT_TRUE(rowPtr);

        EXPECT_TRUE(rowPtr->Tag == "Effect.Damage");
        return Table;
    }

    static cegf::FGameplayTag GetTagForString(const std::string& Name)
    {
        return cegf::UGameplayTagsManager::Get().RequestGameplayTag(Name.c_str());
    }
};

// ---------------- SimpleTest ----------------
TEST_F(FGameplayTagTest, SimpleTest)
{
    FName TagName = FName("Stack.DiminishingReturns");
    auto Tag = GetTagForString("Stack.DiminishingReturns");
    EXPECT_EQ(Tag.GetTagName(), TagName);
}

// ---------------- TagComparison ----------------
TEST_F(FGameplayTagTest, TagComparison)
{
    auto EffectDamage = GetTagForString("Effect.Damage");
    auto EffectDamageType1 = GetTagForString("Effect.Damage.Type1");
    auto EffectDamageType2 = GetTagForString("Effect.Damage.Type2");
    cegf::FGameplayTag CueTag = GetTagForString(GAS_TEXT("GameplayCue.Burning"));
    cegf::FGameplayTag EmptyTag;

    EXPECT_EQ(EffectDamageType1, EffectDamageType1);
    EXPECT_NE(EffectDamageType1, EffectDamageType2);
    EXPECT_NE(EffectDamageType1, EffectDamage);

    EXPECT_TRUE(EffectDamageType1.MatchesTag(EffectDamage));
    EXPECT_FALSE(EffectDamageType1.MatchesTagExact(EffectDamage));
    EXPECT_FALSE(EffectDamageType1.MatchesTag(EmptyTag));
    EXPECT_FALSE(EffectDamageType1.MatchesTagExact(EmptyTag));
    EXPECT_FALSE(EmptyTag.MatchesTag(EmptyTag));
    EXPECT_FALSE(EmptyTag.MatchesTagExact(EmptyTag));

    EXPECT_EQ(EffectDamageType1.RequestDirectParent(), EffectDamage);
}

// ---------------- TagContainer ----------------
TEST_F(FGameplayTagTest, TagContainerTest)
{
    // Basic Tag
    auto EffectDamage = GetTagForString("Effect.Damage");
    auto EffectDamageType1 = GetTagForString("Effect.Damage.Type1");
    auto EffectDamageType2 = GetTagForString("Effect.Damage.Type2");
    auto CueTag = GetTagForString("GameplayCue.Burning");
    cegf::FGameplayTag EmptyTag;

    // Empty container
    cegf::FGameplayTagContainer EmptyContainer;

    // TagContainer = { Type1 , Cue }
    cegf::FGameplayTagContainer TagContainer;
    TagContainer.AddTag(EffectDamageType1);
    TagContainer.AddTag(CueTag);

    // Reverse TagContainer (elements in reverse order)
    cegf::FGameplayTagContainer ReverseTagContainer;
    ReverseTagContainer.AddTag(CueTag);
    ReverseTagContainer.AddTag(EffectDamageType1);

    // TagContainer2 = { Type2 , Cue }
    cegf::FGameplayTagContainer TagContainer2;
    TagContainer2.AddTag(EffectDamageType2);
    TagContainer2.AddTag(CueTag);

    // Equivalent / Not equivalent
    EXPECT_EQ(TagContainer, TagContainer);
    EXPECT_EQ(TagContainer, ReverseTagContainer);
    EXPECT_NE(TagContainer, TagContainer2);

    // Copy, reset, and append again
    auto TagContainerCopy = TagContainer;
    EXPECT_EQ(TagContainerCopy, TagContainer);
    EXPECT_NE(TagContainerCopy, TagContainer2);

    TagContainerCopy.Reset();
    TagContainerCopy.AppendTags(TagContainer);
    EXPECT_EQ(TagContainerCopy, TagContainer);
    EXPECT_NE(TagContainerCopy, TagContainer2);

    // Any / All / Exact combinations
    EXPECT_TRUE(TagContainer.HasAny(TagContainer2));
    EXPECT_TRUE(TagContainer.HasAnyExact(TagContainer2));
    EXPECT_FALSE(TagContainer.HasAll(TagContainer2));
    EXPECT_FALSE(TagContainer.HasAllExact(TagContainer2));
    EXPECT_TRUE(TagContainer.HasAll(TagContainerCopy));
    EXPECT_TRUE(TagContainer.HasAllExact(TagContainerCopy));

    // Compare with empty container
    EXPECT_TRUE(TagContainer.HasAll(EmptyContainer));
    EXPECT_TRUE(TagContainer.HasAllExact(EmptyContainer));
    EXPECT_FALSE(TagContainer.HasAny(EmptyContainer));
    EXPECT_FALSE(TagContainer.HasAnyExact(EmptyContainer));

    EXPECT_TRUE(EmptyContainer.HasAll(EmptyContainer));
    EXPECT_TRUE(EmptyContainer.HasAllExact(EmptyContainer));
    EXPECT_FALSE(EmptyContainer.HasAny(EmptyContainer));
    EXPECT_FALSE(EmptyContainer.HasAnyExact(EmptyContainer));

    EXPECT_FALSE(EmptyContainer.HasAll(TagContainer));
    EXPECT_FALSE(EmptyContainer.HasAllExact(TagContainer));
    EXPECT_FALSE(EmptyContainer.HasAny(TagContainer));
    EXPECT_FALSE(EmptyContainer.HasAnyExact(TagContainer));

    // Single Tag check
    EXPECT_TRUE(TagContainer.HasTag(EffectDamage));
    EXPECT_FALSE(TagContainer.HasTagExact(EffectDamage));
    EXPECT_FALSE(TagContainer.HasTag(EmptyTag));
    EXPECT_FALSE(TagContainer.HasTagExact(EmptyTag));

    // FGameplayTag self-matching
    cegf::FGameplayTagContainer DamageContainer{EffectDamage};
    EXPECT_TRUE(EffectDamageType1.MatchesAny(DamageContainer));
    EXPECT_FALSE(EffectDamageType1.MatchesAnyExact(DamageContainer));

    EXPECT_TRUE(EffectDamageType1.MatchesAny(TagContainer));

    // FilterExact: Keep only tags that are exactly the same in TagContainer and TagContainer2
    auto Filtered = TagContainer.FilterExact(TagContainer2);
    EXPECT_TRUE(Filtered.HasTagExact(CueTag));
    EXPECT_FALSE(Filtered.HasTagExact(EffectDamageType1));

    // Filter: Keep tags that match EffectDamage and its subclasses
    Filtered = TagContainer.Filter(cegf::FGameplayTagContainer(EffectDamage));
    EXPECT_FALSE(Filtered.HasTagExact(CueTag));
    EXPECT_TRUE(Filtered.HasTagExact(EffectDamageType1));

    // AppendMatchingTags: Append the "common ancestor tags" of the two containers to Filtered
    Filtered.Reset();
    Filtered.AppendMatchingTags(TagContainer, TagContainer2);
    EXPECT_TRUE(Filtered.HasTagExact(CueTag));
    EXPECT_FALSE(Filtered.HasTagExact(EffectDamageType1));

    // Single element container & Parent container
    auto SingleTagContainer = EffectDamageType1.GetSingleTagContainer();
    auto ParentContainer = EffectDamageType1.GetGameplayTagParents();

    EXPECT_TRUE(SingleTagContainer.HasTagExact(EffectDamageType1));
    EXPECT_TRUE(SingleTagContainer.HasTag(EffectDamage));
    EXPECT_FALSE(SingleTagContainer.HasTagExact(EffectDamage));

    EXPECT_TRUE(ParentContainer.HasTagExact(EffectDamageType1));
    EXPECT_TRUE(ParentContainer.HasTag(EffectDamage));
    EXPECT_TRUE(ParentContainer.HasTagExact(EffectDamage));
}

// ---------------- PerfTest (Optional) ----------------
TEST_F(FGameplayTagTest, PerfTest)
{
    const int SmallN = 1000;
    const int LargeN = 10000;

    auto DamageType1 = GetTagForString("Effect.Damage.Type1");
    auto DamageType2 = GetTagForString("Effect.Damage.Type2");
    auto CueBurning = GetTagForString("GameplayCue.Burning");

    cegf::FGameplayTagContainer BigContainer;
    BigContainer.AddTag(DamageType1);
    BigContainer.AddTag(DamageType2);
    BigContainer.AddTag(CueBurning);
    for (int i = 1; i <= 40; ++i)
        BigContainer.AddTag(GetTagForString("Expensive.Status.Tag.Type." + std::to_string(i)));

    // Example: Test performance of RequestGameplayTag
    {
        auto Beg = std::chrono::steady_clock::now();
        for (int i = 0; i < LargeN; ++i)
            GetTagForString("Effect.Damage");
        auto End = std::chrono::steady_clock::now();
        std::cout << LargeN << " RequestGameplayTag took " << std::chrono::duration_cast<std::chrono::milliseconds>(End - Beg).count() << " ms\n";
    }

    // Continue with other performance loops; execution completion counts as passing
    SUCCEED();
}

TEST_F(FGameplayTagTest, TagQueryTest)
{
    cegf::FGameplayTagContainer AllGameplayTags;
    cegf::UGameplayTagsManager::Get().RequestAllGameplayTags(AllGameplayTags, true);

    ASSERT_FALSE(AllGameplayTags.IsEmpty()) << "AllGameplayTags should be empty.";

    cegf::FGameplayTag RandomTag = AllGameplayTags.First();
    cegf::FGameplayTagContainer EmptyTagContainer;
    cegf::FGameplayTagContainer NonEmptyContainer{RandomTag};

    // The Query is Completely Empty
    {
        cegf::FGameplayTagQuery Query;
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Empty Query w/ Empty Container";
        EXPECT_FALSE(Query.Matches(NonEmptyContainer)) << "Match Empty Query w/ Non-Empty Container";
    }

    // The Query has Expressions, but those Expressions have no valid tags
    {
        cegf::FGameplayTagQuery Query;
        cegf::FGameplayTagQueryExpression TempExpression;

        TempExpression.AllExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - AllExprMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Empty AllExprMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Empty AllExprMatch w/ Non-Empty Container";

        TempExpression.AllTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - AllTagsMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Empty AllTagsMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Empty AllTagsMatch w/ Non-Empty Container";

        TempExpression.AnyExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - AnyExprMatch"));
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Empty AnyExprMatch w/ Empty Container";
        EXPECT_FALSE(Query.Matches(NonEmptyContainer)) << "Match Empty AnyExprMatch w/ Non-Empty Container";

        TempExpression.AnyTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - AnyTagsMatch"));
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Empty AnyTagsMatch w/ Empty Container";
        EXPECT_FALSE(Query.Matches(NonEmptyContainer)) << "Match Empty AnyTagsMatch w/ Non-Empty Container";

        TempExpression.NoExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - NoExprMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Empty NoExprMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Empty NoExprMatch w/ Non-Empty Container";

        TempExpression.NoTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Empty Tag Query Expression - NoTagsMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Empty NoTagsMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Empty NoTagsMatch w/ Non-Empty Container";
    }

    // The Query has Expressions, and those Expressions have valid tags
    {
        cegf::FGameplayTagQuery Query;
        cegf::FGameplayTagQueryExpression TempExpression;
        TempExpression.TagSet = std::vector<cegf::FGameplayTag>{RandomTag};

        TempExpression.AllExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - AllExprMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty AllExprMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty AllExprMatch w/ Non-Empty Container";

        TempExpression.AllTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - AllTagsMatch"));
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty AllTagsMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty AllTagsMatch w/ Non-Empty Container";

        TempExpression.AnyExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - AnyExprMatch"));
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty AnyExprMatch w/ Empty Container";
        EXPECT_FALSE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty AnyExprMatch w/ Non-Empty Container";

        TempExpression.AnyTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - AnyTagsMatch"));
        EXPECT_FALSE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty AnyTagsMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty AnyTagsMatch w/ Non-Empty Container";

        TempExpression.NoExprMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - NoExprMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty NoExprMatch w/ Empty Container";
        EXPECT_TRUE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty NoExprMatch w/ Non-Empty Container";

        TempExpression.NoTagsMatch();
        Query.Build(TempExpression, GAS_TEXT("Non-Empty Tag Query Expression - NoTagsMatch"));
        EXPECT_TRUE(Query.Matches(EmptyTagContainer)) << "Match Non-Empty NoTagsMatch w/ Empty Container";
        EXPECT_FALSE(Query.Matches(NonEmptyContainer)) << "Match Non-Empty NoTagsMatch w/ Non-Empty Container";
    }
    SUCCEED();
}
