#include "EnginePrefix.h"
#include "CharacterMovementSystemG.h"
#include "MotionMovementComponent.h"
#include "MotionMovementSystemG.h"

namespace cross {

    ecs::ComponentDesc* cross::MotionMovementComponentG::GetDesc()
    {
        return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::MotionMovementComponentG>(
            {false, true, true}, 
            &MotionMovementSystemG::SerializeMotionMovementComponent, 
            &MotionMovementSystemG::DeserializeMotionMovementComponent, 
            &MotionMovementSystemG::PostDeserializeMotionMovementComponent);
    }

}