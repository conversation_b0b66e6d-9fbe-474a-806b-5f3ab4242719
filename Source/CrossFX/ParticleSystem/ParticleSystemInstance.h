#pragma once
#include "NParticleSystemGPUComputeProxy.h"
#include "NParticleSystemGPUDrivenG.h"
#include "ParticleSystemCommon.h"
#include "ParticleEmitterInstance.h"
#include "Resource/ParticleSystem/ParticleSystemResource.h"

namespace cross::fx
{

class ParticleSystemInstance;
using SystemInstancePtr = std::shared_ptr<ParticleSystemInstance>;
using EmitterInstancePtr = std::shared_ptr<ParticleEmitterInstance>;

struct ParticleGpuReadbackResult
{
    UInt32 mAliveNum = 0u;
    UInt32 mEmitterIndex = 0u;
    ParticleGpuReadbackResult() = default;
    ParticleGpuReadbackResult(UInt32 alive_num, UInt32 emitter_index) : mAliveNum(alive_num), mEmitterIndex(emitter_index){}
};

class CrossFX_API ParticleSystemInstance
{
public:
    ParticleSystemInstance(NParticleSystemGPUDrivenG* inGPUDriven, ecs::EntityID entityID)
        : mParticleSystemGPUDriven(inGPUDriven)
        , mEntityID(entityID)
    {}

    void Init();

    void Complete();

    void Reset();

    void ResetResource(ParticleSystemResPtr newResource);

    void ResetUniqueID();

    void ResetEmitter(UInt32 emitterIndex, ParticleEmitterResPtr newResource);

    bool IsRunning() const;

    inline const ParticleSystemID GetID() const { return mID; }

    inline ecs::EntityID GetEntityID() const { return mEntityID; }

    inline const ParticleSystemResource* GetResource() const { return mResource.get(); }

    inline const UInt32 GetEmitterNum() const { return static_cast<UInt32>(mEmitters.size()); }

    inline EmitterInstancePtr GetEmitter(UInt32 index) const { return index < mEmitters.size() ? mEmitters[index] : nullptr; }

    inline const std::vector<EmitterInstancePtr>& GetEmitters() const { return mEmitters; }

    inline std::vector<EmitterInstancePtr>& GetEmitters() { return mEmitters; }

    inline void AddEmitter(EmitterInstancePtr emitter) { mEmitters.emplace_back(emitter); }

    inline void Resize(UInt32 emitterNum) { mEmitters.resize(emitterNum); }

    inline void SetSystemState(SimulationState state) { mSimulationState = state; }

    inline SimulationState GetSystemState() const { return mSimulationState; }

    inline void* GetReadbackPtr() { return mReadbackResults.data(); }

    inline void RegisterEmitterReadbackResult(UInt32 emitterIndex) { mReadbackResults.emplace_back(ParticleGpuReadbackResult{0, emitterIndex}); };

    inline void ClearReadbackResults() { mReadbackResults.clear(); }

    inline std::vector<ParticleGpuReadbackResult>& GetReadbackResults() { return mReadbackResults; }

    ModulePtr GetTargetModule(UInt32 emitterIndex, fx::ModuleScope scope, ModuleIndex moduleIndex);

    template<typename ModuleType, typename InfoType, typename ValueType>
    void ModifyEmitterModule(ModulePtr target, const std::string& name, const ValueType& val)
    {
        auto module = std::dynamic_pointer_cast<ModuleType>(target);
        auto info = std::dynamic_pointer_cast<InfoType>(module->GetModuleInfo());

        gbf::reflection::UserObject uo = gbf::reflection::make_user_object(info.get(), gbf::reflection::remote_storage_policy{});
        uo.SetField(name, gbf::reflection::make_value(val));
    }

    void SetOwnerTransform(const TRSMatrixAType& worldMatrix, const Double3& scale, const Quaternion64& rotation)
    {
        mOwnerTransform.mMatrix = worldMatrix;
        mOwnerTransform.mScale = scale;
        mOwnerTransform.mRotation = rotation;
    }

    void TickNParticleParameters(float deltaTime);

    bool HasInterpolationParameters() const { return mHasInterpolationParameters; }

    UInt32 GetParameterIndex(bool bPrevFrame = false) const { return (!!(bPrevFrame && mIsParametersValid) ^ !!mSwapIndex) ? 1 : 0; }

    void SwapParameterBuffers()
    {
        mSwapIndex = ~mSwapIndex;

        // when we've hit both buffers, we'll mark the parameters as being valid
        if (mSwapIndex == 1)
        {
            mIsParametersValid = true;
        }
    }

    const NParticleEmitterParameters& GetNParticleEmitterParameters(UInt32 emitterIndex, bool bPrevFrame = false) const { return mNParticleEmitterParameters[emitterIndex * 2 + GetParameterIndex(bPrevFrame)]; }

    const NParticleGlobalParameters& GetNParticleGlobalParameters(bool bPrevFrame = false) const { return mNParticleGlobalParameters[GetParameterIndex(bPrevFrame)]; }

    const NParticleOwnerParameters& GetNParticleOwnerParameters(bool bPrevFrame = false) const { return mNParticleOwnerParameters[GetParameterIndex(bPrevFrame)]; }

    const NParticleSystemParameters& GetNParticleSystemParameters(bool bPrevFrame = false) const { return mNParticleSystemParameters[GetParameterIndex(bPrevFrame)]; }

    void FillNParticleEmitterParameters(UInt32 emitterIndex);

    void GenerateAndSubmitGPUTick();

    bool RequiresDepthBuffer() const;
    bool RequiresEarlyViewData() const;
    bool RequiresRayTracingScene() const;
    bool RequiresCurrentFrameNDC() const;

private:
    void InitEmitters();

private:
    ParticleSystemID mID;
    ecs::EntityID mEntityID;
    std::vector<EmitterInstancePtr> mEmitters;
    ParticleSystemResPtr mResource;
    bool mAutoPlay{true};
    bool mHasNGPUEmitters = false;
    SimulationState mSimulationState{SimulationState::INIT};
    std::vector<ParticleGpuReadbackResult> mReadbackResults;

    struct
    {
        TRSMatrixAType mMatrix = TRSMatrixAType::Identity();
        Double3 mScale = {1.0, 1.0, 1.0};
        Quaternion64 mRotation;
    } mOwnerTransform;

    std::unique_ptr<NParticleSystemGPUComputeProxy> mSystemGPUComputeProxy;
    NParticleSystemGPUDrivenG* mParticleSystemGPUDriven = nullptr;

    static constexpr UInt32 sParameterBufferCount = 2;
    NParticleGlobalParameters mNParticleGlobalParameters[sParameterBufferCount];
    NParticleOwnerParameters mNParticleOwnerParameters[sParameterBufferCount];
    NParticleSystemParameters mNParticleSystemParameters[sParameterBufferCount];
    std::vector<NParticleEmitterParameters> mNParticleEmitterParameters;

    UInt8 mSwapIndex : 1 = 1;
    UInt8 mIsParametersValid : 1 = false;
    UInt8 mHasInterpolationParameters : 1 = false;
};

}   // namespace cross::fx