using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class MeshSeparateUI : DialogUI
    {
        static MeshSeparateUI _Instance = new MeshSeparateUI();

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Center")]
        public Float2 Center { set; get; }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "BlockSize")]
        public Float2 BlockSize { set; get; }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "SubPartModel")]
        public ModelAsMode SubPartModel { set; get; }

        Panel mMainPanel;
        ScrollView mScrollView;
        Panel mScrollPanel;
        Button mRunButton;

        List<Inspector> mInspectors = new List<Inspector>();

        int mWidth;
        int mHeight;
        InspectorHandler mInspectorHandler;

        MeshAssetDataResource mMeshData = null;
        vector_string mMatPaths = new vector_string();


        public static MeshSeparateUI GetInstance()
        {
            return _Instance;
        }


        public MeshSeparateUI()
        {
            mWidth = 300;
            mHeight = 300;
            Center = new Float2(0, 0);
            BlockSize = new Float2(0, 0);
            SubPartModel = ModelAsMode.ModelAsEntity;
            mInspectorHandler = new InspectorHandler();
            mInspectorHandler.InspectObject = UpdateContent;
            mInspectorHandler.UpdateLayout = UpdateLayout;
            mInspectorHandler.ReadValue = ReadValue;
        }

        public void Initialize(UIManager UIManager)
        {
            base.Initialize(UIManager, "Mesh Separate", mWidth, mHeight);

            mMainPanel = new Panel();
            mMainPanel.Initialize();
            mMainPanel.SetSize(_PanelDialog.GetWidth(), _PanelDialog.GetHeight());
            _PanelDialog.AddChild(mMainPanel);

            mScrollView = new ScrollView();
            mScrollView.Initialize();
            mScrollView.SetBackgroundColor(Color.FromRGBA(30, 30, 30, 255));
            mScrollView.SetPosition(20, 40, mWidth - 40, mHeight - 120);
            mScrollView.GetHScroll().SetEnable(false);
            mMainPanel.AddChild(mScrollView);
            mScrollPanel = mScrollView.GetScrollPanel();

            mRunButton = new Button();
            mRunButton.Initialize();
            mRunButton.SetText("Run");
            mRunButton.SetFontSize(16);
            mRunButton.SetPosition(100, mHeight - 70, 100, 30);
            mRunButton.ClickedEvent += OnRunButtonClicked;
            mMainPanel.AddChild(mRunButton);
        }

        public void SetMesh(MeshAssetDataResource meshData, List<string> matPaths = null)
        {
            Center.x = meshData.MeshDataInfo.MeshCenter.x;
            Center.y = meshData.MeshDataInfo.MeshCenter.z;
            mMeshData = meshData;
            mMatPaths.Clear();
            if (matPaths != null)
            {
                foreach (string path in matPaths)
                    mMatPaths.Add(path);
            }
            UpdateContent();
        }

        public void UpdateContent()
        {
            mInspectors.Clear();
            mScrollPanel.ClearChildren();
            Type tp = GetType();
            PropertyInfo[] properties = tp.GetProperties();
            foreach (PropertyInfo propertyInfo in properties)
            {
                AddPropertyInspector(this, propertyInfo);
            }
            UpdateLayout();
        }

        public void UpdateLayout()
        {
            int width = mScrollView.GetWidth();
            int Y = 0;
            foreach (Inspector inspector in mInspectors)
            {
                inspector.UpdateLayout(width, ref Y);
            }
            if (Y > mScrollView.GetHeight())
            {
                width = mScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                Y = 0;
                foreach (Inspector inspector in mInspectors)
                {
                    inspector.UpdateLayout(width, ref Y);
                }
            }
            int height = Y;
            mScrollPanel.SetSize(width, height);
            mScrollView.UpdateScrollBar();
        }

        void OnRunButtonClicked(Button Sender)
        {
            ModelSplitErrorCode ret = ModelChange.ModelSplit(mMeshData.Path, mMatPaths, Center, BlockSize, SubPartModel);
            if (ret == ModelSplitErrorCode.ERR_OK)
            {
                CloseDialog();
            }
            else
            {
                string err_string = "";
                switch (ret)
                {
                    case ModelSplitErrorCode.ERR_PARAMETERS:
                        err_string = "Parameter Error";
                        break;
                    default:
                        break;
                }
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Model Split Failed", "Error:" + err_string);
            }
        }

        void ReadValue()
        {
            foreach (Inspector inspector in mInspectors)
            {
                inspector.ReadValue();
            }
        }

        void AddPropertyInspector(object obj, PropertyInfo PropertyInfo)
        {
            string PropertyTypeString = PropertyInfo.PropertyType.ToString();
            PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
            if (PropertyInfoAttribute.PropertyType != "")
            {
                PropertyTypeString = PropertyInfoAttribute.PropertyType;
            }
            Type PropertyType = PropertyInfo.PropertyType;
            bool bIsEnum = PropertyType.IsEnum;
            ObjectProperty ObjectProperty = new ObjectProperty();
            ObjectProperty.Object = obj;
            ObjectProperty.Name = PropertyInfo.Name;
            ObjectProperty.Type = PropertyType;
            ObjectProperty.PropertyInfoAttribute = PropertyInfoAttribute;
            ObjectProperty.GetPropertyValueFunction = delegate (object Object, string name, ValueExtraProperty ValueExtraProperty) { return PropertyInfo.GetValue(Object); };
            ObjectProperty.SetPropertyValueFunction = delegate (object Object, string PropertyName, object PropertyValue, SubProperty SubProperty) { PropertyInfo.SetValue(Object, PropertyValue); }; ;
            Inspector Inspector_Property = InspectorManager.GetInstance().CreatePropertyInspector(PropertyTypeString, bIsEnum);
            Inspector_Property.InspectProperty(ObjectProperty);
            Inspector_Property.SetContainer(mScrollPanel);
            Inspector_Property.SetInspectorHandler(mInspectorHandler);
            mInspectors.Add(Inspector_Property);
        }
    }
}
