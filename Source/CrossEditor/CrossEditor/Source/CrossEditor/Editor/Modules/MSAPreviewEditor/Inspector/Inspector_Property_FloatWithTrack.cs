using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_FloatWithTrack : Inspector_Property
    {
        EditWithProgress _Edit;
        Edit _EditValue;
        TrackBar _TrackBar;

        public Inspector_Property_FloatWithTrack()
        {

        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _Edit = new EditWithProgress(Container);
            if (ObjectProperty.ValueMin != decimal.MinValue)
            {
                _Edit.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            }
            else
            {
                _Edit.SetRange(0.0m, 1.0m);
            }
            if (ObjectProperty.ValueStep != decimal.Zero)
            {
                _Edit.SetStep(ObjectProperty.ValueStep);
            }
            else
            {
                _Edit.SetStep(0.01m);
            }
            _Edit.TextChangedEvent += OnEditValueTextChanged;
            _EditValue = _Edit.GetEditValue();

            _TrackBar = new TrackBar();
            _TrackBar.Initialize();
            _TrackBar.SetValue(EditToTrackBar(GetPropertyValue()));
            _TrackBar.ValueChangedEvent += OnTrackBarValueChanged;
            _TrackBar.PaintBackBarEvent += (Sender) =>
            {
                Color Color = Color.EDITOR_UI_SEPARATOR;
                int X = Sender.GetScreenX();
                int Y = Sender.GetScreenY();
                int Width = Sender.GetWidth();
                int Height = Sender.GetHeight();
                GetUIManager().GetGraphics2D().FillRectangle(Color, X, Y + Height / 4, Width, Height / 2);
            };
            Container.AddChild(_TrackBar);

            ReadValue();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 3) / 3;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            int TrackBarWidth = 2 * EditWidth - SPAN_Y;
            _Edit.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_Edit);
            _TrackBar.SetPosition(0, SPAN_Y, TrackBarWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_TrackBar);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                PropertyValueString = PropertyValue.ToString();
            }
            else
            {
                PropertyValueString = "<null>";
            }
            _Edit.SetText(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            object NewValue = null;
            Type Type = _ObjectProperty.Type;
            if (Type == typeof(string))
            {
                NewValue = ValueString;
            }
            else
            {
                NewValue = MathHelper.ParseNumber(ValueString, Type);
                if (NewValue == null)
                {
                    return;
                }
            }
            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            RecordAndWriteValue();
            float Value = EditToTrackBar(GetPropertyValue());
            _TrackBar.SetValue(Value);
        }

        void OnTrackBarValueChanged(TrackBar Sender)
        {
            _Edit.SetText(TrackBarToEdit(Sender.GetValue()));
            _Edit.UpdateProgress();
            OnEditValueTextChanged(_EditValue);
        }

        String TrackBarToEdit(float Value)
        {
            return ((float)_Edit.GetMin() + Value * (float)(_Edit.GetMax() - _Edit.GetMin())).ToString();
        }

        float EditToTrackBar(object Value)
        {
            return ((float)Value - (float)(_Edit.GetMin())) / (float)(_Edit.GetMax() - _Edit.GetMin());
        }
    }
}
