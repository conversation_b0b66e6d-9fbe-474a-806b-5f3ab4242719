using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    public class AnimSectionPrpertyEditorUI : Control, IPropertyEditorUI
    {
        ScrollView _ScrollView;
        Panel _ScrollPanel;

        Menu _Menu;

        LevelSeqAnimationSection _Value;
        List<KeyFrame> _KeyFrames = new List<KeyFrame>();

        Inspector _Inspector;
        InspectorHandler _InspectorHandler;
        SeqAnimSectionTrack _Track;

        public void Initialize(List<KeyFrame> KeyFrames)
        {
            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.GetHScroll().SetEnable(true);
            _ScrollView.SetPosition(10, 50, 430, 430);
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_TEST_COLOR_RED);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_HILIGHT_COLOR_GREEN);

            _KeyFrames = KeyFrames;
            _Track = (SeqAnimSectionTrack)(KeyFrames[0].OwnerTrack);

            _Value = ((SeqAnimSectionKeyFrame)KeyFrames[0]).Section;

            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.UpdateLayout += UpdateLayout;
            _InspectorHandler.InspectObject += () => { _Inspector.InspectObject(_Value); };
            _InspectorHandler.ReadValue += () => { _Inspector.ReadValue(); };

            _Inspector = new Inspector_Struct_With_Property();
            _Inspector.InspectObject(_Value);
            _Inspector.SetContainer(_ScrollPanel);
            _Inspector.SetInspectorHandler(_InspectorHandler);
            _Inspector.SetPropertyModifiedFunction(OnValueModified);
            UpdateLayout();
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollView.GetScrollPanel().SetSize(ScrollPanelWidth, Height);
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public Panel GetScrollPanel()
        {
            return _ScrollPanel;
        }

        public void SetMenu(Menu menu)
        {
            _Menu = menu;
        }
        public Menu GetMenu()
        {
            return _Menu;
        }

        public void OnValueModified(object propertyOwner, PropertyInfo propertyInfo)
        {
            if (_KeyFrames.Count == 0) return;

            LevelSeqAnimationSection property = (LevelSeqAnimationSection)propertyOwner;

            foreach (var KeyFrame in _KeyFrames)
            {
                KeyFrame.SetKeyValue((decimal)property.SectionStart);
                var track = KeyFrame.OwnerTrack as SeqAnimSectionTrack;

                try
                {
                    if (propertyInfo.Name == "AnimSequencePath")
                    {
                        var AnimSeqAsset = Resource.Get(property.AnimSequencePath, false) as PreviewAnimSeqAsset;
                        CompatibleFailInfo CompatibleFailedInfo = new CompatibleFailInfo();
                        if (AnimSeqAsset != null && _Track.PreviewRunSkelt.IsCompatible(AnimSeqAsset, ref CompatibleFailedInfo) != MsaCompatibleType.Success)
                        {
                            throw new Exception("The selected animation is NOT compatible.");
                        }
                        property.SequenceLength = AnimSeqAsset.Duration;
                    }
                    track.ModifyValue((SeqAnimSectionKeyFrame)KeyFrame, property);
                }
                catch (Exception ex)
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Warning", ex.Message);
                }
            }
            CinematicUI.GetInstance().SetModified();
        }
    }
}
