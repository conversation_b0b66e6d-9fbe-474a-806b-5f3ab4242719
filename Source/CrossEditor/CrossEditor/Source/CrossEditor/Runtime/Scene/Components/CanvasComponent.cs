using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    [ComponentAttribute(NeedRenderProperty = true)]
    class CanvasComponent : Component
    {
        string _Path = "";
        int _Width = 1280;
        int _Height = 720;
        int _Layer = 0;
        bool _Visible = true;
        bool _FitWindow = false;
        string _DefaultFontName = "";
        List<string> _UsedFonts = new List<string>();
        CanvasMode _Mode = CanvasMode.ScreenSpace;
        bool _SupportAlphaVideo = true;
        CanvasAlphaPosition _AlphaPosition = CanvasAlphaPosition.Top;
        bool _EnableDebug = false;

        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 6;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        static string[] _NativeNames = { "cross::CanvasComponentG", "cross::AABBComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override void Reset()
        {
            base.Reset();
            Width = 1280;
            Height = 720;
        }

        public override void SyncDataFromEngine()
        {
            _UsedFonts.Clear();
            int Count = CanvasSystemG.GetUseFontsCount(Entity.World.GetNativePointer(), Entity.EntityID);
            for (int i = 0; i < Count; i++)
            {
                _UsedFonts.Add(CanvasSystemG.GetUsedFontPath(Entity.World.GetNativePointer(), Entity.EntityID, i));
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable this canvas.")]
        public override bool Enable
        {
            get { return Visible; }
            set { Visible = value; }
        }

        public CanvasMode Mode
        {
            get
            {
                return CanvasSystemG.GetCanvasMode(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Mode = value;
                CanvasSystemG.SetCanvasMode(Entity.World.GetNativePointer(), Entity.EntityID, _Mode);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Canvas Width.")]
        public int Width
        {
            get
            {
                return CanvasSystemG.GetWidth(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Width = value;
                CanvasSystemG.ResizeView(Entity.World.GetNativePointer(), Entity.EntityID, _Width, Height);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Canvas Height.")]
        public int Height
        {
            get
            {
                return CanvasSystemG.GetHeight(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Height = value;
                CanvasSystemG.ResizeView(Entity.World.GetNativePointer(), Entity.EntityID, Width, _Height);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Canvas Layer.")]
        public int Layer
        {
            get
            {
                return CanvasSystemG.GetLayer(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Layer = value;
                CanvasSystemG.SetLayer(Entity.World.GetNativePointer(), Entity.EntityID, _Layer);
            }
        }


        [PropertyInfo(PropertyType = "StringAsFile", ToolTips = "Canvas path.", FileTypeDescriptor = "Canvas Files#html")]
        public string Path
        {
            get
            {
                return CanvasSystemG.GetPagePath(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Path = value;
                if (_Path.Length != 0)
                {
                    CanvasSystemG.SetPagePath(Entity.World.GetNativePointer(), Entity.EntityID, _Path);
                    RenderPropertySystemG.SetCullingProperty(Entity.World.GetNativePointer(), Entity.EntityID, CullingProperty.CULLING_PROPERTY_ALWAYS_VISIBLE);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Canvas visble.")]
        public bool Visible
        {
            get
            {
                return CanvasSystemG.GetVisible(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Visible = value;
                CanvasSystemG.SetVisible(Entity.World.GetNativePointer(), Entity.EntityID, _Visible);
            }
        }

        [PropertyInfo(PropertyType = "Auto")]
        public bool FitWindow
        {
            get
            {
                return CanvasSystemG.GetFitWindow(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _FitWindow = value;
                CanvasSystemG.SetFitWindow(Entity.World.GetNativePointer(), Entity.EntityID, _FitWindow);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Default FontName.")]
        public string DefaultFontName
        {
            get
            {
                return CanvasSystemG.GetDefaultFontName(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _DefaultFontName = value;
                CanvasSystemG.SetDefaultFontName(Entity.World.GetNativePointer(), Entity.EntityID, _DefaultFontName);

                Reload();
            }
        }

        [PropertyInfo(PropertyType = "List", ChildPropertyType = "StringAsResource", DisplayName = "Used Fonts", FileTypeDescriptor = "#nda", ObjectClassID1 = ClassIDType.CLASS_FontResource)]
        public List<string> UsedFonts
        {
            get
            {
                return _UsedFonts;
            }
            set
            {
                _UsedFonts = value;
                int Count = CanvasSystemG.GetUseFontsCount(Entity.World.GetNativePointer(), Entity.EntityID);
                if (_UsedFonts.Count < Count)
                {
                    for (int i = Count - 1; i >= _UsedFonts.Count; --i)
                    {
                        CanvasSystemG.RemoveUsedFont(Entity.World.GetNativePointer(), Entity.EntityID, i);
                    }
                }

                for (int i = 0; i < _UsedFonts.Count; ++i)
                {
                    CanvasSystemG.SetUsedFontPath(Entity.World.GetNativePointer(), Entity.EntityID, i, _UsedFonts[i]);
                }

                Reload();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Support Video With Alpha Channel.", DisplayName = "Support Alpha Channel")]
        public bool SupportAlphaVideo
        {
            get
            {
                return CanvasSystemG.GetSupportAlphaVideo(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _SupportAlphaVideo = value;
                CanvasSystemG.SetSupportAlphaVideo(Entity.World.GetNativePointer(), Entity.EntityID, _SupportAlphaVideo);
                Reload();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Video Alpha Position.", DisplayName = "Video Alpha Position")]
        public CanvasAlphaPosition AlphaPosition
        {
            get
            {
                return CanvasSystemG.GetAlphaPosition(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _AlphaPosition = value;
                CanvasSystemG.SetAlphaPosition(Entity.World.GetNativePointer(), Entity.EntityID, _AlphaPosition);
                Reload();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Video Alpha Position.", DisplayName = "Enable Debug")]
        public bool EnableDebug
        {
            get
            {
                return CanvasSystemG.GetEnableDebug(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _EnableDebug = value;
                CanvasSystemG.SetEnableDebug(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        public void Reload()
        {
            CanvasSystemG.SetPagePath(Entity.World.GetNativePointer(), Entity.EntityID, Path);
        }
    }
}
