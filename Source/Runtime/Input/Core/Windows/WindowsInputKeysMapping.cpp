#include "EnginePrefix.h"
#include "Runtime/Input/Core/Windows/WindowsInputKeysMapping.h"
#include "Runtime/Input/Core/InputKeys.h"

#include <Windows.h>

namespace cross::input
{
    UInt32 WindowsInputKeysMapping::AnyKeyCode = 0xFF;

    UInt32 WindowsInputKeysMapping::GetPlatformKeyMap(UInt32* outKeyCodes, UniqueString* outKeyNames, UInt32 maxMappings)
    {
        UInt32 numMappings = 0;

#define LOCAL_KEY_TEXT(v) v

#define ADDKEYMAP(KeyCode, KeyName)         \
    if (numMappings < maxMappings)          \
    {                                       \
        outKeyCodes[numMappings] = KeyCode; \
        outKeyNames[numMappings] = KeyName; \
        ++numMappings;                      \
    };

        if (outKeyCodes && outKeyNames && maxMappings > 0) 
        {
            ADDKEYMAP(0xFF,         LOCAL_KEY_TEXT(CEKeys::AnyKey))

            ADDKEYMAP(VK_LBUTTON,   LOCAL_KEY_TEXT(CEKeys::LeftMouseButton));
            ADDKEYMAP(VK_RBUTTON,   LOCAL_KEY_TEXT(CEKeys::RightMouseButton));
            ADDKEYMAP(VK_MBUTTON,   LOCAL_KEY_TEXT(CEKeys::MiddleMouseButton));

            ADDKEYMAP(VK_XBUTTON1,  LOCAL_KEY_TEXT(CEKeys::ThumbMouseButton));
            ADDKEYMAP(VK_XBUTTON2,  LOCAL_KEY_TEXT(CEKeys::ThumbMouseButton2));
    
            ADDKEYMAP(VK_BACK,      LOCAL_KEY_TEXT(CEKeys::BackSpace));
            ADDKEYMAP(VK_TAB,       LOCAL_KEY_TEXT(CEKeys::Tab));
            ADDKEYMAP(VK_RETURN,    LOCAL_KEY_TEXT(CEKeys::Enter));
            ADDKEYMAP(VK_PAUSE,     LOCAL_KEY_TEXT(CEKeys::Pause));

            ADDKEYMAP(VK_CAPITAL,   LOCAL_KEY_TEXT(CEKeys::CapsLock));
            ADDKEYMAP(VK_ESCAPE,    LOCAL_KEY_TEXT(CEKeys::Escape));
            ADDKEYMAP(VK_SPACE,     LOCAL_KEY_TEXT(CEKeys::SpaceBar));
            ADDKEYMAP(VK_PRIOR,     LOCAL_KEY_TEXT(CEKeys::PageUp));
            ADDKEYMAP(VK_NEXT,      LOCAL_KEY_TEXT(CEKeys::PageDown));
            ADDKEYMAP(VK_END,       LOCAL_KEY_TEXT(CEKeys::End));
            ADDKEYMAP(VK_HOME,      LOCAL_KEY_TEXT(CEKeys::Home));
            
            ADDKEYMAP(VK_LEFT,      LOCAL_KEY_TEXT(CEKeys::Left));
            ADDKEYMAP(VK_UP,        LOCAL_KEY_TEXT(CEKeys::Up));
            ADDKEYMAP(VK_RIGHT,     LOCAL_KEY_TEXT(CEKeys::Right));
            ADDKEYMAP(VK_DOWN,      LOCAL_KEY_TEXT(CEKeys::Down));
                      
            ADDKEYMAP(VK_INSERT,    LOCAL_KEY_TEXT(CEKeys::Insert));
            ADDKEYMAP(VK_DELETE,    LOCAL_KEY_TEXT(CEKeys::Delete));
                              
            ADDKEYMAP(VK_NUMPAD0,   LOCAL_KEY_TEXT(CEKeys::NumPadZero));
            ADDKEYMAP(VK_NUMPAD1,   LOCAL_KEY_TEXT(CEKeys::NumPadOne));
            ADDKEYMAP(VK_NUMPAD2,   LOCAL_KEY_TEXT(CEKeys::NumPadTwo));
            ADDKEYMAP(VK_NUMPAD3,   LOCAL_KEY_TEXT(CEKeys::NumPadThree));
            ADDKEYMAP(VK_NUMPAD4,   LOCAL_KEY_TEXT(CEKeys::NumPadFour));
            ADDKEYMAP(VK_NUMPAD5,   LOCAL_KEY_TEXT(CEKeys::NumPadFive));
            ADDKEYMAP(VK_NUMPAD6,   LOCAL_KEY_TEXT(CEKeys::NumPadSix));
            ADDKEYMAP(VK_NUMPAD7,   LOCAL_KEY_TEXT(CEKeys::NumPadSeven));
            ADDKEYMAP(VK_NUMPAD8,   LOCAL_KEY_TEXT(CEKeys::NumPadEight));
            ADDKEYMAP(VK_NUMPAD9,   LOCAL_KEY_TEXT(CEKeys::NumPadNine));
                      
            ADDKEYMAP(VK_MULTIPLY,  LOCAL_KEY_TEXT(CEKeys::Multiply));
            ADDKEYMAP(VK_ADD,       LOCAL_KEY_TEXT(CEKeys::Add));
            ADDKEYMAP(VK_SUBTRACT,  LOCAL_KEY_TEXT(CEKeys::Subtract));
            ADDKEYMAP(VK_DECIMAL,   LOCAL_KEY_TEXT(CEKeys::Decimal));
            ADDKEYMAP(VK_DIVIDE,    LOCAL_KEY_TEXT(CEKeys::Divide));
                      
            ADDKEYMAP(VK_F1,        LOCAL_KEY_TEXT(CEKeys::F1));
            ADDKEYMAP(VK_F2,        LOCAL_KEY_TEXT(CEKeys::F2));
            ADDKEYMAP(VK_F3,        LOCAL_KEY_TEXT(CEKeys::F3));
            ADDKEYMAP(VK_F4,        LOCAL_KEY_TEXT(CEKeys::F4));
            ADDKEYMAP(VK_F5,        LOCAL_KEY_TEXT(CEKeys::F5));
            ADDKEYMAP(VK_F6,        LOCAL_KEY_TEXT(CEKeys::F6));
            ADDKEYMAP(VK_F7,        LOCAL_KEY_TEXT(CEKeys::F7));
            ADDKEYMAP(VK_F8,        LOCAL_KEY_TEXT(CEKeys::F8));
            ADDKEYMAP(VK_F9,        LOCAL_KEY_TEXT(CEKeys::F9));
            ADDKEYMAP(VK_F10,       LOCAL_KEY_TEXT(CEKeys::F10));
            ADDKEYMAP(VK_F11,       LOCAL_KEY_TEXT(CEKeys::F11));
            ADDKEYMAP(VK_F12,       LOCAL_KEY_TEXT(CEKeys::F12));
                       
            ADDKEYMAP(VK_NUMLOCK,   LOCAL_KEY_TEXT(CEKeys::NumLock));
                      
            ADDKEYMAP(VK_SCROLL,    LOCAL_KEY_TEXT(CEKeys::ScrollLock));
                                    
            ADDKEYMAP(VK_LSHIFT,    LOCAL_KEY_TEXT(CEKeys::LeftShift));
            ADDKEYMAP(VK_RSHIFT,    LOCAL_KEY_TEXT(CEKeys::RightShift));
            ADDKEYMAP(VK_LCONTROL,  LOCAL_KEY_TEXT(CEKeys::LeftControl));
            ADDKEYMAP(VK_RCONTROL,  LOCAL_KEY_TEXT(CEKeys::RightControl));
            ADDKEYMAP(VK_LMENU,     LOCAL_KEY_TEXT(CEKeys::LeftAlt));
            ADDKEYMAP(VK_RMENU,     LOCAL_KEY_TEXT(CEKeys::RightAlt));
            ADDKEYMAP(VK_LWIN,      LOCAL_KEY_TEXT(CEKeys::LeftCommand));
            ADDKEYMAP(VK_RWIN,      LOCAL_KEY_TEXT(CEKeys::RightCommand));
            ADDKEYMAP(VK_OEM_2,     LOCAL_KEY_TEXT(CEKeys::Slash));   // '/?' for US
            ADDKEYMAP(VK_OEM_3,     LOCAL_KEY_TEXT(CEKeys::Tilde));
        }

        return numMappings;

#undef ADDKEYMAP

#undef LOCAL_KEY_TEXT
    }

    UInt32 WindowsInputKeysMapping::GetCharKeyMap(UInt32* outKeyCodes, UniqueString* outKeyNames, UInt32 maxMappings) 
    {
        return GenericInputKeysMapping::GetAsciiPrintableKeyMap(outKeyCodes, outKeyNames, maxMappings, true, false);
    }
}