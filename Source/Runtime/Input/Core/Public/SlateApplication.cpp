#include "SlateApplication.h"
#include "WidgetRouter.h"
#include "Runtime/Input/Core/WidgetPathHelper.h"
#include "Runtime/Input/Core/GameViewPortWidget.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/RendererSystemG.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "Runtime/GameWorld/ScreenTerminalSystemG.h"
#include "Runtime/Input/InputManager.h"
#include "Runtime/Input/Core/Public/PlatformInputMethodManager.h"

namespace cross {

void CrossEngineDeleter::operator()(ICrossEngine* crossEngine)
{
    if (crossEngine)
    {
        DestroyCrossEngine(crossEngine);
    }
}

const UserHandle SlateApplication::mCursorUserIndex = {0};

SlateApplication* SlateApplication::Instance()
{
    static SlateApplication sInstance;
    return &sInstance;
}

void SlateApplication::slateDrawText(const std::string& str) 
{
    auto gameWorldSystem = EngineGlobal::Inst().GetEngine()->GetGlobalSystem<WorldSystemG>();
    auto gameWorld = gameWorldSystem->GetWorldsList()[0];
    auto ScreenTerminalSys = gameWorld->GetGameSystem<ScreenTerminalSystemG>();

    //if (ScreenTerminalSys && !ScreenTerminalSys->GetTerminalState()) 
    //{
    //    ScreenTerminalSys->SetTerminalState(true);
    //}
    if (ScreenTerminalSys /*&& ScreenTerminalSys->GetTerminalState()*/)
    {
        ScreenTerminalSys->AddLog(str);
    }
}

bool SlateApplication::OnKeyChar(const char character, const bool isRepeat, UserHandle userHandle)
{
    UInt32 modifier = mPlatformApplication->ModifierStateToKeyCode();
    input::CharEvent event(userHandle, character, mPlatformApplication->GetModifierKeys(), modifier, isRepeat);
    return ProcessKeyCharEvent(event);
}

bool SlateApplication::OnKeyDown(const SInt32 keyCode, const UInt32 charCode, const bool isRepeat, UserHandle userHandle)
{
    auto keyPtr = input::CEInputKeyManager::Get().GetKeyFromCode(keyCode, charCode);
    input::KeyEvent event(userHandle, keyPtr == nullptr ? *input::CEInputKeyManager::Get().AnyKey() : *keyPtr, mPlatformApplication->GetModifierKeys(), isRepeat);
    return ProcessKeyDownEvent(event);
}

bool SlateApplication::OnKeyUp(const SInt32 keyCode, const UInt32 charCode, const bool isRepeat, UserHandle userHandle)
{
    auto keyPtr = input::CEInputKeyManager::Get().GetKeyFromCode(keyCode, charCode);
    input::KeyEvent event(userHandle, keyPtr == nullptr ? *input::CEInputKeyManager::Get().AnyKey() : *keyPtr, mPlatformApplication->GetModifierKeys(), isRepeat);

    return ProcessKeyUpEvent(event);
}

bool SlateApplication::OnMouseDown(const input::CEMouseButton::Type button, const Float2 cursorPos, UserHandle userHandle)
{
    if (InputSetting::GetInputSetting().ShowDebugInfo)
        LOG_DEBUG("SlateApp Mouse DOWN, x={}, y={}", cursorPos.x, cursorPos.y);

    if (mEnableFakingTouch && button == input::CEMouseButton::Left)
    {
        mIsFakingTouched = true;
        return OnTouchStarted(cursorPos, 1.0f, 0, userHandle);
    }

    auto user = GetOrCreateUser(userHandle);

    auto& key = input::CEKeys::TranslateMouseButtonToKey(button);
    input::PointerEvent event(userHandle, key, mPressedMouseButtons, mPlatformApplication->GetModifierKeys(), cursorPos, user->GetPreviousCursorPosition());

    return ProcessPointerButtonDownEvent(event);
}

bool SlateApplication::OnMouseUp(const input::CEMouseButton::Type button, const Float2 cursorPos, UserHandle userHandle)
{
    if (InputSetting::GetInputSetting().ShowDebugInfo)
        LOG_DEBUG("SlateApp Mouse UP, x={}, y={}", cursorPos.x, cursorPos.y);

    if (mEnableFakingTouch && button == input::CEMouseButton::Left)
    {
        mIsFakingTouched = false;
        return OnTouchEnded(cursorPos, 0, userHandle);
    }

    auto user = GetOrCreateUser(userHandle);

    auto& key = input::CEKeys::TranslateMouseButtonToKey(button);
    input::PointerEvent event(userHandle, key, mPressedMouseButtons, mPlatformApplication->GetModifierKeys(), cursorPos, user->GetPreviousCursorPosition());

    return ProcessPointerButtonUpEvent(event);
}

bool SlateApplication::OnMouseDoubleClick(const input::CEMouseButton::Type button, const Float2 cursorPos, UserHandle userHandle)
{
    // LOG_INFO("--Mouse Double Click -- {} {}", cursorPos.x, cursorPos.y);

    return OnMouseDown(button, cursorPos, userHandle);
}

bool SlateApplication::OnMouseWheel(const float delta, const Float2 cursorPos, UserHandle userHandle)
{
    auto user = GetOrCreateUser(userHandle);
    input::PointerEvent event(userHandle, input::CEKeys::AnyKey, mPressedMouseButtons, mPlatformApplication->GetModifierKeys(), cursorPos, user->GetPreviousCursorPosition());
    event.WheelDelta = delta;

    return ProcessMouseWheelOrGestureEvent(event, nullptr);
}

bool SlateApplication::OnMouseMove(UserHandle userHandle)
{
    if (mIsFakingTouched)
    {
        return OnTouchMoved(mPlatformApplication->GetCursor()->GetPosition(), 1.0f, 0, userHandle);
    }

    auto user = GetOrCreateUser(userHandle);
    input::PointerMoveEvent mouseEvent(userHandle, mPlatformApplication->GetModifierKeys(), mPressedMouseButtons, mPlatformApplication->GetCursor()->GetPosition(), user->GetPreviousCursorPosition(), Float2(0, 0));
    return ProcessPointerMoveEvent(mouseEvent);
}

bool SlateApplication::OnRawMouseMove(const SInt32 x, const SInt32 y, UserHandle userHandle)
{
    // LOG_INFO("--Mouse Raw MOVE -- {} {}", x, y);

    if (x != 0 || y != 0)
    {
        auto user = GetOrCreateUser(userHandle);
        input::PointerMoveEvent mouseEvent(userHandle, mPlatformApplication->GetModifierKeys(), mPressedMouseButtons, mPlatformApplication->GetCursor()->GetPosition(), user->GetPreviousCursorPosition(), Float2(x * 1.f, y * 1.f));
        return ProcessPointerMoveEvent(mouseEvent);
    }

    return false;
}

bool SlateApplication::OnRawMouseMove(const SInt32 cursorX, const SInt32 cursorY, const SInt32 deltaX, const SInt32 deltaY, UserHandle userHandle)
{
    if (deltaX != 0 || deltaY != 0)
    {
        auto user = GetOrCreateUser(userHandle);
        input::PointerMoveEvent mouseEvent(userHandle, mPlatformApplication->GetModifierKeys(), mPressedMouseButtons, Float2(cursorX * 1.f, cursorY * 1.f), user->GetPreviousCursorPosition(), Float2(deltaX * 1.f, deltaY * 1.f));
        return ProcessPointerMoveEvent(mouseEvent);
    }

    return false;
}

bool SlateApplication::OnTouchStarted(const Float2& location, float force, UInt32 touchIndex, UserHandle userHandle)
{
    if (touchIndex >= static_cast<UInt32>(input::CETouchIndex::CursorPointerIndex))
    {
        return false;
    }

    auto& touchKey = input::CEKeys::TranslatePointerIndexToKey(touchIndex);

    input::PointerEvent pointerEvent(userHandle, touchKey, mPressedMouseButtons, cross::input ::CEModifierKeyStates(), location, location, force);
    GetOrCreateUser(userHandle)->NotifyTouchStarted(pointerEvent);

    if (InputSetting::GetInputSetting().ShowDebugInfo)
        LOG_DEBUG("SlateApp touch start, index={}, x={}, y={}", touchIndex, location.x, location.y);

    return ProcessPointerButtonDownEvent(pointerEvent);
}

bool SlateApplication::OnTouchMoved(const Float2& location, float force, UInt32 touchIndex, UserHandle userHandle)
{
    auto user = GetOrCreateUser(userHandle);
    PointerHandle pointerHandle{static_cast<UInt32>(touchIndex)};

    if (user->IsTouchPointerActive(pointerHandle))
    {
        input::PointerMoveEvent pointerMoveEvent(userHandle, pointerHandle, mPressedMouseButtons, cross::input::CEModifierKeyStates(), location, user->GetPreviousPointerPosition(pointerHandle), force);
        // LOG_INFO("SlateApp touch move, index={}, x={}, y={}", touchIndex, location.x, location.y);
        return ProcessPointerMoveEvent(pointerMoveEvent);
    }

    return false;
}

bool SlateApplication::OnTouchEnded(const Float2& location, UInt32 touchIndex, UserHandle userHandle)
{
    auto user = GetOrCreateUser(userHandle);
    PointerHandle pointerHandle{static_cast<UInt32>(touchIndex)};

    if (user->IsTouchPointerActive(pointerHandle))
    {
        auto& touchKey = input::CEKeys::TranslatePointerIndexToKey(touchIndex);

        input::PointerEvent pointerEvent(userHandle, touchKey, mPressedMouseButtons, cross::input ::CEModifierKeyStates(), location, location, 0.f);

        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("SlateApp touch end, index={}, x={}, y={}", touchIndex, location.x, location.y);

        return ProcessPointerButtonUpEvent(pointerEvent);
    }

    return false;
}

bool SlateApplication::OnTouchForceChanged(const Float2& location, float force, UInt32 touchIndex, UserHandle userHandle)
{
    auto user = GetOrCreateUser(userHandle);
    PointerHandle pointerHandle{static_cast<UInt32>(touchIndex)};

    if (user->IsTouchPointerActive(pointerHandle))
    {
        input::PointerMoveEvent pointerMoveEvent(userHandle, pointerHandle, mPressedMouseButtons, cross::input::CEModifierKeyStates(), location, location, force, true, false);

        return ProcessPointerMoveEvent(pointerMoveEvent);
    }

    return false;
}

bool SlateApplication::OnTouchFirstMove(const Float2& location, float force, UInt32 touchIndex, UserHandle userHandle)
{
    auto user = GetOrCreateUser(userHandle);
    PointerHandle pointerHandle{static_cast<UInt32>(touchIndex)};

    if (user->IsTouchPointerActive(pointerHandle))
    {
        input::PointerMoveEvent pointerMoveEvent(userHandle, pointerHandle, mPressedMouseButtons, cross::input::CEModifierKeyStates(), location, user->GetPreviousPointerPosition(pointerHandle), force, false, true);

        return ProcessPointerMoveEvent(pointerMoveEvent);
    }

    return false;
}

void SlateApplication::ShowHideCursor(bool bShow)
{
    mPlatformApplication->GetCursor()->Show(bShow);
}

bool SlateApplication::ProcessKeyCharEvent(const input::CharEvent& iCharEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto user = GetOrCreateUser(iCharEvent.User);
    
    // Bubble the keyboard with pointer path assist
    const auto& keyboardPath = user->GetFocusPath();
    const auto* pointerPath = user->GetCurWidgetsUnderCursor();

    input::CEReply reply = input::CEReply::Unhandled();
    reply = WidgetRouter::RouteAlongFocusPathReversely(pointerPath, keyboardPath, iCharEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::CharEvent& charEvent) {
        const input::CEReply tempReply = arrangeWidget->OnKeyChar(charEvent);
        return tempReply;
    });

    return reply.IsHandled();
}

bool SlateApplication::ProcessKeyDownEvent(const input::KeyEvent& inKeyDownEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto user = GetOrCreateUser(inKeyDownEvent.User);

    // Bubble the keyboard with pointer path assist
    const auto& keyboardPath = user->GetFocusPath();
    const auto* pointerPath = user->GetCurWidgetsUnderCursor();

    input::CEReply reply = input::CEReply::Unhandled();

    // Tunnel the key down event, root -- leaf
    reply = WidgetRouter::RouteAlongFocusPath(pointerPath, keyboardPath, inKeyDownEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::KeyEvent& keyDownEvent) 
    {
        const input::CEReply tempReply = arrangeWidget->OnPreviewKeyDown(keyDownEvent);
        return tempReply;
    });

    // Bubble the key down events if no preview handler consume the key event. leaf -- root
    if (!reply.IsHandled())
    {
        reply = WidgetRouter::RouteAlongFocusPathReversely(pointerPath, keyboardPath, inKeyDownEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::KeyEvent& keyDownEvent) 
        {
            const input::CEReply tempReply = arrangeWidget->OnKeyDown(keyDownEvent);
            return tempReply;
        });
    }

    return reply.IsHandled();
}

bool SlateApplication::ProcessKeyUpEvent(const input::KeyEvent& inKeyUpEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto user = GetOrCreateUser(inKeyUpEvent.User);

    // Bubble the keyboard with pointer path assist
    const auto& keyboardPath = user->GetFocusPath();
    const auto* pointerPath = user->GetCurWidgetsUnderCursor();

    input::CEReply reply = input::CEReply::Unhandled();
    reply = WidgetRouter::RouteAlongFocusPathReversely(pointerPath, keyboardPath, inKeyUpEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::KeyEvent& keyDownEvent) 
    {
        const input::CEReply tempReply = arrangeWidget->OnKeyUp(keyDownEvent);
        return tempReply;
    });

    return reply.IsHandled();
}

bool SlateApplication::ProcessAnalogInputEvent(const input::AnalogInputEvent& inAnalogInputEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto user = GetOrCreateUser(inAnalogInputEvent.User);
    const auto& keyboardPath = user->GetFocusPath();
    const auto* pointerPath = user->GetCurWidgetsUnderCursor();

    input::CEReply reply = input::CEReply::Unhandled();
    reply = WidgetRouter::RouteAlongFocusPathReversely(pointerPath, keyboardPath, inAnalogInputEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::AnalogInputEvent& analogEvent) {
        const input::CEReply tempReply = arrangeWidget->OnAnalogValueChanged(analogEvent);
        return tempReply;
    });

    return reply.IsHandled();
}

bool SlateApplication::ProcessPointerButtonDownEvent(const input::PointerEvent& inPointerEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    // Only process mouse down messages if we are not drag/dropping
    auto userPtr = GetOrCreateUser(inPointerEvent.User);

    if (inPointerEvent.User == mCursorUserIndex)
    {
        mPressedMouseButtons.insert(inPointerEvent.Key);
    }

    input::CEReply reply = input::CEReply::Unhandled();

    // Capture path of user's mouse pointer
    if (!userPtr->HasCapture(inPointerEvent.PointerIndex))
    {
        auto path = cross::WidgetPathHelper::LocateWidgetsUnderPointer(inPointerEvent.ScreenSpacePosition, {mPlatformApplication->GetPrimaryViewPortWidget()}, *userPtr, inPointerEvent.PointerIndex);
        userPtr->SetPointerCaptor(path, inPointerEvent.PointerIndex);
    }

    // Capture path of user's  any pointer
    if (!userPtr->HasCapture({ input::CETouchIndex::CursorPointerIndex })) 
    {
        auto path = cross::WidgetPathHelper::LocateWidgetsUnderPointer(inPointerEvent.ScreenSpacePosition, {mPlatformApplication->GetPrimaryViewPortWidget()}, *userPtr, inPointerEvent.PointerIndex);
        userPtr->SetPointerCaptor(path, {input::CETouchIndex::CursorPointerIndex});    
    }

    // Current user has capture widget
    if (userPtr->HasCapture(inPointerEvent.PointerIndex))
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("SlateApp pointer down, index={}, capture=true", inPointerEvent.PointerIndex.mVal);

        auto mouseCaptorPath = userPtr->GetCaptorPath(inPointerEvent.PointerIndex);

        // Tunnel the key down event, root -- leaf
        reply = WidgetRouter::RouteAlongFocusPath(&mouseCaptorPath, mouseCaptorPath, inPointerEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::PointerEvent& pointerEvent) 
        {
            const input::CEReply tempReply = arrangeWidget->OnPreviewPointerButtonDown(pointerEvent);
            return tempReply;
        });

        // Bubble the key down events if no preview handler consume the key event. leaf -- root
        if (!reply.IsHandled())
        {
            reply = WidgetRouter::RouteAlongFocusPathReversely(&mouseCaptorPath, mouseCaptorPath, inPointerEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::PointerEvent& pointerEvent) 
            {
                const input::CEReply tempReply = arrangeWidget->OnPointerButtonDown(pointerEvent);
                return tempReply;
            });
        }

        return reply.IsHandled();
    }

    return false;
}

bool SlateApplication::ProcessPointerButtonUpEvent(const input::PointerEvent& inPointerEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto userPtr = GetOrCreateUser(inPointerEvent.User);

    if (inPointerEvent.User == mCursorUserIndex)
    {
        mPressedMouseButtons.erase(inPointerEvent.Key);
    }

    input::CEReply reply = input::CEReply::Unhandled();
    // Current user has capture widget
    if (userPtr->HasCapture(inPointerEvent.PointerIndex))
    {
        // Bubble the keyboard with pointer path assist
        auto pointerPath = userPtr->GetCaptorPath(inPointerEvent.PointerIndex);

        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("SlateApp pointer up, index={}, capture=true", inPointerEvent.PointerIndex.mVal);

        reply = WidgetRouter::RouteAlongFocusPathReversely(&pointerPath, pointerPath, inPointerEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::PointerEvent& pointerEvent) 
        {
            const input::CEReply tempReply = arrangeWidget->OnPointerButtonUp(pointerEvent);
            return tempReply;
        });

        userPtr->NotifyPointerReleased(inPointerEvent);
    }
    else
    {
        if (InputSetting::GetInputSetting().ShowDebugInfo)
            LOG_DEBUG("SlateApp pointer up, index={}, capture=false", inPointerEvent.PointerIndex.mVal);

        auto pointerPath = WidgetPathHelper::LocateWidgetsUnderPointer(inPointerEvent.ScreenSpacePosition, {mPlatformApplication->GetPrimaryViewPortWidget()}, *userPtr, inPointerEvent.PointerIndex);
        Assert(pointerPath.IsEmpty() == false);

        // handle drag drop events while cursor move in from other view port
    }

    return reply.IsHandled();
}

bool SlateApplication::ProcessPointerMoveEvent(const input::PointerMoveEvent& inPointerEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    auto userPtr = GetOrCreateUser(inPointerEvent.User);

    // Get pre widget path & root record in user
    auto oldPointerWidgetPath = userPtr->GetCaptorPath(inPointerEvent.PointerIndex);
    auto oldPointerTop = oldPointerWidgetPath.IsEmpty() ? nullptr : oldPointerWidgetPath.GetFirstWidget();

    // Get cur widget path & root from current pointer event
    auto newPointerWidgetPath = WidgetPathHelper::LocateWidgetsUnderPointer(inPointerEvent.ScreenSpacePosition, {mPlatformApplication->GetPrimaryViewPortWidget()}, *userPtr, inPointerEvent.PointerIndex);
    auto newPointerTop = newPointerWidgetPath.IsEmpty() ? nullptr : newPointerWidgetPath.GetFirstWidget();
    // LOG_INFO("SlateApp pointer move, index={}", inPointerEvent.PointerIndex.mVal);
    // Send out mouse/touch leave events in the very beginning for previous path
    // If we are doing a drag and drop, we will send this event instead.
    if (!oldPointerWidgetPath.IsEmpty())
    {
        WidgetRouter::RouteAlongFocusPathReversely(
            &oldPointerWidgetPath, oldPointerWidgetPath, inPointerEvent, [&newPointerWidgetPath, &inPointerEvent](std::shared_ptr<ISequenceableWidget> someWidgetPreviouslyUnderCursor, const input::PointerEvent& pointerEvent) {
                Assert(someWidgetPreviouslyUnderCursor != nullptr);
                if (!newPointerWidgetPath.FindArrangedWidget(someWidgetPreviouslyUnderCursor))
                {
                    // Note that the event's pointer position is not translated.
                    someWidgetPreviouslyUnderCursor->OnPointerLeave(inPointerEvent);
                }

                return input::CEReply::Unhandled();
            });
    }

    if (!newPointerWidgetPath.IsEmpty())
    {
        // Send out mouse/touch enter events then
        WidgetRouter::RouteAlongFocusPath(
            &newPointerWidgetPath, newPointerWidgetPath, inPointerEvent, [&oldPointerWidgetPath, &inPointerEvent](std::shared_ptr<ISequenceableWidget> someWidgetUnderCursor, const input::PointerEvent& pointerEvent) 
            {
                Assert(someWidgetUnderCursor != nullptr);
                if (!oldPointerWidgetPath.FindArrangedWidget(someWidgetUnderCursor))
                {
                    // Note that the event's pointer position is not translated.
                    someWidgetUnderCursor->OnPointerEnter(inPointerEvent);
                }

                return input::CEReply::Unhandled();
            });

        // Send out mouse/touch move events finally
        auto reply = WidgetRouter::RouteAlongFocusPathReversely(
            &newPointerWidgetPath, newPointerWidgetPath, inPointerEvent, [&oldPointerWidgetPath, &inPointerEvent](std::shared_ptr<ISequenceableWidget> someWidgetUnderCursor, const input::PointerEvent& pointerEvent) 
            {
                Assert(someWidgetUnderCursor != nullptr);
                input::CEReply tempReply = input::CEReply::Unhandled();

                // Note that the event's pointer position is not translated.
                tempReply = someWidgetUnderCursor->OnPointerMove(inPointerEvent);
                return tempReply;
            });
        return reply.IsHandled();
    }

    return false;
}

bool SlateApplication::ProcessMouseWheelOrGestureEvent(const input::PointerEvent& InWheelEvent, const input::PointerEvent* InGestureEvent)
{
    SetLastUserInteractionTime(EngineGlobal::GetEngine()->GetRealTimeSeconds());

    // Only process mouse down messages if we are not drag/dropping
    auto userPtr = GetOrCreateUser(InWheelEvent.User);

    input::CEReply reply = input::CEReply::Unhandled();

    // Current user has capture widget
    if (userPtr->HasCapture(InWheelEvent.PointerIndex))
    {
        auto mouseCaptorPath = userPtr->GetCaptorPath(InWheelEvent.PointerIndex);

        reply = WidgetRouter::RouteAlongFocusPathReversely(&mouseCaptorPath, mouseCaptorPath, InWheelEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::PointerEvent& pointerEvent)
            {
                const input::CEReply tempReply = arrangeWidget->OnPointerWheel(pointerEvent);
                return tempReply;
            });

        return reply.IsHandled();
    }
    // Capture path of user's mouse pointer
    else
    {
        auto path = cross::WidgetPathHelper::LocateWidgetsUnderPointer(InWheelEvent.ScreenSpacePosition, { mPlatformApplication->GetPrimaryViewPortWidget() }, *userPtr, InWheelEvent.PointerIndex);
        userPtr->SetPointerCaptor(path, InWheelEvent.PointerIndex);

        return true;
    }

    return false;
}

void SlateApplication::SetLastUserInteractionTime(const double inCurrentTime)
{
    if (mLastUserInteractionTime != inCurrentTime)
    {
        mLastUserInteractionTime = inCurrentTime;

        // todo, broadcast in the future
        // LastUserInteractionTimeUpdateEvent.Broadcast(LastUserInteractionTime);
    }
}

// TODO(hendrikwang): Should SlateApplication take control of the shutdown behaviour of the engine?
void SlateApplication::OnWindowClose()
{
    // Destroy window
    auto* crossEngine = EngineGlobal::GetEngine();
    if (crossEngine)
    {
        crossEngine->ShutDown();
    }
}

void SlateApplication::OnApplicationActivationChanged(const bool active)
{
    // Cancel tooltips, menu, drag / drop, etc.
    return;
}

void SlateApplication::OnWindowActivationChanged(CEWindowActivation const& activationType)
{
    // LOG_INFO("--Application Activation Changed -- {}", activationType);

    // Window being ACTIVATED
    if (activationType != CEWindowActivation::Deactivate)
    {
        mIsPlatformWindowActive = true;

        auto user = GetOrCreateUser({0});
        auto viewportWidget = user->GetGameViewport();

        //user->ReleaseAllCapture();

        if (viewportWidget)
        {
            SequenceableWidgetPath pathToViewport;
            if (WidgetPathHelper::FindPathToWidget(viewportWidget, pathToViewport, Visibility::All()))
            {
                input::WindowActivateEvent activateEvent({0}, activationType);
                input::CEReply viewportActivatedReply = viewportWidget->OnOSWindowActivated(activateEvent);
                if (viewportActivatedReply.IsHandled())
                {
                    ProcessReply(user->GetUserHandle(), pathToViewport, activateEvent, viewportActivatedReply);
                }
            }
        }

        auto* inputMethodManager = mPlatformApplication->GetInputMethodManager();
        if (inputMethodManager != nullptr)
        {
            inputMethodManager->ApplyDefaultInputMethod(mPlatformApplication);
        }
    }
    // Window being DEACTIVATED
    else
    {
        mIsPlatformWindowActive = false;

        // A window was deactivated; keyboard capture should be cleared
        const auto* user = GetOrCreateUser({0});
        const auto* pointerPath = user->GetCurWidgetsUnderCursor();
        const auto& keyboardPath = user->GetFocusPath();

        input::WindowActivateEvent activateEvent({0}, activationType);

        auto reply = WidgetRouter::RouteAlongFocusPath(pointerPath, keyboardPath, activateEvent, [](std::shared_ptr<ISequenceableWidget> arrangeWidget, const input::WindowActivateEvent& activateEvent) {
            arrangeWidget->OnOSWindowDeactivated(activateEvent);
            return input::CEReply::Unhandled();
        });

        mPressedMouseButtons.clear();

        // A window was deactivated; mouse capture should be cleared
        ResetToDefaultPointerInputSettings();
    }

    mPlatformApplication->OnViewPortWidgetActivatedChanged(IsActive());
}

void SlateApplication::SetUserFocusToGameViewport(UserHandle userHandle, FocusCause::Type reasonFocusIsChanging /* = FocusCause::Type::SetDirectly*/)
{
    SequenceableWidgetPath path;

    if (!WidgetPathHelper::FindPathToWidget(mPlatformApplication->GetPrimaryViewPortWidget(), path))
    {
        LOG_WARN("Focus main gameport failed for no valid path exists.");
        return;
    }

    SetUserFocus(userHandle, path, reasonFocusIsChanging);
}

bool SlateApplication::SetUserFocus(UserHandle userHandle, const SequenceableWidgetPath& inFocusPath, const FocusCause::Type inCause)
{
    auto* user = GetUser(userHandle);

    if (inFocusPath.IsEmpty() == false)
    {
        auto topWidget = inFocusPath.GetFirstWidget();

        if (mPlatformApplication->GetPrimaryViewPortWidget() != topWidget)
        {
            LOG_ERROR("Ignoring SetUserFocus because it's not an active modal Window :user %i not set to %s.", (SInt32)userHandle, topWidget->Name().GetCString());
            return false;
        }
    }

    // Get the old Widget information
    const auto& oldFocusedWidgetPath = user->GetFocusPath();
    const auto& oldFocusedWidget = oldFocusedWidgetPath.IsEmpty() ? nullptr : oldFocusedWidgetPath.GetLastWidget();

    // Get the new widget information by finding the first widget in the path that supports focus
    SequenceableWidgetPath newFocusedWidgetPath;
    std::shared_ptr<SequenceableWidget> newFocusedWidget = nullptr;

    if (!inFocusPath.IsEmpty())
    {
        for (auto iter = inFocusPath.crbegin(); iter != inFocusPath.crend(); ++iter)
        {
            auto widgetPtr = *iter;

            // Does this widget support keyboard focus?  If so, then we'll go ahead and set it!
            if (widgetPtr->IsSupportsKeyboardFocus())
            {
                // Is we aren't changing focus then simply return
                if (widgetPtr == oldFocusedWidget)
                {
                    return true;
                }

                newFocusedWidget = widgetPtr;
                newFocusedWidgetPath = WidgetPathHelper::GetPathDownTo(inFocusPath, newFocusedWidget);
                break;
            }
        }
    }

    input::FocusEvent FocusEvent(inCause, user->GetUserHandle());

    // Notify widgets in the old focus path that focus is changing
    if (!oldFocusedWidgetPath.IsEmpty())
    {
        for (auto iter = oldFocusedWidgetPath.cbegin(); iter != oldFocusedWidgetPath.cend(); ++iter)
        {
            std::shared_ptr<SequenceableWidget> curWidget = *iter;
            Assert(curWidget != nullptr);

            curWidget->OnFocusChanging(&oldFocusedWidgetPath, &newFocusedWidgetPath, FocusEvent);
        }
    }

    // Notify widgets in the new focus path that focus is changing
    if (!newFocusedWidgetPath.IsEmpty())
    {
        for (auto iter = newFocusedWidgetPath.begin(); iter != newFocusedWidgetPath.end(); ++iter)
        {
            std::shared_ptr<SequenceableWidget> curWidget = *iter;
            Assert(curWidget != nullptr);

            curWidget->OnFocusChanging(&oldFocusedWidgetPath, &newFocusedWidgetPath, FocusEvent);
        }
    }

    // Record user's focus path happened here!!!
    // Store a weak widget path to the widget that's taking focus
    user->CacheFocusPath(newFocusedWidgetPath, inCause);

    // Let the old widget know that it lost keyboard focus
    if (oldFocusedWidget != nullptr)
    {
        // Let previously-focused widget know that it's losing focus
        oldFocusedWidget->OnFocusLost(&newFocusedWidgetPath, FocusEvent);
    }

    // Let the new widget know that it received keyboard focus
    if (newFocusedWidget != nullptr)
    {
        auto reply = newFocusedWidget->OnFocusReceived(&newFocusedWidgetPath, FocusEvent);

        // Keep reply processor got NO RECURSION !!!
        Assert(reply.ShouldReceiveUserFocus() == false);

        if (reply.IsHandled())
        {
            ProcessReply(user->GetUserHandle(), newFocusedWidgetPath, nullptr, reply);
        }
    }

    return true;
}

void SlateApplication::ProcessReply(UserHandle inUserHandle, const SequenceableWidgetPath& inEventPath, const input::KeyEvent& inKeyEvent, const input::CEReply& theReply)
{
    ProcessReply(inUserHandle, inEventPath, nullptr, theReply);
}

void SlateApplication::ProcessReply(UserHandle inUserHandle, const SequenceableWidgetPath& inEventPath, const input::CharEvent& inKeyCharEvent, const input::CEReply& theReply)
{
    ProcessReply(inUserHandle, inEventPath, nullptr, theReply);
}

void SlateApplication::ProcessReply(UserHandle inUserHandle, const SequenceableWidgetPath& inEventPath, const input::PointerEvent& inPointerEvent, const input::CEReply& theReply)
{
    ProcessReply(inUserHandle, inEventPath, &inPointerEvent, theReply);
}

void SlateApplication::ProcessReply(UserHandle inUserHandle, const SequenceableWidgetPath& inEventPath, const input::WindowActivateEvent& inActivateEvent, const input::CEReply& theReply)
{
    ProcessReply(inUserHandle, inEventPath, nullptr, theReply);
}

void SlateApplication::ProcessReply(UserHandle inUserHandle, const SequenceableWidgetPath& inEventPath, const input::PointerEvent* inPointEvent, const input::CEReply& theReply)
{
    // Release mouse capture if requested or if we are starting a drag and drop.
    // Make sure to only clobber WidgetsUnderCursor if we actually had a mouse capture.
    PointerHandle pointerIndex = CursorPointerIndex();
    if (inPointEvent != nullptr)
        pointerIndex = {inPointEvent->PointerIndex};

    auto platformUser = GetOrCreateUser(inUserHandle);

    // Handle event if requested -- Release mouse/touch capture
    if (theReply.ShouldReleasePointer() && platformUser->HasCapture(pointerIndex))
    {
        platformUser->ReleaseCapture(pointerIndex);
    }

    // Handle event if requested -- Clear focus
    if (theReply.ShouldReleaseUserFocus())
    {
        platformUser->ReleaseAllCapture();
    }

    // Setting mouse/touch capture, mouse/touch position, and locking the mouse/touch
    // are all operations that we shouldn't do if our application isn't Active (The OS ignores half of it, and we'd be in a half state)
    if (IsActive())
    {
        auto requestPointerCaptor = theReply.GetPointerCaptor();

        // A successful pointer path cached make the previous path hold in other hashmap at the same time
        if (requestPointerCaptor != nullptr && platformUser->SetPointerCaptor(inEventPath, pointerIndex))
        {
            const auto* lastWidgetsUnderCursorPath = platformUser->GetLastWidgetsUnderPointer(pointerIndex);

            if (lastWidgetsUnderCursorPath != nullptr)
            {
                for (auto iter = lastWidgetsUnderCursorPath->crbegin(); iter != lastWidgetsUnderCursorPath->crend(); ++iter)
                {
                    auto& lastWidgetPtr = *iter;
                    if (lastWidgetPtr != requestPointerCaptor)
                    {
                        if (inPointEvent != nullptr)
                            lastWidgetPtr->OnPointerLeave(*inPointEvent);
                        else
                        {
                            input::PointerEvent simulatePointer;
                            lastWidgetPtr->OnPointerLeave(simulatePointer);
                        }
                    }
                    else
                    {
                        // Done routing mouse leave
                        break;
                    }
                }
            }
        }

        std::optional<Float2> const& requestedMousePos = theReply.GetRequestedMousePos();
        if (requestedMousePos.has_value())
        {
            platformUser->SetCursorPosition(requestedMousePos.value());
        }
    }

    // Handle event if requested -- Capture focus
    if (theReply.ShouldReceiveUserFocus())
    {
        SequenceableWidgetPath path;
        if (!WidgetPathHelper::FindPathToWidget(theReply.GetFocusRecepient(), path))
            return;

        SetUserFocus(platformUser->GetUserHandle(), path, theReply.GetFocusCause());
    }
}

PlatformUser* SlateApplication::GetOrCreateUser(UserHandle userHandle)
{
    Assert(EngineGlobal::GetEngine());
    auto inputManager = EngineGlobal::GetInputManager();
    auto user = GetUser(userHandle);

    if (user == nullptr)
    {
        auto newUserDummy = inputManager->CreateUser(userHandle, mPlatformApplication->GetCursor().get(), this);

        user = reinterpret_cast<PlatformUser*>(newUserDummy);
    }

    return user;
}

void SlateApplication::PendingExternalInputDevice(const cross::input::InputDevicePtr& device)
{
    // if app is registered, just register the device directly
    if (auto app = GetPlatformApplication(); app)
    {
        app->RegisterExternalDevice(device);
    }
    else // push the device to the pending list, wait for the platform app to be initiated
    {
        mPendingExternalInputDevices.insert(device);
    }
}

void SlateApplication::ResetToDefaultPointerInputSettings()
{
    SequenceableWidgetPath path;
    ProcessReply({0}, path, nullptr, input::CEReply::Handled().ClearUserFocus());
}

void SlateApplication::Init(ICrossEngine* inEngine, std::shared_ptr<PlatformApplication> inPlatformApp)
{
    Assert(inEngine && inPlatformApp.get());

    mPlatformApplication = inPlatformApp;

    // register pending devices to app
    {
        std::scoped_lock lock(mInputDevicesMtx);

        for (const auto& device : mPendingExternalInputDevices)
        {
            mPlatformApplication->RegisterExternalDevice(device);
        }
        mPendingExternalInputDevices.clear();
    }
}

std::vector<std::shared_ptr<GameViewPortWidget>> SlateApplication::GetGameViewPortsImpl()
{
    return SlateApplication::Instance()->GetPlatformApplication()->GetAllViewProtWidgets();
}

std::shared_ptr<GameViewPortWidget> SlateApplication::GetUserGameViewPortImpl(PlatformUser& inUser)
{
    return SlateApplication::Instance()->GetPlatformApplication()->GetUserViewPortWidget(inUser);
}

bool SlateApplication::OnControllerAnalog(const UniqueString KeyName, const float AxisValue, UserHandle userHandle)
{
    auto key = cross::input::CEKey(KeyName);
    if (key.IsValid())
    {
        input::AnalogInputEvent analogEvent(userHandle, key, mPlatformApplication->GetModifierKeys(), false, AxisValue);
        return ProcessAnalogInputEvent(analogEvent);
    }
    return false;
}

bool SlateApplication::OnControllerButtonPressed(const UniqueString KeyName, const bool isRepeat, UserHandle userHandle)
{
    auto key = cross::input::CEKey(KeyName);
    if (key.IsValid())
    {
        input::KeyEvent event(userHandle, key, mPlatformApplication->GetModifierKeys(), isRepeat);
        return ProcessKeyDownEvent(event);
    }
    return false;
}

bool SlateApplication::OnControllerButtonReleased(const UniqueString KeyName, const bool isRepeat, UserHandle userHandle)
{
    auto key = cross::input::CEKey(KeyName);
    if (key.IsValid())
    {
        input::KeyEvent event(userHandle, key, mPlatformApplication->GetModifierKeys(), isRepeat);
        return ProcessKeyUpEvent(event);
    }
    return false;
}

}   // namespace cross
