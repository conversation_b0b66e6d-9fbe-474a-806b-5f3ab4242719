#pragma once
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "CEAnimation/AnimSync.h"

namespace cross::anim {

class AnimStoryBoard : public AnimGraph
{
public:
    AnimStoryBoard() = delete;
    AnimStoryBoard(const Animator* inOwnerAnimator, const CEName& inGraphName)
        : AnimGraph(inGraphName)
        , mOwnerAnimator(inOwnerAnimator)
    {}

    virtual ~AnimStoryBoard() = default;

    //// ~IAnimGraph Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    virtual void PostUpate() override;

    ////
    //// ~IAnimGraph Interface End~

    virtual GraphType GetGraphType() const override
    {
        return GraphType::StoryBoard;
    }

public:
    const Animator* GetOwnerAnimator() const
    {
        return mOwnerAnimator;
    }

    void SetRootMotionMode(RootMotion::ExtractMode extractMode, RootMotion::ApplyMode applyMode)
    {
        mRootMotionExtractMode = extractMode;
        mRootMotionApplyMode = applyMode;
    }

    RootMotion::ExtractMode GetRootMotionExtractMode() const
    {
        return mRootMotionExtractMode;
    }
    RootMotion::ApplyMode GetRootMotionApplyMode() const
    {
        return mRootMotionApplyMode;
    }

    bool CanApplyRootMotion() const
    {
        return mRootMotionExtractMode != RootMotion::DoNotExtract && mRootMotionApplyMode == RootMotion::Apply;
    }

protected:
    void RecordSavePoseNode(const CEName& inPoseName, AnimGraph_SavePoseNode* inSavePoseNode);
    void RecordGetPoseNode(AnimGraph_GetPoseNode* inGetPoseNode);

private:
    const Animator* mOwnerAnimator{nullptr};

    // Non-serialized
    // all SavePose nodes are running in this StoryBoard currently
    CENameMap<CEName, AnimGraph_SavePoseNode*> mSavePoseNodeMap;

    // all GetPose nodes are running in the StoryBoard currently
    std::vector<AnimGraph_GetPoseNode*> mGetPoseNodeList;
    // Sorted SavePoseNode to update in right order
    std::vector<AnimGraph_SavePoseNode*> mSortedSavePoseNodes;

    /* Take over update of AnimInstances according to Sync Strategy */
    sync::AnimSync mAnimSync;

    RootMotionParams mRootMotionParams;
    /* root motion extraction mode */
    RootMotion::ExtractMode mRootMotionExtractMode;
    /* root motion apply mode */
    RootMotion::ApplyMode mRootMotionApplyMode;

    friend class AnimAssembler;
};

using AnimStoryBoardPtr = std::shared_ptr<AnimStoryBoard>;

}   // namespace cross::anim
