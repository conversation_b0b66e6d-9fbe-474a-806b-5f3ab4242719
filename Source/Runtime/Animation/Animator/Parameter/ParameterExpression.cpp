#include "EnginePrefix.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "Runtime/Animation/Animator/Parameter/ParameterExpression.h"

namespace cross::anim 
{
	/////////////////////////////////////////////
	// Lexer
	//
	/////////////////////////////////////////////
	bool Lexer::Process(const std::string& exprStr)
	{
		mAnalyzer.clear();

		if (!mAnalyzer.process(exprStr))
			return false;

		if (mDependencyStr.empty())
			return true;

		if (!ScanToken())
			return false;

		return true;
	}

	bool Lexer::ScanToken()
	{
		auto tokenNum = mAnalyzer.size();
		for (auto i = 0; i < tokenNum; ++i)
		{
			const Token& curToken = mAnalyzer[i];
			if (curToken.type == TokenType::e_symbol)
			{
				if (curToken.value == mDependencyStr)
					return true;
			}
		}

		LOG_ERROR("Symbol [{}] must be used in expression to determine return value", mDependencyStr);
        Assert(false);
		return false;
	}

	/////////////////////////////////////////////
	// ParamSymbolTable
	//
	/////////////////////////////////////////////
	ParamSymbolTable::ParamSymbolTable()
	{
		// add constants such as pi, e, infinity
		mTable.add_constants();

		// add vector processing package
		exprtk::rtl::vecops::package<float> vecops_package;
		mTable.add_package(vecops_package);

		constexpr SInt32 maxParameters = 256;
		mNumericTypeStore.reserve(maxParameters);
		mStringTypeStore.reserve(maxParameters);
		mVector2TypeStore.reserve(maxParameters);
		mVector3TypeStore.reserve(maxParameters);
		mVector4TypeStore.reserve(maxParameters);
	}

	bool ParamSymbolTable::UpdateTableByParam(const Parameter* inParam)
	{
		auto paramType = inParam->GetParameterType();
		auto& paramName = inParam->Name();

		auto res = inParam->ReferenceFromType();
		// Numeric Type
		if (paramType == ParamMode::Bool)
			return AddOrUpdateNumeric(paramName, *(static_cast<const bool*>(res.second)));
		else if (paramType == ParamMode::Int)
			return AddOrUpdateNumeric(paramName, *(static_cast<const SInt32*>(res.second)));
		else if (paramType == ParamMode::Float)
			return AddOrUpdateNumeric(paramName, *(static_cast<const float*>(res.second)));
		// String Type
		else if (paramType == ParamMode::String)
			return AddOrUpdateString(paramName, *(static_cast<const std::string*>(res.second)));
		// Vector Type
		else if (paramType == ParamMode::Vector2)
			return AddOrUpdateVector(paramName, *(static_cast<const Float2*>(res.second)));
		else if (paramType == ParamMode::Vector3)
			return AddOrUpdateVector(paramName, *(static_cast<const Float3*>(res.second)));
		else if (paramType == ParamMode::Vector4)
			return AddOrUpdateVector(paramName, *(static_cast<const Float4*>(res.second)));
		// Transform Type
		else if (paramType == ParamMode::Transform)
			return AddOrUpdateTransform(paramName, *(static_cast<const NodeTransform*>(res.second)));
		// Customized component
		// should not be in paramSymbolTable?
        else if (paramType == ParamMode::Customized)
			return true;

		AssertMsg(false, "Invalid parameter type");
		return false;
	}

	bool ParamSymbolTable::AddOrUpdateString(const CEName& paramName, const std::string& value)
	{
		// Update
		if (mStringTypeStore.find(paramName) != mStringTypeStore.end())
		{
			mStringTypeStore[paramName] = std::move(value);
			return true;
		}

		// Add
		auto iter = mStringTypeStore.insert({ paramName , value });
		std::string nameStr(paramName.GetCString());
		return mTable.add_stringvar(nameStr, iter.first->second, false);
	}

	bool ParamSymbolTable::AddOrUpdateTransform(const CEName& paramName, const NodeTransform& value)
	{
		return false;
	}

	/////////////////////////////////////////////
	// ParamExpression
	//
	/////////////////////////////////////////////
	void ParamExpression::RegisterSymbolTable(ParamSymbolTable& inSymbolTable)
	{
		mExprInstance.register_symbol_table(inSymbolTable.mTable);
	}

	bool ParamExpression::Compile()
	{
		if (mExprInstance.symbol_table_num() == 0)
		{
			// a symbol table can be attached to several expressions.
			// an expression can have more than one symbol tables
			AssertMsg(false, "Expression has no attached symbol table");
			return false;
		}

		mHasCompiled = true;
		return ParamExprParser::GetInstance().CompileExpression(*this);
	}

	float ParamExpression::Evaluate()
	{
		Assert(mHasCompiled);
		return mExprInstance.value();
	}

	/////////////////////////////////////////////
	// ParamExpressionParser
	//
	/////////////////////////////////////////////
	const std::size_t ParamExprParser::sDefaultSettings =
		ExprtkParserSettings::e_replacer + ExprtkParserSettings::e_joiner + ExprtkParserSettings::e_numeric_check +
		ExprtkParserSettings::e_bracket_check + ExprtkParserSettings::e_sequence_check + ExprtkParserSettings::e_strength_reduction;

	ParamExprParser::LexerMap ParamExprParser::sLexerMap =
	{
		{ Expression::None,   Lexer("") },
		{ Expression::Graph,  Lexer(GRAPH_FORMULA_RETURN) },
		{ Expression::FSM,		Lexer(FSM_FORMULA_RETURN) }
	};

	ParamExprParser::ParamExprParser() : mParser(sDefaultSettings)
	{
		Initialize();
	}

	void ParamExprParser::Initialize()
	{
		/* Disable all control keywords, include followings:
		 * (a) If or If-Else
		 * (b) Switch statement
		 * (c) For Loop
		 * (d) While Loop
		 * (e) Repeat Loop
		*/
		mParser.settings().disable_all_control_structures();
		mParser.dec().collect_functions() = true;
		mParser.dec().collect_variables() = true;
	}

	bool ParamExprParser::CompileExpression(ParamExpression& inExpr)
	{
		// Lexical analysis
		if (!sLexerMap.at(inExpr.GetExprType()).Process(inExpr.mExprStr))
			return false;

		// Compile expression meanwhile building abstract syntax tree for expression
		if (!mParser.compile(inExpr.mExprStr, inExpr.mExprInstance))
		{
			for (auto i = 0; i < mParser.error_count(); ++i)
			{
				// Include the specific nature of each error
				// and its position in the expression string.
				auto error = mParser.get_error(i);

				LOG_ERROR("Error Index: {},  Pos in Expression: {},  Error Type: {},  Error Message: {},  Expression: {}",
					static_cast<int>(i),
					static_cast<int>(error.token.position),
					exprtk::parser_error::to_str(error.mode),
					error.diagnostic,
					inExpr.mExprStr);
			}

			return false;
		}

		return true;
	}

	ParamExprParser& ParamExprParser::GetInstance() noexcept
	{
		static ParamExprParser instance = ParamExprParser();
		return instance;
	}
}