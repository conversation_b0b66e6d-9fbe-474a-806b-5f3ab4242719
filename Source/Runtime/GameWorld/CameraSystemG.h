#pragma once
#include "ECS/Develop/Framework/Types.h"

#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "RenderEngine/RenderCamera.h"
#include "Resource/Resource.h"
#include "Resource/RenderTextureResource.h"


namespace cross
{
class RenderPipelineG;
class RenderPipelineExternal;

class CanvasWidget;
enum class ViewType;
//////////////////////////////////////////////////////////////////////////
//camera component
struct CameraComponentG final: ecs::IComponent
{
    CEComponentInternal(SystemType = CameraSystemG)

     CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

protected:
    //---------------------------------------------------
    //!!beware of data alignment when add or remove member!!!
    //---------------------------------------------------
    CameraView mCameraView;
    CameraView mLastFrameCameraView;
    CameraInfo mCameraInfo;
    CameraInfo mLastFrameCameraInfo;
    RenderPipelineG* mRenderPipeline{ nullptr };
    UInt32 mCameraMask{ UINT32_MAX };
    CameraProjectionMode mProjectionMode{ CameraProjectionMode::Perspective };
    bool mEnable{ false };
    UInt32 mTargetWidth = 800;
    UInt32 mTargetHeight = 800;
    SInt32 mPriority = 0;
    std::string mRenderTexturePath;
    RenderTextureResourcePtr mRenderTextureResourcePtr;
private: // Not Serializable
    bool mIsFrustumShow{ false };
    bool mTestCulling{ false };
    friend class CameraSystemG;
};

//////////////////////////////////////////////////////////////////////////
//camera system
class CameraSystemR;

class ENGINE_API CameraSystemG final : public GameSystemBase
{
    CESystemInternal(ComponentType = CameraComponentG)

public:
    using CameraComponentGHandle = ecs::ComponentHandle<CameraComponentG>;
    DEFINE_COMPONENT_READER_WRITER(CameraComponentG, CameraComponentGReader, CameraComponentGWriter)

    CEFunction(Reflect)
    static CameraSystemG* CreateInstance();
    
    virtual void Release() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

public:
    static SerializeNode SerializeCameraComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);

    static void DeserializeCameraComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent * componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

public:
    void BindRenderPipeline(const CameraComponentGWriter& writer, RenderPipelineG* pipeline);

    RenderPipelineG* UnbindRenderPipeline(const CameraComponentGWriter& writer);

    //void BindRenderPipelineE(const CameraComponentGWriter& writer, RenderPipelineExternal* pipeline);
    //RenderPipelineExternal* UnbindRenderPipelineE(const CameraComponentGWriter& writer);

    RenderTextureResourcePtr GetCameraRenderTextureResourcePtr(const CameraComponentGReader& reader) {
        return reader->mRenderTextureResourcePtr;
    }

    CEFunction(Editor)
    bool SetMainCamera(ecs::EntityID camEntity);

    CEFunction(Editor)
    ecs::EntityID GetMainCamera();

    void SwitchCamera(const CameraComponentGWriter& srcCamera, const CameraComponentGWriter& destCamera);
    
    inline RenderPipelineG* GetRenderPipeline(const CameraComponentGReader& reader) const { return reader->mRenderPipeline; }
 
    CEFunction(Editor, Script)
    void SetCameraEnable(const CameraComponentGWriter& writer, bool enable);

    CEFunction(Editor, Script)
    bool GetCameraRenderToTarget(const CameraComponentGReader& reader)
    {
        return reader->mRenderTextureResourcePtr;
    }

    CEFunction(Editor, Script)
    UInt32 GetTargetWidth(const CameraComponentGReader& reader)
    {
        return reader->mTargetWidth;
    }

    CEFunction(Editor, Script)
    UInt32 GetTargetHeight(const CameraComponentGReader& reader)
    {
        return reader->mTargetHeight;
    }

    CEFunction(Editor, Script)
    void SetTargetWidth(const CameraComponentGWriter& writer, UInt32 width);

    CEFunction(Editor, Script)
    void SetTargetHeight(const CameraComponentGWriter& writer, UInt32 height);

    bool GetCameraEnable(const CameraComponentGReader& reader) const;

    CEFunction(Editor, Script)
    void SetCameraMask(const CameraComponentGWriter& writer, UInt32 cameraMask);
    CEFunction(Editor, Script)
    inline UInt32 GetCameraMask(const CameraComponentGReader& reader) const { return reader->mCameraMask; }
    CEFunction(Editor, Script)
    void SetProjectionMode(const CameraComponentGWriter& writer, CameraProjectionMode mode);

    CEFunction(Editor, Script)
    inline CameraProjectionMode GetProjectionMode(const CameraComponentGReader& reader) const { return reader->mProjectionMode; }

    const CameraView& GetCameraView(const CameraComponentGReader& reader) const { return reader->mCameraView; };


    CEFunction(Editor, Script)
    inline float GetFOV(const CameraComponentGReader& reader)const { return reader->mCameraInfo.mFov; }

    CEFunction(Editor, Script)
    inline float GetNearPlane(const CameraComponentGReader& reader, CameraProjectionMode cameraType = CameraProjectionMode::Perspective) const
    {
        return reader->mProjectionMode == CameraProjectionMode::Perspective ? reader->mCameraInfo.mNearPlane : reader->mCameraInfo.mOrthNearPlane;
    }

    CEFunction(Editor, Script)
    inline float GetFarPlane(const CameraComponentGReader& reader, CameraProjectionMode cameraType = CameraProjectionMode::Perspective) const
    {
        return reader->mProjectionMode == CameraProjectionMode::Perspective ? reader->mCameraInfo.mFarPlane : reader->mCameraInfo.mOrthFarPlane;
    }

    //project get
    CEFunction(Editor, Script)
    inline const Float4x4A& GetViewMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mViewMatrix; }
    CEFunction(Editor, Script)
    inline const Float4x4A& GetProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mProjMatrix; }

    inline const Float4x4A& GetViewProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mViewProjMatrix; }

    inline const Float4x4A& GetInvertViewMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mInvertViewMatrix; }

    inline const Float4x4A& GetInvertProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mInvertProjMatrix; }

    CEFunction(Editor, Script, Reflect)
    inline float GetPerspectiveAspect(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mAspectRatio; }
    CEFunction(Editor, Script, Reflect)
    inline float GetPerspectiveFov(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mFov; }
    CEFunction(Editor, Script, Reflect)
    inline float GetMinFocalLength(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mMinFocalLength; }
    CEFunction(Editor, Script, Reflect)
    inline float GetMaxFocalLength(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mMaxFocalLength; }
    CEFunction(Editor, Script, Reflect)
    inline float GetFocalLength(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mFocalLength; }
    CEFunction(Editor, Script, Reflect)
    inline float GetCurrentFocusDistance(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mCurrentFocusDistance; }
    CEFunction(Editor, Script, Reflect)
    inline float GetCurrentAperture(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mCurrentAperture; }
    CEFunction(Editor, Script, Reflect)
    inline float GetSensorWidth(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mSensorWidth; }
    CEFunction(Editor, Script, Reflect)
    inline float GetSensorHeight(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mSensorHeight; }
    CEFunction(Editor, Script, Reflect)
    inline float GetPerspectiveNearPlane(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mNearPlane; }
    CEFunction(Editor, Script, Reflect)
    inline float GetPerspectiveFarPlane(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mFarPlane; }
    CEFunction(Editor, Script, Reflect)
    inline float GetJitterIntensity(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mJitterIntensity; }

    //project get last
    inline const Float4x4A& GetLastFrameViewMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mViewMatrix; }

    inline const Float4x4A& GetLastFrameProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mProjMatrix; }

    inline const Float4x4A& GetLastFrameViewProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mViewProjMatrix; }

    inline const Float4x4A& GetLastFrameInvertViewMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mInvertViewMatrix; }

    inline const Float4x4A& GetLastFrameInvertProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mInvertProjMatrix; }

    inline float GetLastFrameAspectRatio(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mAspectRatio; }

    inline float GetLastFrameFOV(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mFov; }

    inline float GetLastFrameNearPlane(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mNearPlane; }

    inline float GetLastFrameFarPlane(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mFarPlane; }
    
    //ortho get
    CEFunction(Editor, Script)
    inline float GetOrthoWidth(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mWidth; }
    CEFunction(Editor, Script)
    inline float GetOrthoHeight(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mHeight; }
    CEFunction(Editor, Script)
    inline float GetOrthoNearPlane(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mOrthNearPlane; }
    CEFunction(Editor, Script)
    inline float GetOrthoFarPlane(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mOrthFarPlane; }
    CEFunction(Editor, Script)
    const std::string GetRenderTexturePath(const CameraComponentGReader& modelH);

    inline const Float4x4A& GetOrthoViewMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mOrthViewMatrix; }

    inline const Float4x4A& GetOrthoProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mOrthProjMatrix; }

    inline const Float4x4A& GetOrthoViewProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mOrthViewProjMatrix; }

    inline const Float4x4A& GetOrthoInvertViewMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mOrthInvertViewMatrix; }

    inline const Float4x4A& GetOrthoInvertProjMatrix(const CameraComponentGReader& reader) const { return reader->mCameraView.mOrthInvertProjMatrix; }

    //ortho get last
    inline float GetLastFrameOrthoWidth(const CameraComponentGReader& cam) const { return cam->mLastFrameCameraInfo.mWidth; }

    inline float GetLastFrameOrthoHeight(const CameraComponentGReader& cam) const { return cam->mLastFrameCameraInfo.mHeight; }

    inline float GetLastFrameOrthNearPlane(const CameraComponentGReader& cam) const { return cam->mLastFrameCameraInfo.mOrthNearPlane; }

    inline float GetLastFrameOrthFarPlane(const CameraComponentGReader& cam) const { return cam->mLastFrameCameraInfo.mOrthFarPlane; }

    inline const Float4x4A& GetLastFrameOrthoViewMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mOrthViewMatrix; }

    inline const Float4x4A& GetLastFrameOrthoProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mOrthProjMatrix; }

    inline const Float4x4A& GetLastFrameOrthoViewProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mOrthViewProjMatrix; }

    inline const Float4x4A& GetLastFrameOrthoInvertViewMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mOrthInvertViewMatrix; }

    inline const Float4x4A& GetLastFrameOrthoInvertProjMatrix(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mOrthInvertProjMatrix; }

    //others get
    inline const BoundingFrustum& GetFrustum(const CameraComponentGReader& reader) const { return reader->mCameraView.mFrustum; }

    inline const BoundingFrustum& GetLastFrameFrustum(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraView.mFrustum; }

    inline float GetLastFrameNormalBias(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mNormalBias; }

    inline float GetLastFrameDepthBias(const CameraComponentGReader& cam)const { return cam->mLastFrameCameraInfo.mDepthBias; }

    inline float GetNormalBias(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mNormalBias; }

    inline float GetDepthBias(const CameraComponentGReader& reader) const { return reader->mCameraInfo.mDepthBias; }

    CEFunction(Editor, Script)
    inline void SetViewMatrix(const CameraComponentGWriter& writer, const Float4x4& matrix)
    {
        writer->mCameraView.mViewMatrix = matrix;
    }

    //perspective set
    CEFunction(Editor, Script, Reflect)
    void SetPerspective(const CameraComponentGWriter& camHandle, float aspect, float fov, float nearPlane = 0, float farPlane = 0);

    CEFunction(Editor, Script, Reflect)
    bool SetPerspectiveAspect(const CameraComponentGWriter& camHandle, float aspectRatio);

    CEFunction(Editor, Script, Reflect)
    bool SetPerspectiveFov(const CameraComponentGWriter& camHandle, float fov);

    CEFunction(Editor, Script, Reflect)
    bool SetMinFocalLength(const CameraComponentGWriter& camHandle, float minFocalLength);

    CEFunction(Editor, Script, Reflect)
    bool SetMaxFocalLength(const CameraComponentGWriter& camHandle, float maxFocalLength);

    CEFunction(Editor, Script, Reflect)
    bool SetFocalLength(const CameraComponentGWriter& camHandle, float focalLength);

    CEFunction(Editor, Script, Reflect)
    bool SetCurrentFocusDistance(const CameraComponentGWriter& camHandle, float val);

    CEFunction(Editor, Script, Reflect)
    bool SetCurrentAperture(const CameraComponentGWriter& camHandle, float val);

    CEFunction(Editor, Script, Reflect)
    bool SetSensorWidth(const CameraComponentGWriter& camHandle, float sensorWidth);

    CEFunction(Editor, Script, Reflect)
    bool SetSensorHeight(const CameraComponentGWriter& camHandle, float sensorHeight);

    CEFunction(Editor, Script, Reflect) 
    bool SetPerspectiveFovDegree(const CameraComponentGWriter& camHandle, float fov);

    CEFunction(Editor, Script, Reflect)
    bool SetPerspectiveNear(const CameraComponentGWriter& camHandle, float nearDistance);

    CEFunction(Editor, Script, Reflect)
    bool SetPerspectiveFar(const CameraComponentGWriter& camHandle, float farDistance);
    
    CEFunction(Editor, Script, Reflect)
    bool SetPerspectiveOffCenter(const CameraComponentGWriter& camHandle, float leftFov, float rightFov, float upFov, float downFov);

    CEFunction(Editor, Script, Reflect)
    void SetPerspectiveMatrix(const CameraComponentGWriter& writer, const Float4x4& matrix);

    //orthogonal set
    CEFunction(Editor, Script)
    void SetOrthogonal(const CameraComponentGWriter& camHandle, float width, float height, float nearDistance, float farDistance);

    CEFunction(Editor, Script, Reflect)
    bool SetOrthogonalWidth(const CameraComponentGWriter& camHandle, float width);

    CEFunction(Editor, Script, Reflect)
    bool SetOrthogonalHeight(const CameraComponentGWriter& camHandle, float height);

    CEFunction(Editor, Script, Reflect)
    bool SetOrthogonalNear(const CameraComponentGWriter& camHandle, float nearDistance);

    CEFunction(Editor, Script, Reflect)
    bool SetOrthogonalFar(const CameraComponentGWriter& camHandle, float farDistance);

    CEFunction(Editor, Script, Reflect)
    bool SetRenderTexturePath(const CameraComponentGWriter& modelH, const std::string& assetpath);

    void SetRenderTexture(const CameraComponentGWriter& modelH, RenderTextureResourcePtr renderTexture);

    //orthogonal get
    CEFunction(Editor)
    float GetOrthogonalWidth(const CameraComponentGReader& camHandle);
    
    CEFunction(Editor)
    float GetOrthogonalHeight(const CameraComponentGReader& camHandle);
    
    CEFunction(Editor)
    float GetOrthogonalNear(const CameraComponentGReader& camHandle);
    
    CEFunction(Editor)
    float GetOrthogonalFar(const CameraComponentGWriter& camHandle);
    
    //other set
    void SetNormalBias(const CameraComponentGWriter& writer, float normalBias) { writer->mCameraInfo.mNormalBias = normalBias; }

    void SetDepthBias(const CameraComponentGWriter& writer, float depthBias) { writer->mCameraInfo.mDepthBias = depthBias; }

    //todo Frustum
    CEFunction(Editor, Script)
    void SetFrustumShow(const CameraComponentGWriter& writer, bool isShow);
    CEFunction(Editor, Script)
    void SetTestCulling(const CameraComponentGWriter& writer, bool isTestCulling);
    CEFunction(Editor, Script)
    bool GetTestCulling(const CameraComponentGReader& writer) { return writer->mTestCulling; }

    inline Float3 GetCameraCoord(const CameraComponentGReader& reader, const Float3& worldCoord)
        { Float3 result; CameraUtility::WorldToCamera(result, worldCoord, reader->mCameraView); return result; }

    inline Float3 GetWorldCoord(const CameraComponentGReader& reader, const Float3& cameraCoord)
        { Float3 result; CameraUtility::CameraToWorld(result, cameraCoord, reader->mCameraView); return result; }
    CEFunction(Editor, Script)
    inline Float2 GetScreenCoord(const CameraComponentGReader& reader, const Float3& worldCoord)
        { Float2 result; CameraUtility::WorldToScreen(result, worldCoord, reader->mCameraView, reader->mProjectionMode); return result; }

    inline Float3 GetWorldCoord(const CameraComponentGReader& reader, const Float2& screenCoord, float depth)
        { Float3 result; CameraUtility::ScreenToWorld(result, screenCoord, depth, reader->mCameraInfo, reader->mCameraView, reader->mProjectionMode); return result; }

    CEFunction(Editor, Script)
    Float2 GetProjectScreenSize(const CameraComponentGReader& reader, const Float3 & center, const Float3 & extent) const;

    virtual RenderSystemBase* GetRenderSystem() override;

    CEFunction(Editor, Script)
    bool CullPoint(const CameraComponentGReader &reader, Float3 point);

    CEFunction(Editor, Script, Reflect)
    bool SetJitterIntensity(const CameraComponentGWriter& camHandle, float jitterIntensity);

protected:
    CameraSystemG();

    virtual ~CameraSystemG();

    void UpdatePerspectiveCamera(const CameraComponentGWriter& camHandle);

    void UpdateOrthogonalCamera(const CameraComponentGWriter& camHandle);

    void DispatchCameraInfo(ecs::EntityID entity, const CameraInfo& cameraInfo);

    void UpdateCameraData(ecs::EntityID entity, const Float4x4A& viewMatrix, const Float4x4A& projMatrix, const Float4x4A& viewProjMatrix,
        const Float4x4A& inverseViewMatrix, const Float4x4A& inverseProjMatrix, const BoundingFrustum& frustum);

    void UpdateOrthCameraData(ecs::EntityID entity, const Float4x4A& viewMatrix, const Float4x4A& projMatrix, const Float4x4A& viewProjMatrix,
        const Float4x4A& inverseViewMatrix, const Float4x4A& inverseProjMatrix, const BoundingOrientedBox& orthBox);

    void UpdateViewReleatedInfo(ecs::EntityID entity, const Float4x4A& viewMatrix, const Float4x4A& orthViewMatrix, const Float4x4A& inverseViewMatrix,
        const Float4x4A& orthInverseViewMatrix, const Float4x4A& viewProjMatrix, const Float4x4A& orthViewProjMatrix);

private:
    ViewType GetViewType(const CameraComponentGReader& reader);

    void AdaptToResolution(UInt32 width,UInt32 height);

    CameraSystemR* mRenderCamSystem{ nullptr }; 
    bool mIsRenderObjectOwner{ true };
    ecs::EntityID mMainCamera{ecs::EntityID::InvalidHandle()};
};


}
