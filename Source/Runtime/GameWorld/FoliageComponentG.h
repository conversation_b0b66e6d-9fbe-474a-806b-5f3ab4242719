#pragma once
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/TRS.h"
#include "CECommon/Geometry/AABBTree.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/Material.h"
#include "Resource/BinaryResource.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/FoliageComponentR.h"

namespace cross
{
    struct LightInstanceData;
    struct FoliageComponentLoDSection
    {
        CEMeta(Editor)
        std::string mDefaultMaterialPath;
        CEMeta(Editor)
        std::vector<std::string> mSubSectionMaterialPaths;
    };

    struct SubmeshProperty
    {
        CEMeta(Editor)
        bool visible = true;
    };

    struct FoliageComponentResources
    {
        CEMeta(Editor)
        std::string mPrimaryMeshAssetPath;
        CEMeta(Editor)
        std::string mPrimaryMaterialPath;
        CEMeta(Editor)
        std::vector<FoliageComponentLoDSection> mLoDSections;
        CEMeta(Editor)
        std::vector<SubmeshProperty> mSubmeshProperty;
        CEMeta(Editor) 
        bool mEnabledIntersection = true;
        CEMeta(Editor)
        float mGlobalScale = 1.0f;
        CEMeta(Editor)
        std::string mPrefabResource;
        CEMeta(Editor)
        std::string mInstanceResource;
        CEMeta(Editor)
        std::string mInstanceLightResource;
    };

    struct FoliageComponentTransform
    {
        CEMeta(Editor)
        Float3 mTranslation;
        CEMeta(Editor)
        Float3 mScale;
        CEMeta(Editor)
        Quaternion mRotation;
        CEMeta(Editor)
        bool mDelete;
    };

    struct FoliageComponentInstanceData
    {
        CEMeta(Editor)
        std::vector<FoliageComponentTransform> mInstanceData;
    };

    struct FoliageComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = FoliageSystemG)
        ~FoliageComponentG();
        cross::FoliageComponentG& operator=(cross::FoliageComponentG&&)=default;
        friend class FoliageSystemG;
        friend class WorldLoadingSystemG;
        struct LoDSection
        {
            MaterialInterfacePtr mDefaultMaterial;
            std::vector<MaterialInterfacePtr> mSubSectionMaterials;
        };


        CEFunction(Reflect)
        ENGINE_API static ecs::ComponentDesc* GetDesc();

    private:
        MeshAssetDataResourcePtr mPrimaryMeshAsset;

        MaterialInterfacePtr mPrimaryMaterial;

        std::vector<LoDSection> mLoDSections;

        std::vector<SubmeshProperty> mSubmeshProperty;

        std::shared_ptr<InstanceDataVecContainer> mDataContainer = std::make_shared<InstanceDataVecContainer>();
        BinaryResourcePtr mInstanceResource;
        BinaryResourcePtr mInstanceDataLightResource;

        FoliageComponentInstanceData mEditorInstanceData;

        BoundingBox mLocalBoundingBox;

        bool mGenerateBVH = false;

        bool mIntersection = true;

        bool mEnable = true;

        float mGlobalScale = 1.0f;

        float mGlobalRangeScale = 1.0f;

        float mMaxRandomCulling = 1.0f;

        float mDensity = 1.0f;

        Transform mLocalTransform;

        Transform mLightLocalTransform;

        bool mInitialized = false;

        bool mLightCastShadow = true;

        bool mInstanceDataReady = false;
        UInt32 mPCGReservedCapacity = 0;

        FoliageGenerationType mFoliageGenerationType = FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE;

        std::string mPrefabResource;
        ResourcePtr mPrefabResourceHolder;

        std::shared_ptr<AABBTree<UInt32>> mInstanceBVHAABBTree;
    };
}
