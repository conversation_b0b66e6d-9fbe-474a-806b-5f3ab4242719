#include "EnginePrefix.h"
#include "Runtime/GameWorld/RenderNodeSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"

namespace cross
{
RenderNodeSystemG* RenderNodeSystemG::CreateInstance() { return new RenderNodeSystemG(); }

RenderNodeSystemG::RenderNodeSystemG()
{
    mRenderSystem = RenderNodeSystemR::CreateInstance();
}

RenderNodeSystemG::~RenderNodeSystemG()
{
    if (mIsRenderObjectOwner)
    {
        mRenderSystem->Release();
    }
    mRenderSystem = nullptr;
}

void RenderNodeSystemG::Release()
{
    delete this;
}

RenderSystemBase* RenderNodeSystemG::GetRenderSystem()
{
    return mRenderSystem;
}

void RenderNodeSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

}
