#include "EnginePrefix.h"
#include "TerrainSystemG.h"
#include "RenderEngine/TerrainSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "GameWorld.h"
#include "CameraSystemG.h"
#include "TransformSystemG.h"
#include "Resource/AssetStreaming.h"
#include "Resource/TerrainResource.h"
#include "Resource/ResourceManager.h"
#include "GameWindowSystem.h"
#include "RendererSystemG.h"
#include "PhysicsSystemG.h"
#include "RenderPropertySystemG.h"
#include "AABBSystemG.h"
#include "CrossPhysics/PhysicsEngine/PhysicsCooker.h"
#include "Runtime/Interface/CrossEngineImp.h"

#ifdef CROSSENGINE_EDITOR
#include "CrossImage/imageio.h"
#include "CrossImage/lodepng.h"
#include "RenderEngine/RenderPipeline/Effects/ContactShadow.h"
#endif

#if DEBUG_TILE_CULLING
#include "PrimitiveRenderSystemG.h"
#endif
#include "RenderPipelineSystemG.h"

DECLARE_CPU_TIMING_GROUP(GroupTerrainSystem);

namespace cross
{
    //TerrainSystemG::DebugMemoryResource TerrainSystemG::mDebugMemoryResource;

    TerrainSystemG* TerrainSystemG::CreateInstance()
    {
        return new TerrainSystemG;
    }

    void TerrainSystemG::OnFirstUpdate(FrameParam* frameParam)
    {
    }

    SerializeNode TerrainSystemG::SerializeTerrainComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "SerializeTerrainComponent");

        const auto terrainComponent = static_cast<TerrainComponentG*>(componentPtr);

        SerializeNode componentJson;
        componentJson["mTerrainPath"] = terrainComponent->mTerrainPath;
        componentJson["mMaterialOverride"] = terrainComponent->mMaterialOverride;
        componentJson["mEnabled"] = terrainComponent->mEnabled;

        return componentJson;
    }

    void TerrainSystemG::DeserializeTerrainComponent(ISerializeWorld* serializeWorld, const DeserializeNode& componentJson, ecs::IComponent* componentPtr)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "DeserializeTerrainComponent");

        if (!componentJson.IsNull())
        {
            auto enabled = true;
            if (componentJson.HasMember("mEnable"))
            {
                enabled = componentJson["mEnable"].AsBoolean();
            }

            auto terrainComponent = static_cast<TerrainComponentG*>(componentPtr);
            terrainComponent->mTerrainPath = componentJson["mTerrainPath"].AsString();
            if (componentJson.HasMember("mMaterialOverride"))
            {
                terrainComponent->mMaterialOverride = componentJson["mMaterialOverride"].AsString();
            }
            terrainComponent->mEnabled = enabled;
        }
    }

    void TerrainSystemG::PostDeserializeTerrainComponent(const DeserializeNode& componentJson, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityID)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "PostDeserializeTerrainComponent");

        auto terrainSystemG = gameWorld->GetGameSystem<TerrainSystemG>();
        auto terrainComponent = gameWorld->GetComponent<TerrainComponentG>(entityID);

        const auto terrainPath = terrainSystemG->GetTerrainPath(terrainComponent.Read());
        terrainSystemG->SetTerrainPath(terrainComponent.Write(), terrainPath);

        auto prototype = ecs::GetOrCreatePrototypeG<AABBComponentG>();
        gameWorld->AddComponentByPrototype(entityID, prototype);
    }

    void TerrainSystemG::Release()
    {
        for (const auto& terrainComponent : mGameWorld->Query<TerrainComponentG>())
        {
            CleanupTerrain(terrainComponent.Write());
        }

        delete this;
    }

    void TerrainSystemG::OnBeginFrame(FrameParam* frameParam)
    {
        mChangeList.BeginFrame(frameParam, FRAME_STAGE_GAME);
        mPool = std::move(FrameAllocatorPool(frameParam->GetFrameAllocator(), FRAME_STAGE_GAME_RENDER));
        // will be removed
        // tests, could be placed in script
        //{
        //    static auto time = 0.f;
        //    static std::vector<float> accHeightData;

        //    for (const auto& terrainComponent : mGameWorld->Query<TerrainComponentG>())
        //    {
        //        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        //        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(terrainComponent.GetEntityID());
        //        const auto scale = Float4(transformSystemG->GetWorldScale(transformComponent.Read()), 1.f);

        //        auto writer = terrainComponent.Write();
        //        if (scale.y == 100.f)
        //        {
        //            const auto latStart = 37.f * M_PI / 180.f;
        //            const auto latFinish = 31.f * M_PI / 180.f;
        //            const auto latStart1 = 30.f * M_PI / 180.f;
        //            const auto latFinish1 = 26.f * M_PI / 180.f;
        //            const auto lonStart = 84.f * M_PI / 180.f;
        //            const auto lonFinish = 108.f * M_PI / 180.f;
        //            static auto added = false;
        //            static UInt64 handle0 = 0U;
        //            static UInt64 handle1 = 0U;

        //            auto lerp = [](auto a, auto b, auto t) { return (1.f - t) * a + t * b; };

        //            if (added)
        //            {
        //                //RemoveCustomTerrainOverride(writer, handle0);
        //                //RemoveCustomTerrainOverride(writer, handle1);
        //                //added = false;
        //            }

        //            if (!added)
        //            {
        //                auto heightmap = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously("Contents/TerrainData/ZGGG_v2.nda"));
        //                auto heightmapData = heightmap->GetTextureData();
        //                auto heightmapWidth = heightmapData->GetTextureInfo().Width;
        //                auto heightmapHeight = heightmapData->GetTextureInfo().Height;

        //                auto t = std::fmod(time * .01f, 1.f);
        //                t = 0.f;
        //                Float2 latRange{ lerp(latStart, latFinish, t), lerp(latStart, latFinish, t) + M_PI / 180.f * 1.2f };
        //                Float2 lonRange{ lerp(lonStart, lonFinish, t), lerp(lonStart, lonFinish, t) + M_PI / 180.f * 1.2f };
        //                Float2 latRange1{ .0f + latRange.x, latRange.y - .0f };
        //                Float2 lonRange1{ .0f + lonRange.x, lonRange.y - .0f };

        //                Float2 latRangeH{ 23.0548095703125 * M_PI / 180.f, 23.744888305664062 * M_PI / 180.f };
        //                Float2 lonRangeH{ 112.8900146484375 * M_PI / 180.f, 113.7030029296875 * M_PI / 180.f };

        //                //const auto heightmapWidth = static_cast<UInt32>(512 + 113 * std::sin(0.f * .1f));
        //                //const auto heightmapHeight = static_cast<UInt32>(512 + 67 * std::cos(0.f * .07f));
        //                std::vector<float> heightData;
        //                heightData.reserve(heightmapWidth * heightmapHeight);
        //                for (UInt32 i = 0; i != heightmapWidth * heightmapHeight; i++)
        //                {
        //                    //const auto x = M_2PI * i / (heightmapWidth * heightmapHeight);
        //                    //heightData.push_back(std::sin(x + time * .5f) * 4000.f + 4000.f);
        //                    //heightData.push_back(-2000.f);
        //                    auto color = reinterpret_cast<UInt32*>(heightmapData->GetImageData(0))[i];
        //                    heightData.push_back(((color >> 8) & 0xFF) * 256 + (color & 0xFF) - 32768.f);
        //                };

        //                //const auto source = static_cast<float>(heightmapWidth * heightmapHeight);
        //                //const auto divRate = .1f;
        //                //const auto deltaTime = 1.f;
        //                //std::vector<float> heightData(heightmapWidth * heightmapHeight, 0.f);
        //                //if (accHeightData.empty())
        //                //{
        //                //    accHeightData = heightData;

        //                //    for (UInt32 i = 0; i != heightmapWidth; i++)
        //                //    {
        //                //        accHeightData[i] = heightData[i] += source;
        //                //        accHeightData[heightmapWidth * (heightmapHeight - 1U) + i] = heightData[heightmapWidth * (heightmapHeight - 1U) + i] += source;
        //                //    }
        //                //    for (UInt32 i = 0; i != heightmapHeight; i++)
        //                //    {
        //                //        accHeightData[heightmapWidth * i] = heightData[heightmapWidth * i] += source;
        //                //        accHeightData[heightmapWidth * i + heightmapWidth - 1U] = heightData[heightmapWidth * i + heightmapWidth - 1U] += source;
        //                //    }
        //                //}
        //                //else
        //                //{
        //                //    heightData = accHeightData;
        //                //}

        //                //for (UInt32 y = 0U; y != heightmapHeight; y++)
        //                //{
        //                //    const auto tOffset = (y == 0U ? 0U : y - 1U) * heightmapWidth;
        //                //    const auto bOffset = (y == heightmapHeight - 1U ? heightmapHeight - 1U : y + 1U) * heightmapWidth;
        //                //    for (UInt32 x = 0U; x != heightmapWidth; x++)
        //                //    {
        //                //        const auto lOffset = x == 0U ? 0U : x - 1U;
        //                //        const auto rOffset = x == heightmapWidth - 1U ? heightmapWidth - 1U : x + 1U;
        //                //        const auto c = heightData[y * heightmapWidth + x];
        //                //        const auto t = heightData[tOffset + x];
        //                //        const auto b = heightData[bOffset + x];
        //                //        const auto l = heightData[y * heightmapWidth + lOffset];
        //                //        const auto r = heightData[y * heightmapWidth + rOffset];
        //                //        //const auto dt = std::max(0.f, c - t);
        //                //        //const auto db = std::max(0.f, c - b);
        //                //        //const auto dl = std::max(0.f, c - l);
        //                //        //const auto dr = std::max(0.f, c - r);
        //                //        //const auto dw = (0.f < dt) + (0.f < db) + (0.f < dl) + (0.f < dr);
        //                //        //if (dw)
        //                //        //{
        //                //        //    const auto tt = 0.f == dt ? std::numeric_limits<float>::infinity() : dt;
        //                //        //    const auto bb = 0.f == db ? std::numeric_limits<float>::infinity() : db;
        //                //        //    const auto ll = 0.f == dl ? std::numeric_limits<float>::infinity() : dl;
        //                //        //    const auto rr = 0.f == dr ? std::numeric_limits<float>::infinity() : dr;
        //                //        //    const auto w = 1.f / dw;
        //                //        //    const auto flow = std::min(std::min(std::min(tt, bb), std::min(ll, rr)) / (1.f + w), divRate * deltaTime);
        //                //        //    accHeightData[y * heightmapWidth + x] -= flow;
        //                //        //    accHeightData[tOffset + x] += w * (0.f < dt) * flow;
        //                //        //    accHeightData[bOffset + x] += w * (0.f < db) * flow;
        //                //        //    accHeightData[y * heightmapWidth + lOffset] += w * (0.f < dl) * flow;
        //                //        //    accHeightData[y * heightmapWidth + rOffset] += w * (0.f < dr) * flow;
        //                //        //}

        //                //        //if (dw)
        //                //        //{
        //                //        //    const auto w = 1.f / dw;
        //                //        //    const auto d = dt + db + dl + dr;
        //                //        //    const auto flow = std::min(accHeightData[y * heightmapWidth + x], std::min(d, divRate * deltaTime));
        //                //        //    accHeightData[y * heightmapWidth + x] -= flow;
        //                //        //    accHeightData[tOffset + x] += w * (0.f < dt) * flow;
        //                //        //    accHeightData[bOffset + x] += w * (0.f < db) * flow;
        //                //        //    accHeightData[y * heightmapWidth + lOffset] += w * (0.f < dl) * flow;
        //                //        //    accHeightData[y * heightmapWidth + rOffset] += w * (0.f < dr) * flow;
        //                //        //}

        //                //        accHeightData[y * heightmapWidth + x] += (t + b + l + r - 4.f * c) * divRate * deltaTime;
        //                //    }
        //                //}

        //                auto material = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously("Material/CustomTerrain.nda"))->CreateInstance();
        //                auto albedo = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously("Texture/Billy.nda"));
        //                material->SetTexture(TERRAIN_ALBEDO_TEXTURE_NAME, albedo);

        //                handle0 = CreateCustomTerrainOverride(writer, latRangeH, lonRangeH, heightmapWidth, heightmapHeight, heightData.data());
        //                handle1 = CreateCustomTerrainOverride(writer, latRangeH, lonRangeH);

        //                added = true;
        //            }
        //        }
        //        else
        //        {
        //            //transformSystemG->SetWorldScale(transformComponent.Write(), 6000.f + 6.f * .5f * (.99f + std::sin(time * .1f)), .000000001f, .0f);
        //        }
        //    }

        //    time += frameParam->GetDeltaTime();
        //}

        //{
        //    QUICK_SCOPED_CPU_TIMING("Coro");

        //    LOG_INFO("Coro Begin");

        //    auto taskEventPtr0 = threading::Dispatch([](auto)
        //    {
        //        QUICK_SCOPED_CPU_TIMING("Dependent Dispatch");
        //        LOG_INFO("Dependent Dispatch");
        //    });

        //    auto taskEventPtr1 = threading::Async([](auto)
        //    {
        //        QUICK_SCOPED_CPU_TIMING("Dependent Async");
        //        LOG_INFO("Dependent Async");
        //    });

        //    threading::Coro({ taskEventPtr0, taskEventPtr1 }, [magic = 44]() -> threading::CoroDispatchPtr<>
        //    {
        //        QUICK_SCOPED_CPU_TIMING("Coro 0");

        //        LOG_INFO("Coro 0 Begin");

        //        auto r = co_await threading::Coro([magic]() -> threading::CoroAsyncPtr<int>
        //        {
        //            QUICK_SCOPED_CPU_TIMING("Coro 1");
        //            LOG_INFO("Coro 1");
        //            co_return magic - 1;
        //        });

        //        const auto result = co_await threading::Coro([r]() -> threading::CoroDispatchPtr<int>
        //        {
        //            QUICK_SCOPED_CPU_TIMING("Coro 2");
        //            LOG_INFO("Coro 2");
        //            co_return r - 1;
        //        });

        //        LOG_INFO("Coro 0: result from Coro2 -> {}", result);

        //        co_await threading::Coros(threading::Coro([]() -> threading::CoroAsyncPtr<>
        //                                  {
        //                                      QUICK_SCOPED_CPU_TIMING("Coro 3-0");
        //                                      LOG_INFO("Coro 3-0");
        //                                      co_return;
        //                                  }),
        //                                  threading::Coro([]() -> threading::CoroAsyncPtr<>
        //                                  {
        //                                      QUICK_SCOPED_CPU_TIMING("Coro 3-1");
        //                                      LOG_INFO("Coro 3-1");
        //                                      co_return;
        //                                  }));

        //        LOG_INFO("Coro 0 End");

        //        for (auto i = 0; i != 15; i++)
        //        {
        //            co_await threading::Coro([]() -> threading::CoroDispatchPtr<>
        //            {
        //                QUICK_SCOPED_CPU_TIMING("Coro Loop");
        //                co_return;
        //            });
        //        }

        //    }).Resume();

        //    LOG_INFO("Coro End");
        //}
    }

    void TerrainSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
    {
        CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this, frameParam]
        {
            SCOPED_CPU_TIMING(GroupTerrainSystem, "TerrainSystemGUpdate");

            mFrameNumber = frameParam->GetFrameCount();
            mNumPerFrameTileRequests = 0U;

            const auto cameraSystemG = mGameWorld->GetGameSystem<CameraSystemG>();
            const auto mainCamera = cameraSystemG->GetMainCamera();

            if (mGameWorld->IsEntityAlive(mainCamera))
            {
                const auto cameraComponentG = mGameWorld->GetComponent<CameraComponentG>(mainCamera);
                Assert(cameraSystemG->GetProjectionMode(cameraComponentG.Read()) == CameraProjectionMode::Perspective);

                static DataVector<TerrainComponentHandle> sortedTerrainComponents;
                sortedTerrainComponents.clear();

                for (const auto& terrainComponent : mGameWorld->Query<TerrainComponentG>())
                {
                    sortedTerrainComponents.push_back(terrainComponent);                        
                }

                std::sort(sortedTerrainComponents.begin(), sortedTerrainComponents.end(), [this](const auto& lhs, const auto& rhs)
                {
                    const auto infoLHS = GetTerrainInfo(lhs.Read());
                    const auto infoRHS = GetTerrainInfo(rhs.Read());
                    return infoLHS.mLoadingPriority < infoRHS.mLoadingPriority;
                });

                for (const auto& terrainComponent : sortedTerrainComponents)
                {
                    if (auto writer = terrainComponent.Write(); writer->mEnabled && !writer->mTerrainPath.empty())
                    {
                        auto& grid = GetGrid(writer);

                        const auto terrainDim = GetTerrainDimension(ecs::GrantReadAccess(writer));

                        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
                        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(terrainComponent.GetEntityID());

                        TransformAndCameraData transformAndCameraData
                        {
                            Float4(transformSystemG->GetWorldTranslation(transformComponent.Read()), 0.f),
                            Float4(transformSystemG->GetWorldScale(transformComponent.Read()), 1.f),
                            Float4(transformSystemG->GetWorldTranslation(mGameWorld->GetComponent<WorldTransformComponentG>(mainCamera).Read()), cameraSystemG->GetFOV(cameraComponentG.Read())),
                            cameraSystemG->GetFrustum(cameraComponentG.Read()),
                            Float3(cameraSystemG->GetViewMatrix(cameraComponentG.Read()).m02, cameraSystemG->GetViewMatrix(cameraComponentG.Read()).m12, cameraSystemG->GetViewMatrix(cameraComponentG.Read()).m22).Normalized()
                        };

#ifdef CROSSENGINE_EDITOR
                        UpdateDirtyTileList(ecs::GrantReadAccess(writer), terrainDim, grid, transformAndCameraData);
#endif

                        UpdateActiveTiles(writer, terrainDim, grid);

                        transformAndCameraData.mCameraFrustum.Transform(transformAndCameraData.mCameraFrustum, cameraSystemG->GetInvertViewMatrix(cameraComponentG.Read()));

#if defined(CE_USE_DOUBLE_TRANSFORM)
                        Float3 translationTile = transformSystemG->GetWorldTranslationTile(mGameWorld->GetComponent<WorldTransformComponentG>(mainCamera).Read());
                        transformAndCameraData.mCameraFrustum.Transform(transformAndCameraData.mCameraFrustum, 1.0f, Quaternion::Identity(), translationTile * LENGTH_PER_TILE_F);
#endif

                        GenerateTileRequests(writer, terrainDim, grid, transformAndCameraData);

                        DispatchTileStreaming(writer, terrainDim, grid, transformAndCameraData);

                        CalculateLoDTransitionInfo(writer, terrainDim, grid);

                        UpdateActiveTileList(writer, grid);

                        UpdateTileCollision(writer, grid);

                        UpdateRenderState(terrainComponent, terrainDim, grid, transformAndCameraData);

                    }
                }
            }
            else
            {
                // fallback
            }
        });
    }

    void TerrainSystemG::OnEndFrame(FrameParam* frameParam)
    {
    }

    RenderSystemBase* TerrainSystemG::GetRenderSystem()
    {
        //Assert(mTerrainSystemR);

        return mTerrainSystemR;
    }

    Float3 TerrainSystemG::GetWorldScale(const TerrainComponentReader& reader) const {
        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());

        return transformSystemG->GetWorldScale(transformComponent.Read());
    }

    Float3 TerrainSystemG::GetWorldTranslation(const TerrainComponentReader& reader) const 
    {
        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());

        return transformSystemG->GetWorldTranslation(transformComponent.Read());
    }

    float TerrainSystemG::GetWGS84SemiMajor(const TerrainComponentReader& reader) const
    {
        return GetWorldScale(reader).x;
    }

    TerrainSystemG::DataVector<TerrainSystemG::ActiveTile> TerrainSystemG::GetActiveTiles(const TerrainComponentReader& reader) const
    {
        DataVector<ActiveTile> list;
        list.reserve(reader->mActiveTileList.size());

        const auto& grid = GetGrid(reader);
        for (const auto& item : reader->mActiveTileList)
        {
            const auto& node = GetTileNode(grid, item.mTile);
            
            list.push_back({
                {
                    item.mTile.mBlockX,
                    item.mTile.mBlockY,
                    item.mTile.mLevel,
                    item.mTile.mTileX,
                    item.mTile.mTileY
                },
                node.mRenderProperties
            });
        }

        return list;
    }

    UInt64 TerrainSystemG::CreateCustomTerrainOverride(const TerrainComponentWriter& writer, Float2 latRange, Float2 lonRange, UInt32 width, UInt32 height, const float* heightData, const CustomTerrainFlag* heightMask, MaterialPtr material)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "CreateCustomTerrainOverride");

        static UInt64 handle = 0;

        Assert(!heightData || width && height);
        Assert(latRange.x < latRange.y && lonRange.x < lonRange.y);
        Assert(-M_PIDIV2 <= latRange.x && latRange.y <= M_PIDIV2);
        Assert(-M_PI <= lonRange.x && lonRange.y <= M_PI);

        const auto terrainDim = GetTerrainDimension(ecs::GrantReadAccess(writer));
        Assert(terrainDim.mSurfaceType == TerrainSurfaceType::WGS84);

        const auto blockDim = writer->mBlockSize * writer->mTileSize;
        const auto gridDimX = writer->mGridSizeX * blockDim;
        const auto gridDimY = writer->mGridSizeY * blockDim;

        const auto latRangeDim = (latRange + Float2(M_PIDIV2, M_PIDIV2)) * M_1DIVPI * static_cast<float>(terrainDim.mGridDimY);
        const auto lonRangeDim = (lonRange + Float2(M_PI, M_PI)) * M_1DIV2PI * static_cast<float>(terrainDim.mGridDimX);
        const auto rangeY = UInt2(static_cast<UInt32>(std::round(latRangeDim.x)), static_cast<UInt32>(std::round(latRangeDim.y)));
        const auto rangeX = UInt2(static_cast<UInt32>(std::round(lonRangeDim.x)), static_cast<UInt32>(std::round(lonRangeDim.y)));

        const auto levelScale = 1U << (terrainDim.mNumLevels - 1U);
        const auto extRangeY = heightData ? UInt2(rangeY.x & ~(levelScale - 1U), rangeY.y + (rangeY.y & (levelScale - 1U) ? levelScale - (rangeY.y & (levelScale - 1U)) : 0U)) : rangeY;
        const auto extRangeX = heightData ? UInt2(rangeX.x & ~(levelScale - 1U), rangeX.y + (rangeX.y & (levelScale - 1U) ? levelScale - (rangeX.y & (levelScale - 1U)) : 0U)) : rangeX;
        const auto extHeight = heightData ? static_cast<UInt32>(height * static_cast<float>(extRangeY.y - extRangeY.x) / (rangeY.y - rangeY.x)) : height;
        const auto extWidth = heightData ? static_cast<UInt32>(width * static_cast<float>(extRangeX.y - extRangeX.x) / (rangeX.y - rangeX.x)) : width;

        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(writer.GetEntityID());

        TerrainComponentG::CustomTerrainOverride customTerrain
        {
            handle,
            material, 
            {},
            extRangeX,
            extRangeY,
            width,
            height
        };

        if (heightData)
        {
            customTerrain.mEncodedHeightmap.resize(extWidth * extHeight, EncodeHeight(0.f, terrainDim.mSurfaceType));

            auto& grid = GetGrid(writer);
            for (UInt32 y = 0U; y != extHeight; y++)
            {
                const auto offsetY = static_cast<UInt32>(extRangeY.x + y / static_cast<float>(extHeight - 1U) * (extRangeY.y - extRangeY.x));
                const auto indexY = offsetY / terrainDim.mTileSize;
                for (UInt32 x = 0U; x != extWidth; x++)
                {
                    const auto offsetX = static_cast<UInt32>(extRangeX.x + x / static_cast<float>(extWidth - 1U) * (extRangeX.y - extRangeX.x));
                    const auto indexX = offsetX / terrainDim.mTileSize;

                    TerrainComponentG::TileLocator tile
                    {
                        indexX >> (terrainDim.mNumLevels - 1U),
                        indexY >> (terrainDim.mNumLevels - 1U),
                        0U,
                        0U,
                        indexX & (terrainDim.mBlockSize - 1U),
                        indexY & (terrainDim.mBlockSize - 1U),
                        0U
                    };
                    tile.mBlockIndex = tile.mBlockY * terrainDim.mGridSizeX + tile.mBlockX;
                    tile.mTileIndex = tile.mTileY * terrainDim.mBlockSize + tile.mTileX;

                    const auto& node = GetTileNode(grid, tile);

                    if (!node.mResident)
                    {
                        LoadTile(writer, terrainDim, grid, tile);
                    }

                    UInt32 data{};
                    if (node.mResident)
                    {
                        auto heightmap = node.mRenderProperties.GetProperty<TexturePtr>(TERRAIN_HEIGHT_MAP_NAME);
                        const auto baseHeightData = reinterpret_cast<UInt32*>(heightmap->GetTextureData()->GetImageData(0U));
                        data = baseHeightData[offsetY % terrainDim.mTileSize * (terrainDim.mTileSize + 1U) + offsetX % terrainDim.mTileSize];
                    }

                    customTerrain.mEncodedHeightmap[y * extWidth + x] = data;
                }
            }

            const auto yBegin = static_cast<UInt32>(static_cast<float>(rangeY.x - extRangeY.x) / (extRangeY.y - extRangeY.x) * extHeight);
            const auto yEnd = yBegin + height;
            const auto xBegin = static_cast<UInt32>(static_cast<float>(rangeX.x - extRangeX.x) / (extRangeX.y - extRangeX.x) * extWidth);
            const auto xEnd = xBegin + width;
            for (auto y = yBegin; y != yEnd; y++)
            {
                const auto ty = y - yBegin;
                for (auto x = xBegin; x != xEnd; x++)
                {
                    const auto tx = x - xBegin;
                    auto& baseData = customTerrain.mEncodedHeightmap[y * extWidth + x];

                    UInt32 encodedValue{};
                    const auto index = ty * width + tx;
                    if (heightMask)
                    {
                        switch (heightMask[index])
                        {
                            case CustomTerrainFlag::Replace:
                                encodedValue = EncodeHeight(heightData[index], terrainDim.mSurfaceType);
                                break;
                            case CustomTerrainFlag::Keep:
                                encodedValue = baseData;
                                break;
                            case CustomTerrainFlag::Hole:
                                break;
                            default:
                                Assert(false);
                        }
                    }
                    else
                    {
                        encodedValue = EncodeHeight(heightData[index], terrainDim.mSurfaceType);
                    }

                    baseData = baseData ? encodedValue | ((baseData >> 16U) << 16U) : 0U;
                }
            }

            if (material)
            {
                material->SetFloat4("GridDim", Float4(static_cast<float>(gridDimX), static_cast<float>(gridDimY), 1.f / gridDimX, 1.f / gridDimY).data());
                material->SetFloat("PatchSize", static_cast<float>(width | (height << 16U)));   // workaround for material does not support setting an integer
                material->SetFloat("SURFACE_TYPE", static_cast<float>(static_cast<SInt32>(writer->mSurfaceType)));
                material->SetFloat4("WorldTranslation", Float4(transformSystemG->GetWorldTranslation(transformComponent.Read()), 0.f).data());
                material->SetFloat4("WorldScale", Float4(transformSystemG->GetWorldScale(transformComponent.Read()), 1.f).data());
                customTerrain.mRenderProperties.SetProperty("PatchOffsetAndScale", Float4(static_cast<float>(rangeX.x), static_cast<float>(rangeY.x),
                    static_cast<float>(rangeX.y - rangeX.x) / (width - 1U), static_cast<float>(rangeY.y - rangeY.x) / (height - 1U)));

                std::vector<UInt32> heightmapData(width * height);
                for (auto y = yBegin; y != yEnd; y++)
                {
                    for (auto x = xBegin; x != xEnd; x++)
                    {
                        heightmapData[(y - yBegin) * width + x - xBegin] = customTerrain.mEncodedHeightmap[y * extWidth + x];
                    }
                }

                auto heightmap = gResourceMgr.CreateResourceAs<resource::Texture2D>(TextureFormat::RGBA32, ColorSpace::Linear, width, height, 1U, "terrain height map");
                heightmap->UploadImage(0U, reinterpret_cast<UInt8*>(heightmapData.data()), static_cast<UInt32>(heightmapData.size() * sizeof(UInt32)));
                material->SetTexture(TERRAIN_HEIGHT_MAP_NAME, TypeCast<resource::Texture>(heightmap));
            }
        }

        GetTileRangeList(terrainDim, extRangeX, extRangeY, customTerrain.mTileRangeList);

        auto pair = writer->mCustomTerrains.emplace(handle, std::move(customTerrain));
        Assert(pair.second);

        DispatchRenderingCommandWithToken([this,
                                           entity = writer.GetEntityID(),
                                           handleCopy = handle,
                                           extRangeX,
                                           extRangeY,
                                           extWidth,
                                           extHeight,
                                           width,
                                           height,
                                           heightData = pair.first->second.mEncodedHeightmap,
                                           material = material ? TYPE_CAST(MaterialR*, material->GetRenderMaterial()) : nullptr,
                                           renderProperties = pair.first->second.mRenderProperties]()
        {
            mTerrainSystemR->CreateCustomTerrainOverride(entity, handleCopy, extRangeX, extRangeY, extWidth, extHeight, width, height, heightData, material, renderProperties);
        });

#ifdef TERRAIN_DEBUG_CUSTOM_TERRAIN
        GetCustomTerrainDebugData().RecordGCreate(handle);
#endif

        return handle++;
    }

    void TerrainSystemG::RemoveCustomTerrainOverride(const TerrainComponentWriter& writer, UInt64 handle)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "RemoveCustomTerrainOverride");

#ifdef TERRAIN_DEBUG_CUSTOM_TERRAIN
        GetCustomTerrainDebugData().RecordGRemove(handle);
#endif

        const auto count = writer->mCustomTerrains.erase(handle);
        Assert(count);

        DispatchRenderingCommandWithToken([this, entity = writer.GetEntityID(), handle]()
        {
            mTerrainSystemR->RemoveCustomTerrainOverride(entity, handle);
        });
    }

#ifdef CROSSENGINE_EDITOR
    bool TerrainSystemG::InitEditorData(const TerrainComponentWriter& writer, const TileIndex& tileIndex)
    {
        return false;

        Assert(tileIndex.mLevel == 0U);
        Assert(writer->mTileStreamingRequests.empty());

        const auto terrainDim = GetTerrainDimension(ecs::GrantReadAccess(writer));

        const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
        auto& grid = GetGrid(writer);
        auto& node = GetTileNode(grid, tile);

        if (node.mEditorDataIsInitialized)
        {
            return true;
        }

        if (LoadTileWithResidentLock(writer, terrainDim, grid, tile))
        {
            auto heightmap = node.mRenderProperties.GetProperty<TexturePtr>(TERRAIN_HEIGHT_MAP_NAME);
            const auto heightData = reinterpret_cast<UInt32*>(heightmap->GetTextureData()->GetImageData(0U));
            if (node.mHeightmap.empty())
            {
                const auto tileSize = GetTileSize(ecs::GrantReadAccess(writer));
                node.mHeightmap.resize((tileSize + 1U) * (tileSize + 1U));
                for (UInt32 i = 0U; i != node.mHeightmap.size(); i++)
                {
                    node.mHeightmap[i] = DecodeHeight(static_cast<UInt16>(heightData[i]), terrainDim.mSurfaceType);
                }
            }

            {
                std::vector<Float3> positionData((terrainDim.mTileSize + 1U) * (terrainDim.mTileSize + 1U));
                CalculatePositions(tile, terrainDim, {0, 0, 0}, {1, 1, 1}, heightData, positionData.data());
                BoundingBox::CreateFromPoints(node.mBoundingBox, positionData.size(), positionData.data(), sizeof(positionData[0]));
            }

            if (node.mEncodeHeightmap.empty())
            {
                node.mEncodeHeightmap.resize(node.mHeightmap.size());
                memcpy(node.mEncodeHeightmap.data(), heightData, node.mHeightmap.size() * sizeof(node.mEncodeHeightmap[0]));
            }
            node.mEditorDataIsInitialized = 1U;

            return true;
        }
        return false;
    }

    float* TerrainSystemG::GetTileHeightmapData(const TerrainComponentWriter& writer, const TileIndex& tileIndex)
    {
        Assert(tileIndex.mLevel == 0U);
        Assert(writer->mTileStreamingRequests.empty());

        const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
        auto& grid = GetGrid(writer);
        auto& node = GetTileNode(grid, tile);

        if (node.mEditorDataIsInitialized || InitEditorData(writer, tileIndex))
        {
            return node.mHeightmap.data();
        }

        return nullptr;
    }

    UInt32* TerrainSystemG::GetTileEncodeHeightmapData(const TerrainComponentWriter& writer, const TileIndex& tileIndex)
    {
        Assert(tileIndex.mLevel == 0U);
        Assert(writer->mTileStreamingRequests.empty());

        const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
        auto& grid = GetGrid(writer);
        auto& node = GetTileNode(grid, tile);

        if (node.mEditorDataIsInitialized || InitEditorData(writer, tileIndex))
        {
            return node.mEncodeHeightmap.data();
        }

        return nullptr;
    }

    bool TerrainSystemG::GetTileBoundingBox(const TerrainComponentWriter& writer, const TileIndex& tileIndex, Float3& outCenter, Float3& outExtent)
    {
        Assert(tileIndex.mLevel == 0U);

        const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
        auto& grid = GetGrid(writer);
        auto& node = GetTileNode(grid, tile);

        if (node.mEditorDataIsInitialized || InitEditorData(writer, tileIndex))
        {
            node.mBoundingBox.GetCenter(&outCenter);
            node.mBoundingBox.GetExtent(&outExtent);
        }

        return false;
    }

    bool TerrainSystemG::SaveTileHeightmapDataAsPng(const TerrainComponentWriter& writer, const TileIndex& tileIndex, const std::string& outputRoot)
    {
        auto heightmapData = GetTileEncodeHeightmapData(writer, tileIndex);
        if (heightmapData)
        {
            auto terrainInfo = GetTerrainInfo(ecs::GrantReadAccess(writer));
            auto prefixParent = PathHelper::GetParentPath(terrainInfo.mHeightmapPrefix);
            auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mHeightmapPrefix);
            auto outputPath = outputRoot + "/" + prefixParent;

            if (!PathHelper::IsDirectoryExist(outputPath))
            {
                PathHelper::MakeDirectory(outputPath);
            }

            const UInt32 height = terrainInfo.mTileSize + 1, width = height;
            std::string heightMapImagePath = TileIndex::GetPngPath(outputPath, prefixBase, tileIndex);

            lodepng::encode(heightMapImagePath, reinterpret_cast<UInt8*>(heightmapData), width, height);
        }
        return false;
    }

    bool TerrainSystemG::SaveTileWeightTextureAsPng(const TerrainComponentWriter& writer, const TileIndex& tileIndex, const std::string& outputRoot)
    {
        bool success = true;
        auto terrainInfo = GetTerrainInfo(ecs::GrantReadAccess(writer));

        auto numBlendLayers = GetNumBlendLayers(ecs::GrantReadAccess(writer));

        UInt32 wtSize = GetTileLayerWeightSize(writer, tileIndex);
        auto prefixParent = PathHelper::GetParentPath(terrainInfo.mWeightTexturePrefix);
        auto prefixBase = PathHelper::GetBaseFileName(terrainInfo.mWeightTexturePrefix);
        auto outputPath = outputRoot + "/" + prefixParent;

        if (!PathHelper::IsDirectoryExist(outputPath))
        {
            PathHelper::MakeDirectory(outputPath);
        }

        imageio::image image(wtSize, wtSize);
        auto& pixels = image.get_pixels();
        std::fill_n(pixels.data(), pixels.size(), imageio::color_rgba(0, 255));

        for (UInt32 layer = 0, channel = 0; layer != numBlendLayers; layer++, channel++)
        {
            UInt8* weightData = GetTileLayerWeightData(writer, tileIndex, layer);
            if (channel >= NumLayersPerWeightTexture)
            {
                channel = 0;
                std::string weightTexturePath = TileIndex::GetPngPath(outputPath, prefixBase + std::to_string((layer - 1)/ NumLayersPerWeightTexture), tileIndex);
                success &= imageio::save_png(weightTexturePath, image, imageio::cImageSaveAll);
                std::fill_n(pixels.data(), pixels.size(), imageio::color_rgba(0, 255));
            }
            if (weightData)
            {
                for (size_t i = 0; i < image.get_total_pixels(); i++)
                {
                    pixels[i][channel] = weightData[i];
                }
            }
        }

        std::string weightTexturePath = TileIndex::GetPngPath(outputPath, prefixBase + std::to_string((numBlendLayers - 1) / NumLayersPerWeightTexture), tileIndex);
        success &= imageio::save_png(weightTexturePath, image, imageio::cImageSaveAll);
        return success;
    }

    UInt8* TerrainSystemG::GetTileLayerWeightData(const TerrainComponentWriter& writer, const TileIndex& tileIndex, UInt32 layerIndex)
    {
        Assert(tileIndex.mLevel == 0U);
        Assert(writer->mTileStreamingRequests.empty());

        if (layerIndex < GetNumBlendLayers(ecs::GrantReadAccess(writer)))
        {
            const auto terrainDim = GetTerrainDimension(ecs::GrantReadAccess(writer));
            const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
            auto& grid = GetGrid(writer);
            auto& node = GetTileNode(grid, tile);
            if (LoadTileWithResidentLock(writer, terrainDim, grid, tile))
            {
                auto weightTexture = node.mRenderProperties.GetProperty<TexturePtr>(fmt::format("{}{}", TERRAIN_WEIGHT_TEXTURE_NAME, layerIndex / NumLayersPerWeightTexture));
                const auto weightData = reinterpret_cast<UInt32*>(weightTexture->GetTextureData()->GetImageData(0U));
                const auto textureSize = weightTexture->GetWidth();
                auto& layerWeights = node.mBlendLayerWeights[layerIndex];
                if (layerWeights.empty())
                {
                    layerWeights.resize(textureSize * textureSize);
                    for (UInt32 i = 0U; i != layerWeights.size(); i++)
                    {
                        layerWeights[i] = static_cast<UInt8>(weightData[i] >> (8U * (layerIndex % NumLayersPerWeightTexture)));
                    }
                }

                return layerWeights.data();
            }
        }

        return nullptr;
    }

    UInt32 TerrainSystemG::GetTileLayerWeightSize(const TerrainComponentWriter& writer, const TileIndex& tileIndex)
    {
        Assert(tileIndex.mLevel == 0U);
        Assert(writer->mTileStreamingRequests.empty());

        const auto tile = ToTileLocator(ecs::GrantReadAccess(writer), tileIndex);
        auto& grid = GetGrid(writer);
        auto& node = GetTileNode(grid, tile);
        if (node.mResident)
        {
            return writer->mTextureSize;
        }

        return 0U;
    }

    std::vector<TerrainLayerTexture> TerrainSystemG::GetBlendLayers(const TerrainComponentReader& reader) const
    {
        std::vector<TerrainLayerTexture> layers;
        layers.resize(GetNumBlendLayers(reader));

        for (UInt32 i = 0; i != layers.size(); i++)
        {
            layers[i].mBaseColorTexture = reader->mBaseColorTextureNameList[i];
            layers[i].mNormalTexture = reader->mNormalTextureNameList[i];
            layers[i].mHMRATexture = reader->mHMRATextureNameList[i];
        }

        return layers;
    }

    void TerrainSystemG::SetBlendLayers(const TerrainComponentWriter& writer, const std::vector<TerrainLayerTexture>& layers)
    {
        Assert(GetNumBlendLayers(ecs::GrantReadAccess(writer)));

        writer->mBaseColorTextureNameList.resize(layers.size());
        writer->mNormalTextureNameList.resize(layers.size());
        writer->mHMRATextureNameList.resize(layers.size());

        for (UInt32 i = 0; i != layers.size(); i++)
        {
            writer->mBaseColorTextureNameList[i] = layers[i].mBaseColorTexture;
            writer->mNormalTextureNameList[i] = layers[i].mNormalTexture;
            writer->mHMRATextureNameList[i] = layers[i].mHMRATexture;
        }

        LoadAndUpdateLayerTextures(writer);
    }

    Float3 TerrainSystemG::GetTileVertexWorldPosition(const TerrainComponentWriter& writer, const TileIndex& tileIndex, UInt32 vertexIndex)
    {
        if (const auto heightmapData = GetTileHeightmapData(writer, tileIndex))
        {
            const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
            const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(writer.GetEntityID());
            Float3 worldTranslation = transformSystemG->GetWorldTranslation(transformComponent.Read());
            Float3 worldScale = transformSystemG->GetWorldScale(transformComponent.Read());
            return GetTileVertexWorldPosition(ecs::GrantReadAccess(writer), worldTranslation, worldScale, tileIndex, vertexIndex, heightmapData);
        }
        else
        {   
            constexpr auto fNaN = std::numeric_limits<float>::quiet_NaN();
            return { fNaN, fNaN, fNaN };
        }
    }

    Float3 TerrainSystemG::GetTileVertexWorldPosition(const TerrainComponentReader& reader, const Float3& worldTranslation, const Float3& worldScale, const TileIndex& tileIndex, UInt32 vertexIndex, const float* heightmapData) const
    {
        Assert(heightmapData);

        const auto patchCoordX = static_cast<float>(vertexIndex % (GetTileSize(reader) + 1U));
        const auto patchCoordY = static_cast<float>(vertexIndex / (GetTileSize(reader) + 1U));
        const auto patchCoords = Float3(patchCoordX, 0.f, patchCoordY);

        const auto offsetX = static_cast<float>(GetTileSize(reader) * (GetBlockSize(reader) * tileIndex.mBlockX + tileIndex.mTileX * (1U << tileIndex.mLevel)));
        const auto offsetY = static_cast<float>(GetTileSize(reader) * (GetBlockSize(reader) * tileIndex.mBlockY + tileIndex.mTileY * (1U << tileIndex.mLevel)));
        const auto patchOffsetAndScale = Float4(offsetX, 0.f, offsetY, static_cast<float>(1U << tileIndex.mLevel));

        const auto surfaceType = GetSurfaceType(reader);
        if (surfaceType == TerrainSurfaceType::Flat)
        {
            return CalculateFlatPosition(patchOffsetAndScale, worldTranslation, worldScale, patchCoords, heightmapData[vertexIndex]);
        }
     
        if (surfaceType == TerrainSurfaceType::Spherical)
        {
            const auto gridDimRcp = Float2(1.f / static_cast<float>(GetGridDimX(reader)), 1.f / static_cast<float>(GetGridDimY(reader)));
            return CalculateSphericalPosition(gridDimRcp, patchOffsetAndScale, worldTranslation, worldScale, patchCoords, heightmapData[vertexIndex]);
        }

        Assert(surfaceType == TerrainSurfaceType::WGS84);
        {
            const auto gridDimRcp = Float2(1.f / static_cast<float>(GetGridDimX(reader)), 1.f / static_cast<float>(GetGridDimY(reader)));
            return CalculateWGS84Position(gridDimRcp, patchOffsetAndScale, worldTranslation, worldScale, patchCoords, heightmapData[vertexIndex]);
        }
    }

    void TerrainSystemG::SaveLayers(const TerrainComponentWriter& writer)
    {
        auto terrainInfo = GetTerrainInfo(ecs::GrantReadAccess(writer));
        terrainInfo.mBaseColorTextures = writer->mBaseColorTextureNameList;
        terrainInfo.mNormalTextures = writer->mNormalTextureNameList;
        terrainInfo.mHMRATextures = writer->mHMRATextureNameList;
        TerrainResourcePtr terrainRes = gResourceMgr.CreateResourceAs<resource::TerrainResource>();
        terrainRes->mTerrainInfo = terrainInfo;

        auto terrainPath = GetTerrainPath(ecs::GrantReadAccess(writer));
        terrainRes->Serialize(gResourceMgr.ConvertGuidToPath(terrainPath));
    }
#endif

    TerrainComponentG::TileLocator TerrainSystemG::GetLeftTile(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim)
    {
        const auto blockSize = terrainDim.mBlockSize >> tile.mLevel;
        auto ret = tile;

        if (ret.mTileX > 0)
        {
            ret.mTileX--;
        }
        else
        {
            ret.mTileX = blockSize - 1;
            if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat && ret.mBlockX == 0)
            {
                ret.mBlockX = terrainDim.mGridDimX - 1;
            }
            else
            {
                ret.mBlockX--;
            }
        }
        ret.mBlockIndex = ret.mBlockY * terrainDim.mGridSizeX + ret.mBlockX;
        ret.mTileIndex = ret.mTileY * terrainDim.mBlockSize + ret.mTileX;
        return ret;
    }

    TerrainComponentG::TileLocator TerrainSystemG::GetRightTile(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim)
    {
        const auto blockSize = terrainDim.mBlockSize >> tile.mLevel;
        auto ret = tile;
        if (ret.mTileX < blockSize - 1)
        {
            ret.mTileX++;
        }
        else
        {
            ret.mTileX = 0;
            if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat && ret.mBlockX == terrainDim.mGridDimX - 1)
            {
                ret.mBlockX = 0;
            }
            else
            {
                ret.mBlockX++;
            }
        }
        ret.mBlockIndex = ret.mBlockY * terrainDim.mGridSizeX + ret.mBlockX;
        ret.mTileIndex = ret.mTileY * terrainDim.mBlockSize + ret.mTileX;
        return ret;
    }

    TerrainComponentG::TileLocator TerrainSystemG::GetUpTile(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim)
    {
        const auto blockSize = terrainDim.mBlockSize >> tile.mLevel;
        auto ret = tile;

        if (ret.mTileY > 0)
        {
            ret.mTileY--;
        }
        else
        {
            ret.mTileY = blockSize - 1;
            if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat && ret.mBlockY == 0)
            {
                ret.mBlockY = terrainDim.mGridDimY - 1;
            }
            else
            {
                ret.mBlockY--;
            }
        }
        ret.mBlockIndex = ret.mBlockY * terrainDim.mGridSizeX + ret.mBlockX;
        ret.mTileIndex = ret.mTileY * terrainDim.mBlockSize + ret.mTileX;
        return ret;
    }

    TerrainComponentG::TileLocator TerrainSystemG::GetDownTile(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim)
    {
        const auto blockSize = terrainDim.mBlockSize >> tile.mLevel;
        auto ret = tile;
        if (ret.mTileY < blockSize - 1)
        {
            ret.mTileY++;
        }
        else
        {
            ret.mTileY = 0;
            if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat && ret.mBlockY == terrainDim.mGridDimY - 1)
            {
                ret.mBlockY = 0;
            }
            else
            {
                ret.mBlockY++;
            }
        }
        ret.mBlockIndex = ret.mBlockY * terrainDim.mGridSizeX + ret.mBlockX;
        ret.mTileIndex = ret.mTileY * terrainDim.mBlockSize + ret.mTileX;
        return ret;
    }

    TerrainSystemG::TerrainSystemG() : mTerrainSystemR(EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeHeadless ? nullptr : new TerrainSystemR)
    {
    }

    TerrainSystemG::~TerrainSystemG()
    {
        if (mIsRenderObjectOwner && mTerrainSystemR)
        {
            mTerrainSystemR->Release();
        }

        mTerrainSystemR = nullptr;
    }

    void TerrainSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
    {
        GameSystemBase::NotifyEvent(event, flag);

        if (event.mEventType == TRSChangedEvent::sEventType)
        {
            const auto& e = TYPE_CAST(const TRSChangedEvent&, event);
            const auto& eventData = e.mData;
            const auto entityId = eventData.mEntity;

            if (eventData.mEventFlag & TRSEventFlagTRSChanged)
            {
                auto terrainComponent = mGameWorld->GetComponent<TerrainComponentG>(entityId);
                if (terrainComponent.IsValid())
                {
                    terrainComponent.Write()->mTransformUpdated = true;
                }
            }
        }
    }

    void TerrainSystemG::OnEntityPropChange(ecs::EntityID entity, ecs::EntityDescFlags::EntityProps prop, bool value)
    {
        if (prop == ecs::EntityDescFlags::Visibility)
        {
            auto component = mGameWorld->GetComponent<cross::TerrainComponentG>(entity);
            if (component.IsValid())
            {
                SetTerrainEnable(component.Write(), value);
            }
        }
    }

    void TerrainSystemG::ResetTerrain(const TerrainComponentWriter& writer)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "ResetTerrain");

        CleanupTerrain(writer);
    }

    void TerrainSystemG::CleanupTerrain(const TerrainComponentWriter& writer)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "CleanupTerrain");

        Assert(writer->mTileStreamingRequests.empty());
        Assert(writer->mDrawableTileList.empty());

        writer->mTransformUpdated = true;

        auto& grid = GetGrid(writer);
        std::transform(writer->mActiveTileList.begin(), writer->mActiveTileList.end(), std::back_inserter(writer->mPendingReleaseList), [this, &writer, &grid](const auto& tile)
        {
            auto& node = GetTileNode(grid, tile.mTile);
            ClearTileUsage(node);
            ResetTileNode(writer, tile.mTile, node);
            return tile.mTileAssetData;
        });

        std::transform(writer->mAlwaysResidentTileList.begin(), writer->mAlwaysResidentTileList.end(), std::back_inserter(writer->mPendingReleaseList), [this, &writer, &grid](const auto& tile)
        {
            auto& node = GetTileNode(grid, tile.mTile);
            ClearTileUsage(node);
            ResetTileNode(writer, tile.mTile, node);
            return tile.mTileAssetData; 
        });

        writer->mActiveTileList.clear();
        writer->mAlwaysResidentTileList.clear();

        if (!writer->mPendingReleaseList.empty())
        {
            PostTickUpdates::Get()->Add([pendingReleaseList = std::make_shared<DataVector<resource::TerrainTileAssetData>>(std::move(writer->mPendingReleaseList))]
            {
                auto release = [pendingReleaseList](auto self) -> void
                {
                    pendingReleaseList->erase(std::remove_if(pendingReleaseList->begin(), pendingReleaseList->end(), [](auto& tileAssetData)
                    {
                        return tileAssetData.ReleaseResources();
                    }), pendingReleaseList->end());

                    if (!pendingReleaseList->empty())
                    {
                        cross::PostTickUpdates::Get()->Add([pendingReleaseList, self]
                        {
                            self(self);
                        });
                    }
                };

                release(release);
            });

            Assert(writer->mPendingReleaseList.empty());
        }
        auto pool = &mPool;
        TransferVector<TerrainComponentR::SlotUpdate> removedTileListUpdate{pool};
        removedTileListUpdate.reserve(writer->mRemovedTileList.size());
        for (const auto& tile : writer->mRemovedTileList)
        {
            const auto& node = GetTileNode(grid, tile);
            removedTileListUpdate.push_back(TerrainComponentR::SlotUpdate{ TerrainRenderProperties{}, node.mSlotIndex });
        }

        writer->mAddedTileList.clear();
        writer->mRemovedTileList.clear();

        writer->mLocalSlotIndexTable.Clear();

        writer->mCustomTerrains.clear();

        writer->mGrid.clear();

        //Assert(mTerrainSystemR);
        DispatchRenderingCommandWithToken([terrainSystemR = mTerrainSystemR, entity = writer.GetEntityID(), removedTileList = std::move(removedTileListUpdate)]() mutable
        {
            terrainSystemR->CleanupTerrain(entity, std::move(removedTileList));
        });
    }

    void TerrainSystemG::InitTerrain(const TerrainComponentWriter& writer)
    {
        SCOPED_CPU_TIMING(GroupSerialize, "InitTerrain");

        const auto blackTexture = "EngineResource/Texture/BlackTexture.nda";
        const auto baseColorTexture = "EngineResource/Texture/DefaultWhiteGrid.nda";
        const auto normalTexturePath = "EngineResource/Texture/TerrainDefaultNormal.nda";
        const auto hmraTexturePath = "EngineResource/Texture/TerrainDefaultHMRA.nda";
        mBlackTexture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(blackTexture));
        mDefaultBaseColorTexture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(baseColorTexture));
        mDefaultNormalTexture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(normalTexturePath));
        mDefaultHMRATexture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(hmraTexturePath));

        auto terrainResourcePtr = TypeCast<resource::TerrainResource>(gAssetStreamingManager->LoadSynchronously(writer->mTerrainPath));
        Assert(terrainResourcePtr);

#if CROSSENGINE_EDITOR
        if (terrainResourcePtr->NeedReload())
        {
            gResourceMgr.ReloadResource(TypeCast<Resource>(terrainResourcePtr));
        }
#endif

        const auto& terrainInfo = terrainResourcePtr->mTerrainInfo;
        Assert(terrainInfo.IsValid());

        writer->mTerrainResourcePtr = terrainResourcePtr;
        writer->mSurfaceType = terrainInfo.mSurfaceType;
        writer->mGridSizeX = terrainInfo.mGridSizeX;
        writer->mGridSizeY = terrainInfo.mGridSizeY;
        writer->mBlockSize = terrainInfo.mBlockSize;
        writer->mTileSize = terrainInfo.mTileSize;
        writer->mTextureSize = 0U;
        writer->mTexelDensity = terrainInfo.mTexelDensity;
        writer->mRootDataPath = terrainInfo.mRootDataPath;
        writer->mBaseColorTextureNameList = terrainInfo.mBaseColorTextures;
        writer->mNormalTextureNameList = terrainInfo.mNormalTextures;
        writer->mHMRATextureNameList = terrainInfo.mHMRATextures;
        writer->mHeightmapName = terrainInfo.mHeightmapPrefix;
        writer->mAlbedoTextureName = terrainInfo.mAlbedoTexturePrefix;
        writer->mWeightTextureName = terrainInfo.mWeightTexturePrefix;

        {
            if (writer->mMaterialOverride.empty())
            {
                if (terrainInfo.mMaterialPath.empty())
                {
                    // temporary code
                    if (EngineGlobal::GetSettingMgr()->GetRenderPipelineSettingForEditor().UseRenderPipeline == "UseFFSRP")
                    {
                        if (terrainInfo.mSurfaceType == TerrainSurfaceType::Flat)
                        {
                            // For DGW custom terrain effect, use project mtl temporary
                            if (EngineGlobal::GetFileSystem()->HaveFile("Contents/TerrainMaterial/Terrain_Flat.mtl.nda"))
                            {
                                writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously("Contents/TerrainMaterial/Terrain_Flat.mtl.nda"))->CreateInstance();
                            }
                            else
                            {
                                writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously("Material/Terrain_Flat.nda"))->CreateInstance();
                            }
                        }
                        else
                        {
                            writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously("Material/Terrain.nda"))->CreateInstance();
                        }
                    }
                    else
                    {
                        writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/TerrainMaterial.nda"))->CreateInstance();
                    }
                }
                else
                {
                    // to-do: validation
                    writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(terrainInfo.mMaterialPath))->CreateInstance();
                }
            }
            else
            {
                writer->mMaterial = TypeCast<resource::MaterialInterface>(gAssetStreamingManager->LoadSynchronously(writer->mMaterialOverride))->CreateInstance();
            }

            const auto blockDim = writer->mBlockSize * writer->mTileSize;
            const auto gridDimX = writer->mGridSizeX * blockDim;
            const auto gridDimY = writer->mGridSizeY * blockDim;
            writer->mMaterial->SetFloat4("GridDim", Float4(static_cast<float>(gridDimX), static_cast<float>(gridDimY), 1.f / gridDimX, 1.f / gridDimY).data());
            writer->mMaterial->SetFloat("PatchSize", static_cast<float>(writer->mTileSize));   // workaround for material does not support setting an integer
            writer->mMaterial->SetFloat("SURFACE_TYPE", static_cast<float>(static_cast<SInt32>(writer->mSurfaceType)));

            if (UseWeightBlend(ecs::GrantReadAccess(writer)))
            {
                writer->mMaterial->SetBool("USE_WEIGHT_BLEND", true);
                LoadAndUpdateLayerTextures(writer);
            }
            else
            {
                writer->mMaterial->SetBool("USE_WEIGHT_BLEND", false);
            }

#if TERRAIN_USE_INSTANCING
            if (UseWeightBlend(ecs::GrantReadAccess(writer)))
            {
                writer->mMaterial->SetBool("TERRAIN_USE_INSTANCING", false);
            }
            else
            {
                writer->mMaterial->SetBool("TERRAIN_USE_INSTANCING", true);
            }
            writer->mMaterial->SetBool("CE_INSTANCING", TERRAIN_USE_GPU_SCENE);
#endif
        }

        terrainResourcePtr->WaitForDefaultResources();
        for (UInt32 blockY = 0U; blockY != writer->mGridSizeY; blockY++)
        {
            for (UInt32 blockX = 0U; blockX != writer->mGridSizeX; blockX++)
            {
#if TERRAIN_USE_ARRAY_GRID
                writer->mGrid.push_back({});
                auto& block = writer->mGrid.back();
                for (UInt32 level = 0U, blockSize = writer->mBlockSize; blockSize; level++, blockSize >>= 1U)
                {
                    block.push_back({});
                    for (UInt32 tileY = 0U; tileY != blockSize; tileY++)
                    {
                        for (UInt32 tileX = 0U; tileX != blockSize; tileX++)
                        {
                            block.back().push_back({});
                            auto& node = block.back().back();

                            if (blockSize == 1U)
                            {
                                node.mTouched = 1U;

                                if (writer->mTerrainResourcePtr->DoesTileExists(blockX, blockY, level, 0U, 0U))
                                {
                                    const auto tile = TerrainComponentG::TileLocator{ blockX, blockY, blockY * writer->mGridSizeX + blockX, level, tileX, tileY, tileY * blockSize + tileX };
                                    StartTileStreaming(writer->mTerrainResourcePtr, tile, node, writer->mAlwaysResidentTileList);

                                    node.mInvalid = 0U;
                                    node.mAlwaysResident = 1U;
                                }
                                else
                                {
                                    node.mInvalid = 1U;
                                }
                            }
                        }
                    }
                }
#else
                TerrainComponentG::TileNode node{};
                node.mTouched = 1U;

                const auto level = static_cast<UInt32>(std::countr_zero(writer->mBlockSize));
                const auto tile = TerrainComponentG::TileLocator{ blockX, blockY, blockY * writer->mGridSizeX + blockX, level, 0U, 0U, 0U };
                if (writer->mTerrainResourcePtr->DoesTileExists(blockX, blockY, level, 0U, 0U))
                {
                    node.mInvalid = 0U;
                    node.mAlwaysResident = 1U;

                    StartTileStreaming(writer->mTerrainResourcePtr, tile, node, writer->mAlwaysResidentTileList);
                }
                else
                {
                    node.mInvalid = 1U;
                }

                writer->mGrid.emplace(tile, std::move(node));
#endif
            }
        }

        const auto terrainDim = GetTerrainDimension(ecs::GrantReadAccess(writer));

        for (auto& [tile, tileAssetData, frameNumber] : writer->mAlwaysResidentTileList)
        {
            auto& node = GetTileNode(writer->mGrid, tile);

            tileAssetData.WaitForStreaming();
            node.mResident = 1U;
            node.mStreaming = 0U;
            node.mRequested = 0U;
            node.mDrawable = 1U;
            node.mVisible = 0U;
            CompleteTileStreaming(writer, terrainDim, tile, node, tileAssetData);

            {
                const auto texture = tileAssetData.GetNumWeightTexture() ? tileAssetData.GetWeightTexture(0U) : tileAssetData.GetAlbedoTexture();
                const auto width = texture->GetWidth();
                const auto height = texture->GetHeight();
                Assert(width == height);

                Assert(writer->mTextureSize == 0U || writer->mTextureSize == width);
                writer->mTextureSize = width;
            }

            frameNumber = mFrameNumber;
        }

        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(writer.GetEntityID());
        if (phySys && phyComp)
        {
            phySys->InitPhysics(phyComp.Write());
        }

        SetTerrainEntityAlwaysVisible(writer.GetEntityID());

        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(writer.GetEntityID());
        transformSystemG->AddMovementEventListener(transformComponent.Write(), this);
    }

    void TerrainSystemG::GenerateTileRequests(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TransformAndCameraData& transformAndCameraData)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "GenerateTileRequests");

        auto viewResolution = 0.f;
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor)
        {
            auto renderTexture = mGameWorld->GetGameSystem<RenderPipelineSystemG>()->GetEditorSceneViewTexture();
            viewResolution = static_cast<float>(renderTexture->GetHeight());
        }
        else
        {
            const auto windowSystem = static_cast<WindowSystemG*>(EngineGlobal::GetEngine()->GetGlobalSystemByID(WindowSystemG::GetDesc().mID));
            const auto window = windowSystem->GetAppGlobalWindow();
            viewResolution = static_cast<float>(window->GetHeight());
        }

        const auto halfViewResolution = .5f * viewResolution;
        const auto halfFovY = transformAndCameraData.mCameraPositionAndFovy.w * .5f;
        const auto pixelError = EngineGlobal::GetSettingMgr()->GetTerrainLoDPixelError();
        const auto baseLodDistance = halfViewResolution * GetTerrainScale(terrainDim, transformAndCameraData) / writer->mTexelDensity / std::tan(halfFovY) / pixelError;

        Assert(writer->mDrawableTileList.empty());
        for (UInt32 blockY = 0U; blockY != terrainDim.mGridSizeY; blockY++)
        {
            for (UInt32 blockX = 0; blockX != terrainDim.mGridSizeX; blockX++)
            {
                TerrainComponentG::TileLocator tile{ blockX, blockY, blockY * terrainDim.mGridSizeX + blockX, terrainDim.mNumLevels - 1U, 0U, 0U, 0U };
                const auto lodDistance = baseLodDistance * terrainDim.mBlockSize;

                const auto& node = GetTileNode(grid, tile);
                Assert(!!node.mTouched);

                if (!node.mInvalid)
                {
                    if (ShouldSubdivide(terrainDim, grid, tile, transformAndCameraData, lodDistance))
                    {
                        GenerateTileRequestsImpl(writer, terrainDim, grid, transformAndCameraData, tile, 2U, lodDistance * mLoDDistanceScaleFactor);
                    }
                    else
                    {
                        writer->mDrawableTileList.push_back(tile);
                    }
                }
            }
        }
    }

    void TerrainSystemG::GenerateTileRequestsImpl(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TransformAndCameraData& transformAndCameraData,
        const TerrainComponentG::TileLocator& parentTile, UInt32 blockSize, float lodDistance)
    {
        if (parentTile.mLevel == 0U)
        {
            writer->mDrawableTileList.push_back(parentTile);
        }
        else
        {
            TerrainComponentG::TileLocator tile{ parentTile };
            tile.mLevel--;

            bool bShouldSubdivide[4]{};
            auto numInvalidSubTiles = 0;
            auto numResidentSubTiles = 0;
            for (UInt32 i = 0U; i != 2U; i++)
            {
                for (UInt32 j = 0U; j != 2U; j++)
                {
                    tile.mTileX = (parentTile.mTileX << 1U) + j;
                    tile.mTileY = (parentTile.mTileY << 1U) + i;
                    tile.mTileIndex = tile.mTileY * blockSize + tile.mTileX;

                    auto& node = GetTileNode(grid, tile);
                    if (!node.mTouched)
                    {
                        node.mTouched = 1U;
                        node.mInvalid = 1U - writer->mTerrainResourcePtr->DoesTileExists(tile.mBlockX, tile.mBlockY, tile.mLevel, tile.mTileX, tile.mTileY);
                    }

                    if (!node.mInvalid)
                    {
                        Assert(!!node.mTouched);
                        RecordTileUsage(node);

                        TerrainComponentG::TileRequestData data{ tile };
                        bShouldSubdivide[i * 2 + j] = ShouldSubdivide(terrainDim, grid, tile, transformAndCameraData, lodDistance, &data);

                        if (!node.mResident && !node.mStreaming)
                        {
                            writer->mTileStreamingRequests.push_back(data);
                        }
                    }

                    numInvalidSubTiles += node.mInvalid;
                    numResidentSubTiles += node.mResident;
                }
            }
       
            if (numInvalidSubTiles < 4 && numResidentSubTiles == 4 - numInvalidSubTiles)
            {
                for (UInt32 i = 0U; i != 2U; i++)
                {
                    for (UInt32 j = 0U; j != 2U; j++)
                    {
                        tile.mTileX = (parentTile.mTileX << 1U) + j;
                        tile.mTileY = (parentTile.mTileY << 1U) + i;
                        tile.mTileIndex = tile.mTileY * blockSize + tile.mTileX;

                        auto& node = GetTileNode(grid, tile);
                        if (!node.mInvalid)
                        {
                            node.mDrawable = 1U;

                            if (bShouldSubdivide[i * 2 + j])
                            {
                                GenerateTileRequestsImpl(writer, terrainDim, grid, transformAndCameraData, tile, blockSize << 1U, lodDistance * mLoDDistanceScaleFactor);
                            }
                            else
                            {
                                writer->mDrawableTileList.push_back(tile);
                            }
                        }
                    }
                }
            }
            else
            {
                writer->mDrawableTileList.push_back(parentTile);
            }
        }
    }

    bool TerrainSystemG::ShouldSubdivide(const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TerrainComponentG::TileLocator& tile, const TransformAndCameraData& transformAndCameraData,
        float lodDistance, TerrainComponentG::TileRequestData* data)
    {
        auto& node = GetTileNode(grid, tile);
        const auto tileSize = static_cast<float>(terrainDim.mTileSize * (1U << tile.mLevel));
        const auto tileX = (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel)) * terrainDim.mTileSize;
        const auto tileY = (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel)) * terrainDim.mTileSize;

        switch (terrainDim.mSurfaceType)
        {
            case TerrainSurfaceType::Flat:
            {
                const auto cosHalfAlpha = std::cos(transformAndCameraData.mCameraPositionAndFovy.w * .5f);
                const auto sinHalfAplha = std::sqrt(1.f - std::min(1.f, cosHalfAlpha * cosHalfAlpha));
                const auto cosTheta = std::min(1.f, std::max(-1.f, -transformAndCameraData.mCameraForward.y));
                const auto sinTheta = std::sqrt(1.f - cosTheta * cosTheta);
                const auto directionalAdjustment = cosTheta < cosHalfAlpha ? cosTheta * cosHalfAlpha + sinTheta * sinHalfAplha : 1.f;
                lodDistance *= std::max(0.f, directionalAdjustment);

                // to-do: add bounding info to tile resource
                const auto offset = Float3(static_cast<float>(tileX), 0.f, static_cast<float>(tileY)) * transformAndCameraData.mWorldScale.XYZ() + transformAndCameraData.mWorldTranslation.XYZ();
                const auto extent = .5f * Float3(static_cast<float>(tileSize), 0.f, static_cast<float>(tileSize)) * transformAndCameraData.mWorldScale.XYZ();
                const auto center = offset + extent;
                const auto radius = std::max(32768.f * TerrainYScale * transformAndCameraData.mWorldScale.y, extent.Length());

                BoundingBox bBox{ center, Float3(extent.x, radius, extent.z) };
                node.mVisible = /*0.f < directionalAdjustment &&*/ transformAndCameraData.mCameraFrustum.Intersects(bBox);

#if DEBUG_TILE_CULLING
                {
                    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                    Float3 bBoxCenter;
                    Float3 bBoxExtent;
                    bBox.GetCenter(&bBoxCenter);
                    bBox.GetExtent(&bBoxExtent);
                    PrimitiveData primData;
                    PrimitiveGenerator::GenerateCubeFrame(&primData, bBoxExtent, bBoxCenter);
                    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.0), PrimitiveDepth::SceneDepth, static_cast<UInt8>(tile.mLevel + 1U)));
                }
#endif

                if (data)
                {                  
                    BoundingSphere::CreateFromBoundingBox(data->mBound, bBox);
                }

                const auto length = (transformAndCameraData.mCameraPositionAndFovy.XYZ() - center).Length();
                return length < lodDistance + radius;
            }
            case TerrainSurfaceType::Spherical:
            {
                const auto phi0 = static_cast<float>(M_PI) * (1.f - tileY / static_cast<float>(terrainDim.mGridDimY));
                const auto theta0 = static_cast<float>(M_2PI) * tileX / static_cast<float>(terrainDim.mGridDimX);
                const auto phi1 = static_cast<float>(M_PI) * (1.f - (tileY + tileSize) / static_cast<float>(terrainDim.mGridDimY));
                const auto theta1 = static_cast<float>(M_2PI) * (tileX + tileSize) / static_cast<float>(terrainDim.mGridDimX);
                const auto phi2 = (phi0 + phi1) * .5f;
                const auto theta2 = (theta0 + theta1) * .5f;
                const auto radius = transformAndCameraData.mWorldScale.x;

                const auto cosPhi0 = std::cos(phi0);
                const auto sinPhi0 = std::sin(phi0);
                const auto cosTheta0 = std::cos(theta0);
                const auto sinTheta0 = std::sin(theta0);

                const auto cosPhi1 = std::cos(phi1);
                const auto sinPhi1 = std::sin(phi1);
                const auto cosTheta1 = std::cos(theta1);        
                const auto sinTheta1 = std::sin(theta1);

                const Float3 points[5] =
                {
                    radius * Float3(sinPhi0 * cosTheta0, cosPhi0, sinPhi0 * sinTheta0) + transformAndCameraData.mWorldTranslation.XYZ(),
                    radius * Float3(sinPhi0 * cosTheta1, cosPhi0, sinPhi0 * sinTheta1) + transformAndCameraData.mWorldTranslation.XYZ(),
                    radius * Float3(sinPhi1 * cosTheta0, cosPhi1, sinPhi1 * sinTheta0) + transformAndCameraData.mWorldTranslation.XYZ(),
                    radius * Float3(sinPhi1 * cosTheta1, cosPhi1, sinPhi1 * sinTheta1) + transformAndCameraData.mWorldTranslation.XYZ(),
                    radius * Float3(std::sin(phi2) * std::cos(theta2), std::cos(phi2), std::sin(phi2) * std::sin(theta2)) + transformAndCameraData.mWorldTranslation.XYZ()
                };

                BoundingBox bBox;
                BoundingBox::CreateFromPoints(bBox, 5, points, sizeof(Float3));

                auto normal = static_cast<float>(M_PIDIV2) < phi0 ? (points[2] - points[3]).Cross(points[1] - points[3]) : (points[1] - points[0]).Cross(points[2] - points[0]);
                normal.Normalize();

                node.mVisible = transformAndCameraData.mCameraFrustum.Intersects(bBox); // && -.831f < transformAndCameraData.mCameraForward.Dot(normal); // hard-coded for now

                BoundingSphere bSphere{ transformAndCameraData.mCameraPositionAndFovy.XYZ(), lodDistance * (static_cast<float>(M_PIDIV2) < phi0 ? sinPhi1 : sinPhi0) };

                if (bSphere.Intersects(points[0], points[2], points[4]) || bSphere.Intersects(points[4], points[3], points[1]))
                {
                    return true;
                }

                if (phi0 == static_cast<float>(M_PI))
                {
                    return bSphere.Intersects(points[4], points[2], points[3]);
                }

                if (phi1 == 0.f)
                {
                    return bSphere.Intersects(points[4], points[1], points[0]);
                }

                return bSphere.Intersects(points[1], points[0], points[4]) || bSphere.Intersects(points[4], points[2], points[3]);
            }
            case TerrainSurfaceType::WGS84:
            {
                constexpr auto b = .9966471893f;
                constexpr auto b2 = b * b;
                constexpr auto e2 = 1.f - b2;
                constexpr auto alt = 9000.f / 6378137.f;

                const auto lat0 = static_cast<float>(M_PIDIV2) * (2.f * tileY / static_cast<float>(terrainDim.mGridDimY) - 1.f);
                const auto lon0 = static_cast<float>(M_PI) * (2.f * tileX / static_cast<float>(terrainDim.mGridDimX) - 1.f);
                const auto lat1 = static_cast<float>(M_PIDIV2) * (2.f * (tileY + tileSize) / static_cast<float>(terrainDim.mGridDimY) - 1.f);
                const auto lon1 = static_cast<float>(M_PI) * (2.f * (tileX + tileSize) / static_cast<float>(terrainDim.mGridDimX) - 1.f);
                const auto lat2 = (lat0 + lat1) * .5f;
                const auto lon2 = (lon0 + lon1) * .5f;
                const auto h = alt * transformAndCameraData.mWorldScale.x;

                const auto sinLat0 = std::sin(lat0);
                const auto cosLat0 = std::cos(lat0);
                const auto n0 = transformAndCameraData.mWorldScale.x / std::sqrt(1.f - e2 * sinLat0 * sinLat0);
                const auto nh0 = n0 + h;

                const auto sinLat1 = std::sin(lat1);
                const auto cosLat1 = std::cos(lat1);
                const auto n1 = transformAndCameraData.mWorldScale.x / std::sqrt(1.f - e2 * sinLat1 * sinLat1);
                const auto nh1 = n1 + h;

                const auto sinLat2 = std::sin(lat2);
                const auto cosLat2 = std::cos(lat2);
                const auto n2 = transformAndCameraData.mWorldScale.x / std::sqrt(1.f - e2 * sinLat2 * sinLat2);
                const auto nh2 = n2;

                const auto center = Float3(nh2 * cosLat2 * std::sin(lon2), b2 * n2 * std::sin(lat2), -nh2 * cosLat2 * std::cos(lon2)) + transformAndCameraData.mWorldTranslation.XYZ();
                const auto radius = ((0.f < lat2 ? Float3(nh0 * cosLat0 * std::sin(lon0), (b2 * n0 + h) * std::sin(lat0), -nh0 * cosLat0 * std::cos(lon0)) + transformAndCameraData.mWorldTranslation.XYZ()
                                                 : Float3(nh1 * cosLat1 * std::sin(lon1), (b2 * n1 + h) * std::sin(lat1), -nh1 * cosLat1 * std::cos(lon1)) + transformAndCameraData.mWorldTranslation.XYZ()) - center).Length();

#if DEBUG_TILE_CULLING
                auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                PrimitiveData primData;
                {
                    PrimitiveGenerator::GenerateSphereFrame(&primData, radius, 4, 4, center - transformAndCameraData.mCameraTilePosition * LENGTH_PER_TILE_F);
                    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.0), PrimitiveDepth::SceneDepth, static_cast<UInt8>(tile.mLevel + 1U)));
                }
#endif

                BoundingSphere bSphere{ center, radius };
                node.mVisible = transformAndCameraData.mCameraFrustum.Intersects(bSphere);

                const auto vVO = transformAndCameraData.mWorldTranslation.XYZ() - transformAndCameraData.mCameraPositionAndFovy.XYZ();
                const auto vVOLength = vVO.Length();
                const auto uVO = vVOLength ? vVO / vVOLength : transformAndCameraData.mCameraForward;

                const auto vOC = center - transformAndCameraData.mWorldTranslation.XYZ();
                const auto vOCLength = vOC.Length();
                const auto uOC = vOC / vOCLength;
                const auto cosBeta = uOC.Dot(-uVO);

                const auto vVC = center - transformAndCameraData.mCameraPositionAndFovy.XYZ();
                const auto vVCLength = vVC.Length();
                const auto uVC = vVC / vVCLength;
                const auto cosAlpha = uVO.Dot(uVC);
                const auto sinBeta = std::sqrt(1.f - cosBeta * cosBeta);
                const auto dT = .5f * radius * radius / transformAndCameraData.mWorldScale.x;

                const auto dH = cosAlpha * vVCLength - sinBeta * radius + cosBeta * dT;
                const auto dR = sinBeta * (vOCLength - dT) - cosBeta * radius;
                const auto rD = vVOLength - transformAndCameraData.mWorldScale.x;
                const auto d = std::abs(dR < 0.f ? rD : std::max(rD, dH));
                const auto e = std::max(0.f, dR);

                if (data)
                {
                    data->mBound = bSphere;
                    data->mDistance = e;
                }

                lodDistance *= cosLat2 * cosBeta * std::min(1.f, d / e / std::tan(.5f * transformAndCameraData.mCameraPositionAndFovy.w));

                return d < lodDistance;
            }
            default:
            {
                Assert(false);
                break;
            }
        }

        Assert(false);
        return false;
    }

    void TerrainSystemG::CalculateLoDTransitionInfo(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid) const
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "CalculateLoDTransitionInfo");

        const auto gridSizeX = terrainDim.mGridSizeX;
        const auto gridSizeY = terrainDim.mGridSizeY;
        const auto blockSize = terrainDim.mBlockSize;

        for (const auto& tile : writer->mDrawableTileList)
        {
            auto& node = GetTileNode(grid, tile);

            Assert(!!node.mDrawable);
            node.mLodInfo = 0U;

            const auto subBlockSize = blockSize >> tile.mLevel;
            const auto subX = tile.mTileX & 1U;
            const auto subY = tile.mTileY & 1U;

            if (subY == 0U)
            {
                if (tile.mTileY == 0U)
                {
                    if (tile.mBlockY != 0U)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockY--;
                        neighbor.mBlockIndex -= gridSizeX;
                        neighbor.mTileY = subBlockSize - 1U;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize + neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= level - tile.mLevel;
                    }
                    else if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockY = gridSizeY - 1U;
                        neighbor.mBlockIndex += gridSizeX * (gridSizeY - 1U);
                        neighbor.mTileY = subBlockSize - 1U;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize + neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= level - tile.mLevel;
                    }
                }
                else
                {
                    TerrainComponentG::TileLocator neighbor{ tile };
                    neighbor.mTileY--;
                    neighbor.mTileIndex -= blockSize >> neighbor.mLevel;

                    const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                    node.mLodInfo |= level - tile.mLevel;
                }
            }
            else
            {
                if (tile.mTileY == subBlockSize - 1U)
                {
                    if (tile.mBlockY != gridSizeY - 1U)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockY++;
                        neighbor.mBlockIndex += gridSizeX;
                        neighbor.mTileY = 0;
                        neighbor.mTileIndex = neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 16;
                    }
                    else if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockY = 0;
                        neighbor.mBlockIndex -= gridSizeX * (gridSizeY - 1U);
                        neighbor.mTileY = 0;
                        neighbor.mTileIndex = neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 16;
                    }
                }
                else
                {
                    TerrainComponentG::TileLocator neighbor{ tile };
                    neighbor.mTileY++;
                    neighbor.mTileIndex += blockSize >> neighbor.mLevel;

                    const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                    node.mLodInfo |= (level - tile.mLevel) << 16;
                }
            }

            if (subX == 0U)
            {
                if (tile.mTileX == 0U)
                {
                    if (tile.mBlockX != 0U)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockX--;
                        neighbor.mBlockIndex--;
                        neighbor.mTileX = subBlockSize - 1U;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize + neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 8;
                    }
                    else if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockX = gridSizeX - 1U;
                        neighbor.mBlockIndex += gridSizeX - 1U;
                        neighbor.mTileX = subBlockSize - 1U;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize + neighbor.mTileX;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 8;
                    }
                }
                else
                {
                    TerrainComponentG::TileLocator neighbor{ tile };
                    neighbor.mTileX--;
                    neighbor.mTileIndex--;

                    const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                    node.mLodInfo |= (level - tile.mLevel) << 8;
                }
            }
            else
            {
                if (tile.mTileX == subBlockSize - 1U)
                {
                    if (tile.mBlockX != gridSizeX - 1U)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockX++;
                        neighbor.mBlockIndex++;
                        neighbor.mTileX = 0;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 24;
                    }
                    else if (terrainDim.mSurfaceType != TerrainSurfaceType::Flat)
                    {
                        TerrainComponentG::TileLocator neighbor{ tile };
                        neighbor.mBlockX = 0;
                        neighbor.mBlockIndex -= gridSizeX - 1U;
                        neighbor.mTileX = 0;
                        neighbor.mTileIndex = neighbor.mTileY * subBlockSize;

                        const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                        node.mLodInfo |= (level - tile.mLevel) << 24;
                    }
                }
                else
                {
                    TerrainComponentG::TileLocator neighbor{ tile };
                    neighbor.mTileX++;
                    neighbor.mTileIndex++;

                    const auto level = FindSmallestDrawableLevel(terrainDim, grid, neighbor);
                    node.mLodInfo |= (level - tile.mLevel) << 24;
                }
            }
        }
    }

    UInt32 TerrainSystemG::FindSmallestDrawableLevel(const TerrainDimension& terrainDim, const TerrainComponentG::Grid& grid, const TerrainComponentG::TileLocator& tile) const
    {
        if (tile.mLevel + 1U == terrainDim.mNumLevels)
        {
            return tile.mLevel;
        }

        const auto& node = GetTileNode(grid, tile);
        if (node.mDrawable)
        {
            return tile.mLevel;
        }
        else
        {
            TerrainComponentG::TileLocator parentTile{ tile };
            parentTile.mLevel++;
            parentTile.mTileX >>= 1U;
            parentTile.mTileY >>= 1U;
            parentTile.mTileIndex = parentTile.mTileY * (terrainDim.mBlockSize >> parentTile.mLevel) + parentTile.mTileX;

            return FindSmallestDrawableLevel(terrainDim, grid, parentTile);
        }
    }

    void TerrainSystemG::UpdateActiveTiles(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "UpdateActiveTiles");

        Assert((std::unordered_set<TerrainComponentG::ActiveTile, TerrainComponentG::ActiveTileHasher>(writer->mActiveTileList.begin(), writer->mActiveTileList.end()).size() == writer->mActiveTileList.size()));

        for (auto& item : writer->mActiveTileList)
        {
            auto& node = GetTileNode(grid, item.mTile);
            if (!item.mTileAssetData.IsLoaded() && item.mTileAssetData.CheckAndCompleteStreaming())
            {
                node.mResident = 1U;
                node.mStreaming = 0U;

                CompleteTileStreaming(writer, terrainDim, item.mTile, node, item.mTileAssetData);

                item.mFrameNumber = mFrameNumber;
            }

            UpdateTileUsage(node);
            node.mRequested = 0U;
            node.mDrawable = 0U;
            node.mVisible = 0U;
        }

#ifdef CROSSENGINE_EDITOR
        //generate aabb tree
        //GenerateTerrainAABBTree(writer);
#endif
    }

    void TerrainSystemG::DispatchTileStreaming(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TransformAndCameraData& transformAndCameraData)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "DispatchTileStreaming");

        auto cameraHeight = 0.f;
        {
            switch (terrainDim.mSurfaceType)
            {
                case TerrainSurfaceType::Flat:
                {
                    cameraHeight = std::max(0.f, transformAndCameraData.mCameraPositionAndFovy.y - transformAndCameraData.mWorldTranslation.y);

                    for (auto& tileRequestData : writer->mTileStreamingRequests)
                    {
                        const auto distance = (transformAndCameraData.mCameraPositionAndFovy.XYZ() - tileRequestData.mBound.GetCenter()).Length();
                        tileRequestData.mDistance = std::sqrt(std::max(0.f, distance * distance - cameraHeight * cameraHeight)) -  tileRequestData.mBound.GetRadius();
                    }
                    break;
                }
                case TerrainSurfaceType::Spherical:
                case TerrainSurfaceType::WGS84:
                {
                    const auto v0 = (transformAndCameraData.mCameraPositionAndFovy.XYZ() - transformAndCameraData.mWorldTranslation.XYZ()).Normalized();

                    //{
                    //    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                    //    PrimitiveData primData;
                    //    PrimitiveGenerator::GenerateCubeFrame(&primData, Float3(transformAndCameraData.mWorldScale.x * 1.01f, EngineGlobal::GetSettingMgr()->GetTerrainStreamingDistance(),
                    //        EngineGlobal::GetSettingMgr()->GetTerrainStreamingDistance()), Float3::Zero(), Quaternion::CreateFrom2Vectors(Float3(1.f, 0.f, 0.f), v0));
                    //    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.0), PrimitiveDepth::SceneDepth, 4U));
                    //}

                    //{
                    //    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                    //    PrimitiveData primData;
                    //    PrimitiveGenerator::GeneratePoint(&primData, v0.Normalized() * transformAndCameraData.mWorldScale.x * 1.01f);
                    //    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.0), PrimitiveDepth::SceneDepth, 4U));
                    //}

                    for (auto& tileRequestData : writer->mTileStreamingRequests)
                    {
                        const auto v1 = tileRequestData.mBound.GetCenter() - transformAndCameraData.mWorldTranslation.XYZ();
                        const auto cosTheta = v0.Dot(v1);
                        if (cosTheta < 0.f)
                        {
                            tileRequestData.mDistance = std::numeric_limits<float>::infinity();
                        }

                        //if (.9f < cosTheta)
                        //{
                        //    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                        //    PrimitiveData primData;
                        //    PrimitiveGenerator::GenerateSphereFrame(&primData, tileRequestData.mBound.GetRadius(), 6U, 6U, transformAndCameraData.mWorldTranslation.XYZ() + v1.Normalized() * transformAndCameraData.mWorldScale.x);
                        //    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(1.0, 0.0, 0.f), PrimitiveDepth::SceneDepth, 1U));
                        //}
                        //else
                        //{
                        //    auto primitiveSys = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();
                        //    PrimitiveData primData;
                        //    PrimitiveGenerator::GenerateSphereFrame(&primData, tileRequestData.mBound.GetRadius(), 6U, 6U, transformAndCameraData.mWorldTranslation.XYZ() + v1.Normalized() * transformAndCameraData.mWorldScale.x);
                        //    primitiveSys->DrawPrimitive(&primData, Float4x4::Identity(), cross::PrimitiveRenderSystemG::PrimitiveLook(cross::ColorRGBAf(0.0, 1.0, 0.f), PrimitiveDepth::SceneDepth, 1U));
                        //}
                    }
                    break;
                }
                default:
                {
                    Assert(false);
                    break;
                }
            }
        }

        std::sort(writer->mTileStreamingRequests.begin(), writer->mTileStreamingRequests.end(), [](const auto& lhs, const auto& rhs)
        {
            if (rhs.mTile.mLevel != lhs.mTile.mLevel)
            {
                return rhs.mTile.mLevel < lhs.mTile.mLevel;
            }

            return lhs.mDistance < rhs.mDistance;
        });

        std::unordered_set<TerrainComponentG::TileLocator, TerrainComponentG::TileLocatorHasher> candidates; // to-do: optimize memory usage
        candidates.reserve(mNumMaxPerFrameTileRequests * 4U);
        const auto streamingDistance = EngineGlobal::GetSettingMgr()->GetTerrainStreamingDistance() * GetTerrainScale(terrainDim, transformAndCameraData);
        for (const auto& tileRequestData : writer->mTileStreamingRequests)
        {
            if (mNumPerFrameTileRequests != mNumMaxPerFrameTileRequests)
            {
                if (!candidates.count(tileRequestData.mTile))
                {
                    if (tileRequestData.mDistance < streamingDistance)
                    {
                        candidates.emplace(tileRequestData.mTile);

                        Assert(tileRequestData.mTile.mLevel + 1U < terrainDim.mNumLevels);
                        const auto levelBlockSize = terrainDim.mBlockSize >> tileRequestData.mTile.mLevel;
                        auto tile = tileRequestData.mTile;
                        for (UInt32 y = 0U; y != 2U; y++)
                        {
                            tile.mTileY = (tileRequestData.mTile.mTileY & ~1U) + y;
                            for (UInt32 x = 0U; x != 2U; x++)
                            {
                                tile.mTileX = (tileRequestData.mTile.mTileX & ~1U) + x;
                                tile.mTileIndex = tile.mTileY * levelBlockSize + tile.mTileX;
                                if (tile != tileRequestData.mTile)
                                {
                                    candidates.emplace(tile);
                                }
                            }
                        }
                    }
                    else
                    {
                        auto neighborExists = false;
                        Assert(tileRequestData.mTile.mLevel + 1U < terrainDim.mNumLevels);
                        const auto levelBlockSize = terrainDim.mBlockSize >> tileRequestData.mTile.mLevel;
                        auto tile = tileRequestData.mTile;
                        for (UInt32 y = 0U; y != 2U; y++)
                        {
                            tile.mTileY = (tileRequestData.mTile.mTileY & ~1U) + y;
                            for (UInt32 x = 0U; x != 2U; x++)
                            {
                                tile.mTileX = (tileRequestData.mTile.mTileX & ~1U) + x;
                                tile.mTileIndex = tile.mTileY * levelBlockSize + tile.mTileX;
                                if (tile != tileRequestData.mTile)
                                {
                                    auto& node = GetTileNode(grid, tile);
                                    neighborExists = neighborExists || node.mStreaming || node.mResident;
                                }
                            }
                        }

                        if (neighborExists)
                        {
                            candidates.emplace(tileRequestData.mTile);
                        }
                    }
                }

                if (candidates.count(tileRequestData.mTile))
                {
                    auto& node = GetTileNode(grid, tileRequestData.mTile);
                    StartTileStreaming(writer->mTerrainResourcePtr, tileRequestData.mTile, node, writer->mActiveTileList);
                    node.mStreaming = 1U;
                    node.mRequested = 1U;

                    mNumPerFrameTileRequests++;
                }
            }
        }

        writer->mTileStreamingRequests.clear();
    }

    void TerrainSystemG::StartTileStreaming(TerrainResourcePtr terrainResource, const TerrainComponentG::TileLocator& tile, TerrainComponentG::TileNode& node, DataVector<TerrainComponentG::ActiveTile>& tileList)
    {
        Assert(!node.mResident && !node.mStreaming);

        tileList.push_back
        (
            TerrainComponentG::ActiveTile
            {
                tile,
                terrainResource->LoadTileAsync(tile.mBlockX, tile.mBlockY, tile.mLevel, tile.mTileX, tile.mTileY),
                0U
            }
        );

        Assert(tileList.back().mTileAssetData.IsValid());
    }

    void TerrainSystemG::CompleteTileStreaming(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, const TerrainComponentG::TileLocator& tile, TerrainComponentG::TileNode& node,
        const resource::TerrainTileAssetData& tileAssetData)
    {
        Assert(tileAssetData.IsLoaded());
        Assert(node.mResident && !node.mStreaming);

        node.mRenderProperties.SetProperty(TERRAIN_HEIGHT_MAP_NAME, tileAssetData.GetHeightmap());

        if (tileAssetData.GetNumWeightTexture())
        {
            for (UInt32 i = 0; i != tileAssetData.GetNumWeightTexture(); i++)
            {
                node.mRenderProperties.SetProperty(fmt::format("{}{}", TERRAIN_WEIGHT_TEXTURE_NAME, i), tileAssetData.GetWeightTexture(i));
            }
        }
        else
        {
            node.mRenderProperties.SetProperty(TERRAIN_ALBEDO_TEXTURE_NAME, tileAssetData.GetAlbedoTexture());
        }

        const auto offsetX = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel));
        const auto offsetY = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel));
        node.mRenderProperties.SetProperty("PatchOffsetAndScale", Float4(static_cast<float>(offsetX), 0.f, static_cast<float>(offsetY), static_cast<float>(1U << tile.mLevel)));

        {
            //generate tile collision
            const UInt32* imageData = reinterpret_cast<const UInt32*>(tileAssetData.GetHeightmap()->GetTextureData()->GetImageData(0U));

            const auto tileSize = GetTileSize(ecs::GrantReadAccess(writer));
            const auto blockSize = GetBlockSize(ecs::GrantReadAccess(writer));
            std::vector<int16_t> heightData;
            ;
            heightData.resize((tileSize + 1U) * (tileSize + 1U));
            for (UInt32 i = 0U; i != heightData.size(); i++)
            {
                heightData[i] = (int16_t)(DecodeHeight(static_cast<UInt16>(imageData[i]), terrainDim.mSurfaceType) * GetWorldScale(ecs::GrantReadAccess(writer)).y);
            }
            auto heightMap = ::cross::EngineGlobal::GetPhysicsEngine()->GetCooker()->BuildTerrainHeightMap(reinterpret_cast<UInt8*>(heightData.data()), tileSize + 1U, tileSize + 1U, sizeof(int16_t));
            node.mTerrainCollision = {heightMap, offsetX, offsetY, 1U << tile.mLevel};
        }

        auto AcquireNextSlot = [](TerrainComponentG::SlotIndexTable& table)
        {
            if (!table.HasNextSlot())
            {
                table.AddSlots(1U);
            }

            const auto index = table.AcquireNextSlot();
            Assert(index < table.mNumSlots);

            return index;
        };

        if (tileAssetData.IsDefaultResource())
        {
            if (writer->mDefaultResourceSlotCount)
            {
                node.mSlotIndex = writer->mDefaultResourceSlotIndex;
            }
            else
            {
                node.mSlotIndex = AcquireNextSlot(mGlobalSlotIndexTable) | (AcquireNextSlot(writer->mLocalSlotIndexTable) << 16U);
                writer->mDefaultResourceSlotIndex = node.mSlotIndex;
            }
            node.mDefaultResource = 1U;
            writer->mDefaultResourceSlotCount++;
        }
        else
        {
            node.mSlotIndex = AcquireNextSlot(mGlobalSlotIndexTable) | (AcquireNextSlot(writer->mLocalSlotIndexTable) << 16U);
            node.mDefaultResource = 0U;
        }

        writer->mAddedTileList.push_back(tile);
    }

    void TerrainSystemG::UpdateActiveTileList(const TerrainComponentWriter& writer, TerrainComponentG::Grid& grid)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "UpdateActiveTileList");

        std::sort(writer->mActiveTileList.begin(), writer->mActiveTileList.end(), [this, &grid](const auto& lhs, const auto& rhs)
        {
            return CompareTileUsageLess(GetTileNode(grid, lhs.mTile), GetTileNode(grid, rhs.mTile));
        });

        const auto numActiveTiles = static_cast<UInt32>(std::count_if(writer->mActiveTileList.begin(), writer->mActiveTileList.end(), [this, &grid](const auto& item)
        {
            return IsTileRecentlyUsed(GetTileNode(grid, item.mTile));
        }));

        const auto alpha = 2.f / (mNumATBSamples + 1U);
        mATBufferSize = (1.f - alpha) * mATBufferSize + alpha * numActiveTiles;

        const auto bufferCapacity = static_cast<UInt32>(std::ceil(mATBufferSize * mATBufferCapacityScale));
        if (bufferCapacity < writer->mActiveTileList.size())
        {
            auto resetCount = 0U;
            for (UInt32 i = bufferCapacity; i != writer->mActiveTileList.size(); i++)
            {
                auto& item = writer->mActiveTileList[i];
                auto& node = GetTileNode(grid, item.mTile);
                if (!IsTileRecentlyUsed(node))
                {
#ifdef CROSSENGINE_EDITOR
                    if (!node.mResidentLock)
#endif
                    {
                        resetCount++;

                        ResetTileNode(writer, item.mTile, node);

                        // if (item.mTileAssetData.IsLoaded())
                        //{
                        //     item.mTileAssetData.CancelStreaming();
                        // }

                        writer->mPendingReleaseList.push_back(std::move(item.mTileAssetData));
                    }
                }
            }

            writer->mActiveTileList.resize(writer->mActiveTileList.size() - resetCount);
        }

#if TERRAIN_USE_INSTANCING
        if (!UseWeightBlend(ecs::GrantReadAccess(writer)))
        {
            const auto appStartupType = EngineGlobal::GetSettingMgr()->GetAppStartUpType();
            for (auto& item : writer->mActiveTileList)
            {
                auto& node = GetTileNode(grid, item.mTile);
#ifdef CROSSENGINE_EDITOR
                if (!node.mResidentLock)
#endif
                {
                     if (appStartupType != cross::AppStartUpType::AppStartUpTypeCrossEditor)
                     {
                         if (node.mResident && item.mFrameNumber + 2U == mFrameNumber)
                         {
                             //node.mRenderProperties.SetProperty(TERRAIN_HEIGHT_MAP_NAME, mBlackTexture);
                             node.mRenderProperties.SetProperty(TERRAIN_ALBEDO_TEXTURE_NAME, mDefaultBaseColorTexture);
                             item.mTileAssetData.ReleaseResources();
                         }
                     }
                }
            }
        }
#endif

        writer->mPendingReleaseList.erase(std::remove_if(writer->mPendingReleaseList.begin(), writer->mPendingReleaseList.end(), [](auto& tileAssetData)
        {
            return tileAssetData.ReleaseResources();
        }), writer->mPendingReleaseList.end());
    }

    void TerrainSystemG::UpdateRenderState(TerrainComponentHandle terrainComponent, const TerrainDimension& terrainDim, const TerrainComponentG::Grid& grid, const TransformAndCameraData& transformAndCameraData)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "UpdateRenderState");

        if (terrainComponent.Read()->mTransformUpdated)
        {
            if (auto material = GetMaterial(terrainComponent.Read()))
            {
                material->SetFloat4("WorldTranslation", transformAndCameraData.mWorldTranslation.data());
                material->SetFloat4("WorldScale", transformAndCameraData.mWorldScale.data());
            }

            terrainComponent.Write()->mTransformUpdated = false;
        }
        auto pool = &mPool;
        TransferVector<TerrainComponentR::TileNode> tileNodesDrawable{pool};
        tileNodesDrawable.reserve(terrainComponent.Read()->mDrawableTileList.size());
        for (const auto& tile : terrainComponent.Read()->mDrawableTileList)
        {
            const auto& node = GetTileNode(grid, tile);
            Assert(!!node.mDrawable);

            const auto offsetX = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel));
            const auto offsetZ = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel));
            tileNodesDrawable.push_back(TerrainComponentR::TileNode{ node.mRenderProperties, node.mSlotIndex, offsetX, offsetZ, tile.mLevel, node.mLodInfo, node.mVisible });
        }

        TransferVector<TerrainComponentR::TileNode> tileNodesAlwaysResident{pool};
        tileNodesAlwaysResident.reserve(terrainComponent.Read()->mAlwaysResidentTileList.size());
        for (const auto& activeTile : terrainComponent.Read()->mAlwaysResidentTileList)
        {
            const auto& tile = activeTile.mTile;
            const auto& node = GetTileNode(grid, tile);
            Assert(!!node.mDrawable);

            const auto offsetX = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel));
            const auto offsetZ = terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel));
            tileNodesAlwaysResident.push_back(TerrainComponentR::TileNode{ node.mRenderProperties, node.mSlotIndex, offsetX, offsetZ, tile.mLevel, node.mLodInfo, node.mVisible });
        }

        TransferVector<TerrainComponentR::SlotUpdate> addedTileListUpdate{pool};
        addedTileListUpdate.reserve(terrainComponent.Read()->mAddedTileList.size());
        for (const auto& tile : terrainComponent.Read()->mAddedTileList)
        {
            const auto& node = GetTileNode(grid, tile);
            if (node.mResident)
            {
                addedTileListUpdate.push_back(TerrainComponentR::SlotUpdate{ node.mRenderProperties, node.mSlotIndex });
            }
        }

        TransferVector<TerrainComponentR::SlotUpdate> removedTileListUpdate{pool};
        removedTileListUpdate.reserve(terrainComponent.Read()->mRemovedTileList.size());
        for (const auto& tile : terrainComponent.Read()->mRemovedTileList)
        {
            const auto& node = GetTileNode(grid, tile);
            removedTileListUpdate.push_back(TerrainComponentR::SlotUpdate{ TerrainRenderProperties{}, node.mSlotIndex });
        }

        DispatchRenderingCommandWithToken([this,
            entity = terrainComponent.GetEntityID(),
            enabled = GetTerrainEnable(terrainComponent.Read()),
            surfaceType = terrainDim.mSurfaceType,
            tileSize = terrainDim.mTileSize,
            textureSize = GetTextureSize(terrainComponent.Read()),
            numLevels = terrainDim.mNumLevels,
            numBlendLayers = GetNumBlendLayers(terrainComponent.Read()),
            numGlobalAllocatedSlots = mGlobalSlotIndexTable.mNumSlots,
            numLocalAllocatedSlots = terrainComponent.Read()->mLocalSlotIndexTable.mNumSlots,
            material = TYPE_CAST(MaterialR*, terrainComponent.Read()->mMaterial->GetRenderMaterial()),
            tileNodesDrawableCopy = std::move(tileNodesDrawable),
            tileNodesAlwaysResidentCopy = std::move(tileNodesAlwaysResident),
            addeTileListCopy = std::move(addedTileListUpdate),
            removedTileListCopy = std::move(removedTileListUpdate)]() mutable
        {
            mTerrainSystemR->SetTerrainData(entity, enabled, surfaceType, tileSize, textureSize, numLevels, numBlendLayers, numGlobalAllocatedSlots, numLocalAllocatedSlots, material,
                std::move(tileNodesDrawableCopy), std::move(tileNodesAlwaysResidentCopy), std::move(addeTileListCopy), std::move(removedTileListCopy));
        });

        TransferVector<TerrainComponentR::TileRange> updateList{pool};
        for (auto& pair : terrainComponent.Write()->mCustomTerrains)
        {
            auto& customTerrain = pair.second;

            for (const auto& tile : terrainComponent.Read()->mRemovedTileList)
            {
                customTerrain.mModifiedTileList.erase(tile);
            }

            for (const auto& tileRange : customTerrain.mTileRangeList)
            {
                if (!customTerrain.mModifiedTileList.count(tileRange.mTile))
                {
                    auto& node = GetTileNode(grid, tileRange.mTile);
                    if (node.mResident)
                    {
                        updateList.push_back(TerrainComponentR::TileRange{ customTerrain.mHandle, node.mSlotIndex, tileRange.mTile.mLevel, tileRange.mRangeX, tileRange.mRangeY, tileRange.mDimX, tileRange.mDimY });
                        customTerrain.mModifiedTileList.emplace(tileRange.mTile);
                    }                    
                }
            }
        }

        if (!terrainComponent.Read()->mCustomTerrains.empty())
        {
            DispatchRenderingCommandWithToken([this, entity = terrainComponent.GetEntityID(), updateListCopy = std::move(updateList)]() mutable
            {
                mTerrainSystemR->SetCustomTerrainTileRangeUpdates(entity, std::move(updateListCopy));
            });
        }

        terrainComponent.Write()->mAddedTileList.clear();
        terrainComponent.Write()->mRemovedTileList.clear();
        terrainComponent.Write()->mDrawableTileList.clear();
    }

    void TerrainSystemG::UpdateTileCollision(const TerrainComponentWriter& writer, TerrainComponentG::Grid& grid)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "UpdateTileCollision");

        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        auto phyComp = mGameWorld->GetComponent<PhysicsComponentG>(writer.GetEntityID());
        if (phySys && phyComp)
        {
            // Add collision for new drawn tile
            for (const auto& tile : writer->mDrawableTileList)
            {
                
                if (writer->mCollisionShapes.find(tile) == writer->mCollisionShapes.end())
                {
                    const auto& node = GetTileNode(grid, tile);
                    if (node.mTerrainCollision.heightMap)
                    {
                        if (PhysicsShape * shape = phySys->AddTerrainCollision(phyComp.Write(), node.mTerrainCollision))
                            writer->mCollisionShapes.try_emplace(tile, std::make_pair(shape,true));
                    }
                }
                else
                {
                    writer->mCollisionShapes.at(tile).second = true;
                }
               
            }
            // Remove collision for tile not drawn
            for (auto it = writer->mCollisionShapes.begin();it!=writer->mCollisionShapes.end();)
			{
				if (!it->second.second)
				{
					auto shape = it->second.first;
                    phySys->RemoveShape(phyComp.Write(), shape);
					it = writer->mCollisionShapes.erase(it);
				}
				else
				{
                    it->second.second = false;
					++it;
				}
			}
        }
    }

    void TerrainSystemG::LoadAndUpdateLayerTextures(const TerrainComponentWriter& writer)
    {
        writer->mNormalTextureNameList.resize(writer->mBaseColorTextureNameList.size());
        writer->mHMRATextureNameList.resize(writer->mBaseColorTextureNameList.size());

        {
            UInt32 i = 0U;
            for (; i != GetNumBlendLayers(ecs::GrantReadAccess(writer)); i++)
            {
                const auto baseColorTexture = TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(writer->mBaseColorTextureNameList[i]));
                const auto normalTexture = writer->mNormalTextureNameList[i].empty() ? mDefaultNormalTexture : TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(writer->mNormalTextureNameList[i]));
                const auto hmraTexture = writer->mHMRATextureNameList[i].empty() ? mDefaultHMRATexture : TypeCast<resource::Texture>(gAssetStreamingManager->LoadSynchronously(writer->mHMRATextureNameList[i]));

                Assert(baseColorTexture && normalTexture && hmraTexture);
                writer->mMaterial->SetTexture(fmt::format("BaseColorTexture{}", i), baseColorTexture);
                writer->mMaterial->SetTexture(fmt::format("NormalTexture{}", i), normalTexture);
                writer->mMaterial->SetTexture(fmt::format("HMRATexture{}", i), hmraTexture);
            }

            for (; i != NumMaxTerrainBlendLayers; i++)
            {
                Assert(mDefaultBaseColorTexture && mDefaultNormalTexture && mDefaultHMRATexture);
                writer->mMaterial->SetTexture(fmt::format("BaseColorTexture{}", i), mDefaultBaseColorTexture);
                writer->mMaterial->SetTexture(fmt::format("NormalTexture{}", i), mDefaultNormalTexture);
                writer->mMaterial->SetTexture(fmt::format("HMRATexture{}", i), mDefaultHMRATexture);
            }
        }

        const auto numBlendLayers = GetNumBlendLayers(ecs::GrantReadAccess(writer));
        const auto numBlendMasks = numBlendLayers / NumLayersPerWeightTexture;
        const auto blendMaskLayerCount = numBlendLayers % NumLayersPerWeightTexture;
        for (UInt32 i = 0; i != (NumMaxTerrainBlendLayers + NumLayersPerWeightTexture - 1U) / NumLayersPerWeightTexture; i++)
        {
            const auto count = i < numBlendMasks ? NumLayersPerWeightTexture : (i == numBlendMasks ? blendMaskLayerCount : 0U);
            const auto componentMask = Float4(0U < count, 1U < count, 2U < count, 3U < count);
            writer->mMaterial->SetFloat4(fmt::format("BlendLayerMask{}", i), componentMask.data());
        }
    }

    void TerrainSystemG::SetTerrainEntityAlwaysVisible(ecs::EntityID entity) 
    {
        auto renderPropertySystemG = mGameWorld->GetGameSystem<RenderPropertySystemG>();

        if (mGameWorld->HasComponent<RenderPropertyComponentG>(entity))
        {
            auto renderPropertyComponent = mGameWorld->GetComponent<RenderPropertyComponentG>(entity);
            renderPropertySystemG->SetCullingProperty(renderPropertyComponent.Write(), CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
        }
    }

    void TerrainSystemG::GetTileRangeList(const TerrainDimension& terrainDim, UInt2 rangeX, UInt2 rangeY, DataVector<TerrainComponentG::TileRange>& list, UInt32 levelOffset)
    {
        rangeX.x >>= levelOffset;
        rangeX.y >>= levelOffset;
        rangeY.x >>= levelOffset;
        rangeY.y >>= levelOffset;

        for (UInt32 level = levelOffset; level != terrainDim.mNumLevels; level++)
        { 
            const auto levelBlockSize = terrainDim.mBlockSize >> level;

            if ((rangeX.x & (terrainDim.mTileSize - 1U)) == 0U)
            {
                for (UInt32 levelRangeY = rangeY.x; levelRangeY < rangeY.y;)
                {
                    const auto baseX = rangeX.x < terrainDim.mTileSize ? terrainDim.mGridDimX / terrainDim.mTileSize : rangeX.x / terrainDim.mTileSize - 1U;
                    const auto baseY = levelRangeY / terrainDim.mTileSize;
                    const auto offsetY = levelRangeY % terrainDim.mTileSize;

                    TerrainComponentG::TileRange tileRange{};
                    tileRange.mTile.mBlockX = baseX / levelBlockSize;
                    tileRange.mTile.mBlockY = baseY / levelBlockSize;
                    tileRange.mTile.mBlockIndex = tileRange.mTile.mBlockY * terrainDim.mGridSizeX + tileRange.mTile.mBlockX;
                    tileRange.mTile.mLevel = level;
                    tileRange.mTile.mTileX = baseX % levelBlockSize;
                    tileRange.mTile.mTileY = baseY % levelBlockSize;
                    tileRange.mTile.mTileIndex = tileRange.mTile.mTileY * levelBlockSize + tileRange.mTile.mTileX;
                    tileRange.mRangeX = UInt2(rangeX.x, rangeX.x);
                    tileRange.mRangeY = UInt2(levelRangeY, std::min(rangeY.y, (levelRangeY & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize));
                    tileRange.mDimX = UInt2(terrainDim.mTileSize, terrainDim.mTileSize);
                    tileRange.mDimY = UInt2(offsetY, offsetY + tileRange.mRangeY.y - tileRange.mRangeY.x);
                    list.push_back(tileRange);

                    levelRangeY = tileRange.mRangeY.y;
                }
            }

            if ((rangeY.x & (terrainDim.mTileSize - 1U)) == 0U)
            {
                for (UInt32 levelRangeX = rangeX.x; levelRangeX < rangeX.y;)
                {
                    const auto baseX = levelRangeX / terrainDim.mTileSize;
                    const auto baseY = rangeY.x < terrainDim.mTileSize ? terrainDim.mGridDimY / terrainDim.mTileSize : rangeY.x / terrainDim.mTileSize - 1U;
                    const auto offsetX = levelRangeX % terrainDim.mTileSize;

                    TerrainComponentG::TileRange tileRange{};
                    tileRange.mTile.mBlockX = baseX / levelBlockSize;
                    tileRange.mTile.mBlockY = baseY / levelBlockSize;
                    tileRange.mTile.mBlockIndex = tileRange.mTile.mBlockY * terrainDim.mGridSizeX + tileRange.mTile.mBlockX;
                    tileRange.mTile.mLevel = level;
                    tileRange.mTile.mTileX = baseX % levelBlockSize;
                    tileRange.mTile.mTileY = baseY % levelBlockSize;
                    tileRange.mTile.mTileIndex = tileRange.mTile.mTileY * levelBlockSize + tileRange.mTile.mTileX;
                    tileRange.mRangeX = UInt2(levelRangeX, std::min(rangeX.y, (levelRangeX & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize));
                    tileRange.mRangeY = UInt2(rangeY.x, rangeY.x);
                    tileRange.mDimX = UInt2(offsetX, offsetX + tileRange.mRangeX.y - tileRange.mRangeX.x);
                    tileRange.mDimY = UInt2(terrainDim.mTileSize, terrainDim.mTileSize);
                    list.push_back(tileRange);

                    levelRangeX = tileRange.mRangeX.y;
                }
            }

            for (UInt32 levelRangeY = rangeY.x; levelRangeY < rangeY.y;)
            {
                const auto baseY = levelRangeY / terrainDim.mTileSize;

                TerrainComponentG::TileRange tileRange{};
                tileRange.mTile.mBlockY = baseY / levelBlockSize;
                tileRange.mTile.mLevel = level;
                tileRange.mTile.mTileY = baseY % levelBlockSize;
                tileRange.mRangeY.x = levelRangeY;
                tileRange.mRangeY.y = std::min(rangeY.y, (levelRangeY & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize);
                tileRange.mDimY.x = levelRangeY % terrainDim.mTileSize;
                tileRange.mDimY.y = tileRange.mDimY.x + tileRange.mRangeY.y - tileRange.mRangeY.x;

                for (UInt32 levelRangeX = rangeX.x; levelRangeX < rangeX.y;)
                {
                    const auto baseX = levelRangeX / terrainDim.mTileSize;
                    tileRange.mTile.mBlockX = baseX / levelBlockSize;
                    tileRange.mTile.mBlockIndex = tileRange.mTile.mBlockY * terrainDim.mGridSizeX + tileRange.mTile.mBlockX;
                    tileRange.mTile.mTileX = baseX % levelBlockSize;
                    tileRange.mTile.mTileIndex = tileRange.mTile.mTileY * levelBlockSize + tileRange.mTile.mTileX;
                    tileRange.mRangeX.x = levelRangeX;
                    tileRange.mRangeX.y = std::min(rangeX.y, (levelRangeX & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize);
                    tileRange.mDimX.x = levelRangeX % terrainDim.mTileSize;
                    tileRange.mDimX.y = tileRange.mDimX.x + tileRange.mRangeX.y - tileRange.mRangeX.x;
                    list.push_back(tileRange);
            
                    levelRangeX = tileRange.mRangeX.y;
                }

                levelRangeY = tileRange.mRangeY.y;
            }

            if ((rangeX.y & (terrainDim.mTileSize - 1U)) == 0U)
            {
                for (UInt32 levelRangeY = rangeY.x; levelRangeY < rangeY.y;)
                {
                    const auto baseX = rangeX.y == terrainDim.mGridDimX ? 0U : rangeX.y / terrainDim.mTileSize;
                    const auto baseY = levelRangeY / terrainDim.mTileSize;
                    const auto offsetY = levelRangeY % terrainDim.mTileSize;

                    TerrainComponentG::TileRange tileRange{};
                    tileRange.mTile.mBlockX = baseX / levelBlockSize;
                    tileRange.mTile.mBlockY = baseY / levelBlockSize;
                    tileRange.mTile.mBlockIndex = tileRange.mTile.mBlockY * terrainDim.mGridSizeX + tileRange.mTile.mBlockX;
                    tileRange.mTile.mLevel = level;
                    tileRange.mTile.mTileX = baseX % levelBlockSize;
                    tileRange.mTile.mTileY = baseY % levelBlockSize;
                    tileRange.mTile.mTileIndex = tileRange.mTile.mTileY * levelBlockSize + tileRange.mTile.mTileX;
                    tileRange.mRangeX = UInt2(rangeX.y, rangeX.y);
                    tileRange.mRangeY = UInt2(levelRangeY, std::min(rangeY.y, (levelRangeY & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize));
                    tileRange.mDimX = UInt2(0U, 0U);
                    tileRange.mDimY = UInt2(offsetY, offsetY + tileRange.mRangeY.y - tileRange.mRangeY.x);
                    list.push_back(tileRange);

                    levelRangeY = tileRange.mRangeY.y;
                }
            }

            if ((rangeY.y & (terrainDim.mTileSize - 1U)) == 0U)
            {
                for (UInt32 levelRangeX = rangeX.x; levelRangeX < rangeX.y;)
                {
                    const auto baseX = levelRangeX / terrainDim.mTileSize;
                    const auto baseY = rangeY.y == terrainDim.mGridDimY ? 0U : rangeY.y / terrainDim.mTileSize;
                    const auto offsetX = levelRangeX % terrainDim.mTileSize;

                    TerrainComponentG::TileRange tileRange{};
                    tileRange.mTile.mBlockX = baseX / levelBlockSize;
                    tileRange.mTile.mBlockY = baseY / levelBlockSize;
                    tileRange.mTile.mBlockIndex = tileRange.mTile.mBlockY * terrainDim.mGridSizeX + tileRange.mTile.mBlockX;
                    tileRange.mTile.mLevel = level;
                    tileRange.mTile.mTileX = baseX % levelBlockSize;
                    tileRange.mTile.mTileY = baseY % levelBlockSize;
                    tileRange.mTile.mTileIndex = tileRange.mTile.mTileY * levelBlockSize + tileRange.mTile.mTileX;
                    tileRange.mRangeX = UInt2(levelRangeX, std::min(rangeX.y, (levelRangeX & ~(terrainDim.mTileSize - 1U)) + terrainDim.mTileSize));
                    tileRange.mRangeY = UInt2(rangeY.y, rangeY.y);
                    tileRange.mDimX = UInt2(offsetX, offsetX + tileRange.mRangeX.y - tileRange.mRangeX.x);
                    tileRange.mDimY = UInt2(0U, 0U);
                    list.push_back(tileRange);

                    levelRangeX = tileRange.mRangeX.y;
                }
            }

            rangeX.x >>= 1U;
            rangeX.y >>= 1U;
            rangeY.x >>= 1U;
            rangeY.y >>= 1U;
        }
    }

    bool TerrainSystemG::LoadTile(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TerrainComponentG::TileLocator& tile)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "LoadTile");

        Assert(writer->mTileStreamingRequests.empty());

        auto& node = GetTileNode(grid, tile);
        if (node.mTouched && node.mInvalid)
        {
            return false;
        }

        if (!node.mResident)
        {
            if (!node.mTouched)
            {
                node.mTouched = 1U;
                node.mInvalid = 1U - writer->mTerrainResourcePtr->DoesTileExists(tile.mBlockX, tile.mBlockY, tile.mLevel, tile.mTileX, tile.mTileY);
                if (node.mInvalid)
                {
                    return false;
                }
            }

            Assert(node.mTouched && !node.mInvalid);
            auto tileAssetData = writer->mTerrainResourcePtr->LoadTileAsync(tile.mBlockX, tile.mBlockY, tile.mLevel, tile.mTileX, tile.mTileY);
            tileAssetData.WaitForStreaming();

            node.mResident = 1U;
            node.mStreaming = 0U;

            CompleteTileStreaming(writer, terrainDim, tile, node, tileAssetData);
        }

        return true;
    }

    float TerrainSystemG::GetAltitudeByGeoCoord(const TerrainComponentReader& reader, double latitude, double longtitude)
    {
        double geoX = longtitude / 360.0 + 0.5;
        double geoY = latitude / 180.0 + 0.5;

        UInt32 blockX = static_cast<UInt32>(geoX * reader->mGridSizeX);
        UInt32 blockY = static_cast<UInt32>(geoY * reader->mGridSizeY);

        double geoX_frac = geoX * reader->mGridSizeX - blockX;
        double geoY_frac = geoY * reader->mGridSizeY - blockY;

        UInt32 retLevel = static_cast<UInt32>(-1);
        float retHeight = DecodeHeight(0U, TerrainSurfaceType::WGS84);

        //for (const auto& tile : reader->mAlwaysResidentTileList)
        for (const auto& tile : reader->mActiveTileList)
        {
            const auto& tileIndex = tile.mTile;
            if (tileIndex.mBlockX == blockX && tileIndex.mBlockY == blockY)
            {
                UInt32 blockSize = reader->mBlockSize;
                for (UInt32 i = 0; i < tileIndex.mLevel; i++)
                {
                    blockSize /= 2;
                }

                UInt32 tileX = static_cast<UInt32>(geoX_frac * blockSize);
                UInt32 tileY = static_cast<UInt32>(geoY_frac * blockSize);
                if (tileIndex.mTileX == tileX && tileIndex.mTileY == tileY)
                {
                    const auto& node = GetTileNode(GetGrid(reader), tile.mTile);
                    const auto& renderProperties = node.mRenderProperties;
                    if (renderProperties.HasProperty(TERRAIN_HEIGHT_MAP_NAME))
                    {
                        double pixelX = (geoX_frac * blockSize - tileX) * (reader->mTileSize + 1);
                        double pixelY = (geoY_frac * blockSize - tileY) * (reader->mTileSize + 1);

                        UInt32 pX0 = static_cast<UInt32>(pixelX), pX1 = pX0 + 1;
                        UInt32 pY0 = static_cast<UInt32>(pixelY), pY1 = pY0 + 1;

                        TexturePtr heightmap = renderProperties.GetProperty<TexturePtr>(TERRAIN_HEIGHT_MAP_NAME);
                        UInt32* heightData = reinterpret_cast<UInt32*>(heightmap->GetTextureData()->GetImageData(0U));
                        UInt16 hData00 = static_cast<UInt16>(heightData[pY0 * (reader->mTileSize + 1) + pX0]);
                        UInt16 hData01 = static_cast<UInt16>(heightData[pY0 * (reader->mTileSize + 1) + pX1]);
                        UInt16 hData10 = static_cast<UInt16>(heightData[pY1 * (reader->mTileSize + 1) + pX0]);
                        UInt16 hData11 = static_cast<UInt16>(heightData[pY1 * (reader->mTileSize + 1) + pX1]);

                        if (IsHole(hData00))
                        {
                            hData00 = IsHole(hData01) ? hData10 : hData10;
                            hData01 = IsHole(hData01) ? hData11 : hData01;
                            hData10 = IsHole(hData10) ? hData11 : hData10;
                        }
                        if (IsHole(hData11))
                        {
                            hData11 = IsHole(hData01) ? hData10 : hData10;
                            hData01 = IsHole(hData01) ? hData00 : hData01;
                            hData10 = IsHole(hData10) ? hData00 : hData10;
                        }

                        float height = MathUtils::Lerp(MathUtils::Lerp(DecodeHeight(hData00, TerrainSurfaceType::WGS84), DecodeHeight(hData01, TerrainSurfaceType::WGS84), static_cast<float>(pixelX - pX0)),
                                                       MathUtils::Lerp(DecodeHeight(hData10, TerrainSurfaceType::WGS84), DecodeHeight(hData11, TerrainSurfaceType::WGS84), static_cast<float>(pixelX - pX0)),
                                                       static_cast<float>(pixelY - pY0));

                        if (tileIndex.mLevel < retLevel)
                        {
                            retLevel = tileIndex.mLevel;
                            //retHeight = DecodeHeight(hData00, TerrainSurfaceType::WGS84);
                            retHeight = height;
                        }
                    }
                }
            }
        }
        return retHeight;
    }
#ifdef CROSSENGINE_EDITOR
    void TerrainSystemG::CalculatePositions(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim,
        const Float3& worldTranslation, const Float3& worldScale, const UInt32* heightData, Float3* positionData)
    {
        const auto gridDimRcp = Float2(1.f / static_cast<float>(terrainDim.mGridDimX), 1.f / static_cast<float>(terrainDim.mGridDimY));
        const auto offsetX = static_cast<float>(terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel)));
        const auto offsetY = static_cast<float>(terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel)));
        const auto patchOffsetAndScale = Float4(offsetX, 0.f, offsetY, static_cast<float>(1U << tile.mLevel));

        threading::ParallelFor(terrainDim.mTileSize + 1U, [this, &positionData, &heightData, &gridDimRcp, &terrainDim, &patchOffsetAndScale, &worldTranslation, &worldScale](UInt32 h)
        {
            switch (terrainDim.mSurfaceType)
            {
                case TerrainSurfaceType::Flat:
                {
                    for (UInt32 w = 0U; w != terrainDim.mTileSize + 1U; w++)
                    {
                        const auto i = h * (terrainDim.mTileSize + 1U) + w;
                        const auto patchCoords = Float3(static_cast<float>(w), 0.f, static_cast<float>(h));
                        const auto tileH = DecodeHeight(static_cast<UInt16>(heightData[i]), terrainDim.mSurfaceType);
                        positionData[i] = CalculateFlatPosition(patchOffsetAndScale, worldTranslation, worldScale, patchCoords, tileH);
                    }
                    break;
                }
                case TerrainSurfaceType::Spherical:
                {
                    for (UInt32 w = 0U; w != terrainDim.mTileSize + 1U; w++)
                    {
                        const auto i = h * (terrainDim.mTileSize + 1U) + w;
                        const auto patchCoords = Float3(static_cast<float>(w), 0.f, static_cast<float>(h));
                        const auto tileH = DecodeHeight(static_cast<UInt16>(heightData[i]), terrainDim.mSurfaceType);
                        positionData[i] = CalculateSphericalPosition(gridDimRcp, patchOffsetAndScale, worldTranslation, worldScale, patchCoords, tileH);
                    }
                    break;
                }
                case TerrainSurfaceType::WGS84:
                {
                    for (UInt32 w = 0U; w != terrainDim.mTileSize + 1U; w++)
                    {
                        const auto i = h * (terrainDim.mTileSize + 1U) + w;
                        const auto patchCoords = Float3(static_cast<float>(w), 0.f, static_cast<float>(h));
                        const auto tileH = DecodeHeight(static_cast<UInt16>(heightData[i]), terrainDim.mSurfaceType);
                        positionData[i] = CalculateWGS84Position(gridDimRcp, patchOffsetAndScale, worldTranslation, worldScale, patchCoords, tileH);
                    }
                    break;
                }
                default:
                {
                    Assert(false);
                    break;
                }
            }
        });
    }

    void TerrainSystemG::CalculateTilePositions(const TerrainComponentG::TileLocator& tile, const TerrainDimension& terrainDim, 
        const Float3& worldTranslation, const Float3& worldScale, const UInt32* heightData, Float3* positionData) 
    {
        const auto gridDimRcp = Float2(1.f / static_cast<float>(terrainDim.mGridDimX), 1.f / static_cast<float>(terrainDim.mGridDimY));
        const auto offsetX = static_cast<float>(terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockX + tile.mTileX * (1U << tile.mLevel)));
        const auto offsetY = static_cast<float>(terrainDim.mTileSize * (terrainDim.mBlockSize * tile.mBlockY + tile.mTileY * (1U << tile.mLevel)));
        const auto patchOffsetAndScale = Float4(offsetX, 0.f, offsetY, static_cast<float>(1U << tile.mLevel));
        const auto patchOffsetAndScale1 = Float4(393216.0f, 0.f, 163840.0f, 128.00);

        UInt32 index = 0;
        auto Calculate = 
        [this, &positionData, &heightData, &gridDimRcp, &terrainDim, &patchOffsetAndScale, &worldTranslation, &worldScale, &index](UInt32 h, UInt32 height)
        {
            switch (terrainDim.mSurfaceType)
            {
                case TerrainSurfaceType::Flat:
                {
                    break;
                }
                case TerrainSurfaceType::Spherical:
                {
                    break;
                }
                case TerrainSurfaceType::WGS84:
                {
                    for (UInt32 w = 0U; w < terrainDim.mTileSize + 1U; w += terrainDim.mTileSize)
                    {
                        const auto patchCoords = Float3(static_cast<float>(w), 0.f, static_cast<float>(h));
                        //const auto tileH = DecodeHeight(static_cast<UInt16>(heightData[height]), terrainDim.mSurfaceType);
                        const auto tileH = heightData[height];
                        //std::cout << "tileH : " << tileH << std::endl;
                        positionData[index] = CalculateWGS84Position(gridDimRcp, patchOffsetAndScale, worldTranslation, worldScale, patchCoords, static_cast<float>(tileH));
                        //positionData[index].y *= 100.0f;
                        //positionData[index].z *= 100.0f;
                        ++index;
                    }
                    break;
                }
                default:
                {
                    Assert(false);
                    break;
                }
            }
        };

        for (UInt32 height = 0; height < 2; ++height) 
        {
            Calculate(0U, height);
            Calculate(terrainDim.mTileSize, height);
        }
    }

    bool TerrainSystemG::LoadTileWithResidentLock(const TerrainComponentWriter& writer, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TerrainComponentG::TileLocator& tile)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "LoadTileWithResidentLock");

        if (LoadTile(writer, terrainDim, grid, tile))
        {
            auto& node = GetTileNode(grid, tile);
            node.mResidentLock = 1U;
            return true;
        }

        return false;
    }

    void TerrainSystemG::UpdateDirtyTileList(const TerrainComponentReader& reader, const TerrainDimension& terrainDim, TerrainComponentG::Grid& grid, const TransformAndCameraData& transformAndCameraData)
    {
        SCOPED_CPU_TIMING(GroupTerrainSystem, "UpdateDirtyTileList");

        static auto CalcNormal = [](const Float3& ct, const Float3& cb, const Float3& cl, const Float3& cr) -> Float3
        {
            const auto v0 = ct.Cross(cl);
            const auto v1 = cr.Cross(ct);
            const auto v2 = cb.Cross(cr);
            const auto v3 = cl.Cross(cb);

            const auto w0 = v0.Length();
            const auto w1 = v1.Length();
            const auto w2 = v2.Length();
            const auto w3 = v3.Length();

            const auto wt = w0 + w1 + w2 + w3;
            const auto n = (w0 / wt * v0 + w1 / wt * v1 + w2 / wt * v2 + w3 / wt * v3).Normalized();
            return n;
        };

        std::vector<Float3> positionData;
        const UInt32 height = terrainDim.mTileSize + 1, width = height;
        auto UpdateNormalEdge = [=, &positionData](UInt32 h, UInt32 w, std::vector<UInt32>& heightData)
        {
            const auto c = h * width + w;
            const auto tc = h > 0 ? positionData[c - width] - positionData[c] : positionData[c] - positionData[c + width];
            const auto bc = h < height - 1 ? positionData[c + width] - positionData[c] : positionData[c] - positionData[c - width];
            const auto lc = w > 0 ? positionData[c - 1] - positionData[c] : positionData[c] - positionData[c + 1];
            const auto rc = w < width - 1 ? positionData[c + 1] - positionData[c] : positionData[c] - positionData[c - 1];
            const auto n = CalcNormal(tc, bc, lc, rc);
            heightData[c] = static_cast<UInt32>(EncodeNormal(n) << 16U) | (heightData[c] & 0x0000FFFF);
        };

        for (const auto& tile : mDirtyTileList)
        {
            auto& node = GetTileNode(grid, tile);
            Assert(node.mResident && (node.mHeightmapDirty || node.mWeightTextureDirty));

            if (node.mHeightmapDirty)
            {
                Assert(!node.mHeightmap.empty());

                positionData.resize((terrainDim.mTileSize + 1U) * (terrainDim.mTileSize + 1U));

                auto& heightData = node.mEncodeHeightmap;
                threading::ParallelFor(terrainDim.mTileSize + 1U, [this, &terrainDim, &heightData, &node](UInt32 h)
                {
                    for (UInt32 w = 0U; w != terrainDim.mTileSize + 1U; w++)
                    {
                        const auto i = h * (terrainDim.mTileSize + 1U) + w;
                        heightData[i] = (heightData[i] & 0xFFFF0000) | static_cast<UInt32>(EncodeHeight(node.mHeightmap[i], terrainDim.mSurfaceType));
                    }
                });

                const auto& worldTranslation = transformAndCameraData.mWorldTranslation.XYZ();
                const auto& worldScale = transformAndCameraData.mWorldScale.XYZ();
                CalculatePositions(tile, terrainDim, worldTranslation, worldScale, heightData.data(), positionData.data());

                BoundingBox::CreateFromPoints(node.mBoundingBox, positionData.size(), positionData.data(), sizeof(positionData[0]));
                Float3 center, extent;
                node.mBoundingBox.GetCenter(&center);
                node.mBoundingBox.GetExtent(&extent);
                center = (center - worldTranslation) / worldScale;
                extent = extent / worldScale;
                node.mBoundingBox = BoundingBox(center, extent);


                threading::ParallelFor(terrainDim.mTileSize - 1, [this, &terrainDim, &positionData, &heightData, &node](UInt32 h)
                {
                    const auto height = terrainDim.mTileSize + 1, width = height;
                    h = h + 1U;
                    for (UInt32 w = 1U; w != terrainDim.mTileSize; w++)
                    {
                        const auto c = h * width + w;
                        const auto tc = positionData[c - width] - positionData[c];
                        const auto bc = positionData[c + width] - positionData[c];
                        const auto lc = positionData[c - 1] - positionData[c];
                        const auto rc = positionData[c + 1] - positionData[c];
                        const auto n = CalcNormal(tc, bc, lc, rc);
                        heightData[c] = static_cast<UInt32>(EncodeNormal(n) << 16U) | (heightData[c] & 0x0000FFFF);
                    }
                });

                auto leftTile = GetLeftTile(tile, terrainDim);
                auto rightTile = GetRightTile(tile, terrainDim);
                auto upTile = GetUpTile(tile, terrainDim);
                auto downTile = GetDownTile(tile, terrainDim);
                if (mDirtyTileList.find(leftTile) != mDirtyTileList.end())
                {
                    for (UInt32 h = 0U; h != height; h++)
                    {
                        UpdateNormalEdge(h, 0, heightData);
                    }
                }
                if (mDirtyTileList.find(rightTile) != mDirtyTileList.end())
                {
                    for (UInt32 h = 0U; h != height; h++)
                    {
                        UpdateNormalEdge(h, width - 1, heightData);
                    }
                }
                if (mDirtyTileList.find(upTile) != mDirtyTileList.end())
                {
                    for (UInt32 w = 0U; w != width; w++)
                    {
                        UpdateNormalEdge(0, w, heightData);
                    }
                }
                if (mDirtyTileList.find(downTile) != mDirtyTileList.end())
                {
                    for (UInt32 w = 0U; w != width; w++)
                    {
                        UpdateNormalEdge(height - 1, w, heightData);
                    }
                }
            }
        }

        for (const auto& tile : mDirtyTileList)
        {
            auto& node = GetTileNode(grid, tile);
            auto& heightData = node.mEncodeHeightmap;
            auto leftTile = GetLeftTile(tile, terrainDim);

            if (mDirtyTileList.find(leftTile) != mDirtyTileList.end())
            {
                auto& leftNode = GetTileNode(grid, leftTile);
                auto& leftHeightData = leftNode.mEncodeHeightmap;
                for (UInt32 h = 0; h < height; h++)
                {
                    auto& h0 = heightData[h * width];
                    auto& h1 = leftHeightData[h * width + width - 1];

                    auto b = (((h0 >> 16U) & 0x00FF) + ((h1 >> 16U) & 0x00FF)) / 2;
                    auto a = ((h0 >> 24U) + (h1 >> 24U)) / 2;
                    auto n = (a << 8) | b;

                    h0 = (n << 16) | (h0 & 0x0000FFFF);
                    h1 = (n << 16) | (h1 & 0x0000FFFF);
                }
            }
        }

        for (const auto& tile : mDirtyTileList)
        {
            auto& node = GetTileNode(grid, tile);
            auto& heightData = node.mEncodeHeightmap;
            auto upTile = GetUpTile(tile, terrainDim);

            if (mDirtyTileList.find(upTile) != mDirtyTileList.end())
            {
                auto& upNode = GetTileNode(grid, upTile);
                auto& upHeightData = upNode.mEncodeHeightmap;
                for (UInt32 w = 0; w < width; w++)
                {
                    auto& h0 = heightData[w];
                    auto& h1 = upHeightData[(height - 1) * width + w];

                    auto b = (((h0 >> 16U) & 0x00FF) + ((h1 >> 16U) & 0x00FF)) / 2;
                    auto a = ((h0 >> 24U) + (h1 >> 24U)) / 2;
                    auto n = (a << 8) | b;

                    h0 = (n << 16) | (h0 & 0x0000FFFF);
                    h1 = (n << 16) | (h1 & 0x0000FFFF);
                }
            }
        }

        std::vector<UInt32> weightData;

        for (const auto& tile : mDirtyTileList)
        {
            auto& node = GetTileNode(grid, tile);
            if (node.mHeightmapDirty)
            {
                auto heightmap = node.mRenderProperties.GetProperty<TexturePtr>(TERRAIN_HEIGHT_MAP_NAME);
                heightmap->UploadImage(0U, 0U, 0U, reinterpret_cast<UInt8*>(node.mEncodeHeightmap.data()), static_cast<UInt32>(node.mEncodeHeightmap.size() * sizeof(UInt32)));
                node.mHeightmapDirty = 0U;
            }

            if (node.mWeightTextureDirty)
            {
                Assert(GetNumBlendLayers(reader));
                const auto weightTextureCount = (GetNumBlendLayers(reader) + NumLayersPerWeightTexture - 1U) / NumLayersPerWeightTexture;
                for (UInt32 weightTextureIndex = 0; weightTextureIndex != weightTextureCount; weightTextureIndex++)
                {
                    auto weightTexture = node.mRenderProperties.GetProperty<TexturePtr>(fmt::format("{}{}", TERRAIN_WEIGHT_TEXTURE_NAME, weightTextureIndex));
                    const auto sourceData = reinterpret_cast<UInt32*>(weightTexture->GetTextureData()->GetImageData(0U));

                    weightData.resize(weightTexture->GetWidth() * weightTexture->GetWidth());
                    for (UInt32 i = 0; i != weightData.size(); i++)
                    {
                        UInt32 data{};
                        for (UInt32 layer = 0U; layer != 4U; layer++)
                        {
                            const auto layerIndex = NumLayersPerWeightTexture * weightTextureIndex + layer;
                            if (layerIndex < node.mBlendLayerWeights.size() && !node.mBlendLayerWeights[layerIndex].empty())
                            {
                                data |= node.mBlendLayerWeights[layerIndex][i] << (8U * layer);
                            }
                            else
                            {
                                data |= sourceData[i] & (0xFFU << (8U * layer));
                            }
                        }

                        weightData[i] = data;
                    }

                    weightTexture->UploadImage(0U, 0U, 0U, reinterpret_cast<UInt8*>(weightData.data()), static_cast<UInt32>(weightData.size() * sizeof(UInt32)));
                }

                node.mWeightTextureDirty = 0U;
            }
        }

        mDirtyTileList.clear();
    }

    cross::BoundingBox TerrainSystemG::GetActiveBoundingBox(const TerrainComponentReader& reader, TerrainSystemG::ActiveTile& activeTile)
    {
        const auto& tile = ToTileLocator(reader, activeTile.mTileIndex);

        const auto& grid = GetGrid(reader);
        auto& node = GetTileNode(grid, tile);
        const auto terrainDim = GetTerrainDimension(reader);

        std::vector<Float3> positionData{Float3(-100, -100, 0), Float3(-100, 100, 0), Float3(100, 100, 0), Float3(100, -100, 0), Float3(-100, -100, 200), Float3(-100, 100, 200), Float3(100, 100, 200), Float3(100, -100, 200)};
        positionData.resize(8);

        std::vector<UInt32> heightData{0, 8800};
        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());
        const auto& worldTranslation = transformSystemG->GetWorldTranslation(transformComponent.Read());
        const auto& worldScale = transformSystemG->GetWorldScale(transformComponent.Read());
        CalculateTilePositions(tile, terrainDim, worldTranslation, worldScale, heightData.data(), positionData.data());
        cross::BoundingBox box;
        BoundingBox::CreateFromPoints(box, positionData.size(), positionData.data(), sizeof(positionData[0]));
        Float3 center, extent;
        box.GetCenter(&center);
        box.GetExtent(&extent);
        return box;
    }

    std::unique_ptr<AABBTree<std::string>> TerrainSystemG::GenerateBlockAABBTree(const TerrainComponentWriter& writer, UInt32 block, TileIndex tileIndex)
    {
        auto& reader = ecs::GrantReadAccess(writer);
        auto tileSize = GetTileSize(reader);

        const auto transformSystemG = mGameWorld->GetGameSystem<TransformSystemG>();
        const auto transformComponent = mGameWorld->GetComponent<WorldTransformComponentG>(reader.GetEntityID());
        const auto& worldTranslation = transformSystemG->GetWorldTranslation(transformComponent.Read());
        const auto& worldScale = transformSystemG->GetWorldScale(transformComponent.Read());

        std::unordered_map<std::string, std::vector<UInt32>> blockHeightmapTriangleIndices = GenerateBlockHeightmapTriangleIndices(tileSize, tileSize, block);

        UInt32 tileHeightmapDataSize = (tileSize + 1) * (tileSize + 1);
        std::vector<Float3> tileVertexList;
        tileVertexList.resize(tileHeightmapDataSize);

        float* tileHeightmapData = static_cast<float*>(GetTileHeightmapData(writer, tileIndex));
        for (UInt32 vertexIndex = 0; vertexIndex < tileHeightmapDataSize; ++vertexIndex)
        {
            tileVertexList[vertexIndex] = GetTileVertexWorldPosition(reader, worldTranslation, worldScale, tileIndex, vertexIndex, tileHeightmapData);
        }

        std::unique_ptr<AABBTree<std::string>> blockAABBTree = std::make_unique<AABBTree<std::string>>(static_cast<UInt32>(tileSize / block * tileSize / block));
        for (auto& blockMap : blockHeightmapTriangleIndices)
        {
            UInt32 triangleCount = static_cast<UInt32>(blockMap.second.size() / 3);
            std::vector<Float3> points(triangleCount);

            for (UInt32 i = 0; i < triangleCount; i++)
            {
                int indexIndex = i * 3;
                int index1 = blockMap.second[indexIndex];
                int index2 = blockMap.second[indexIndex + 1];
                int index3 = blockMap.second[indexIndex + 2];
                Float3 point1 = tileVertexList[index1];
                Float3 point2 = tileVertexList[index2];
                Float3 point3 = tileVertexList[index3];

                points.push_back(point1);
                points.push_back(point2);
                points.push_back(point3);
            }
            cross::BoundingBox box;
            BoundingBox::CreateFromPoints(box, points.size(), points.data(), sizeof(Float3));

            blockAABBTree->insertObject(blockMap.first, box);
            blockAABBTree->insertTilePoints(blockMap.first, points);
        }

        return blockAABBTree;
    }

    AABBTree<cross::TileIndex>* TerrainSystemG::GetTerrainAABBTree(const TerrainComponentReader& reader) const 
    {
        return reader->mTerrainAABBTree.get();
    }

    void TerrainSystemG::GenerateTerrainAABBTree(const TerrainComponentWriter& writer) 
    {
        QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
        auto& reader = ecs::GrantReadAccess(writer);
        TerrainSystemG::DataVector<TerrainSystemG::ActiveTile> activeTiles = GetActiveTiles(reader);
        if (activeTiles.size() != 0)
            writer->mTerrainAABBTree = std::make_unique<AABBTree<cross::TileIndex>>(static_cast<UInt32>(200));
        if (activeTiles.size() == 1) 
        {   
                std::cout << "activeTiles 0 mBlockX: " << activeTiles[0].mTileIndex.mBlockX << " mBlockY : " << activeTiles[0].mTileIndex.mBlockX 
                          << "activeTiles 0 mTileX: " << activeTiles[0].mTileIndex.mTileX << " mTileY : " << activeTiles[0].mTileIndex.mTileY
                          << "activeTiles 0 mLevel: " << activeTiles[0].mTileIndex.mLevel << std::endl;
        }
        for (auto& activeTile : activeTiles) 
        {
            BoundingBox singleAABB = GetActiveBoundingBox(reader, activeTile);
            writer->mTerrainAABBTree->insertObject(activeTile.mTileIndex, singleAABB);
        }
    }

    void TerrainSystemG::GenerateTerrainTileAABBTree(const TerrainComponentWriter& writer, UInt32 block) 
    {
        auto& reader = ecs::GrantReadAccess(writer);
        TerrainSystemG::DataVector<TerrainSystemG::ActiveTile> activeTiles = GetActiveTiles(reader);
        writer->mBlock = block;
        for (auto& activeTile : activeTiles ) 
        {
            TileIndex tileIndex = activeTile.mTileIndex;
            std::unique_ptr<AABBTree<std::string>> blockAABBTree = GenerateBlockAABBTree(writer, block, tileIndex);
            writer->mTerrainTileAABBTreeMap.insert(std::make_pair(tileIndex, *blockAABBTree));
        }
    }

    AABBTree<std::string> TerrainSystemG::GetBlockAABBTree(const TerrainComponentWriter& writer, TileIndex tileIndex) 
    {
        auto& reader = ecs::GrantReadAccess(writer);
        if (reader->mTerrainTileAABBTreeMap.find(tileIndex) != reader->mTerrainTileAABBTreeMap.end()) 
        {
            auto value = reader->mTerrainTileAABBTreeMap.find(tileIndex);
            return value->second;
        }

        std::unique_ptr<AABBTree<std::string>> blockAABBTree = GenerateBlockAABBTree(writer, writer->mBlock, tileIndex);
        writer->mTerrainTileAABBTreeMap.insert(std::make_pair(tileIndex, *blockAABBTree));
        return *blockAABBTree;
    }
#endif
}
