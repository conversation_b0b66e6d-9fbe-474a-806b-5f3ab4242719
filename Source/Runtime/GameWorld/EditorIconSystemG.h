#pragma once
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"

#include "Resource/Resource.h"
#include "RenderEngine/EditorIconSystemR.h"

#define ICON_RADIUS 50.0F

namespace cross 
{
    struct EditorIconComponentG : ecs::IComponent
    {
        CEComponentInternal(SystemType = EditorIconSystemG);

        CEFunction(Reflect) 
        ENGINE_API static ecs::ComponentDesc* GetDesc();

        CEProperty(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", DisplayName = "Editor Billboard Scale"))
        float Scale{ 1.0f };

        CE_Serialize_Deserialize;
    };

    class ENGINE_API EditorIconSystemG : public GameSystemBase
    {
        CESystemInternal(ComponentType = EditorIconComponentG)

    public:
        using EditorIconComponentHandle = ecs::ComponentHandle<EditorIconComponentG>;
        DEFINE_COMPONENT_READER_WRITER(EditorIconComponentG, EditorIconComponentReader, EditorIconComponentWriter);

        CEFunction(Reflect)
        static EditorIconSystemG* CreateInstance();
        virtual void Release() override;
        virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;
        virtual void NotifyAddRenderSystemToRenderWorld() override;
        virtual RenderSystemBase* GetRenderSystem() override;
        CEFunction(Reflect)
        virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    protected:
        virtual void OnBeginFrame(FrameParam* frameParam) override;
        virtual void OnEndFrame(FrameParam* frameParam) override;

    public:
        CEFunction(Editor)
        void GetEditorIconComponent(const EditorIconComponentReader& component, EditorIconComponentG& outValue);

        CEFunction(Editor)
        void SetEditorIconComponent(const EditorIconComponentWriter& component, const EditorIconComponentG& inValue);

        CEFunction(Editor)
        bool GetDrawIcons() { return mDrawIcons; }

        CEFunction(Editor)
        void SetDrawIcons(const bool& drawIcons) 
        { 
            mDrawIcons = drawIcons;
            DispatchRenderingCommandWithToken([renderSystem = mRenderEditorIconSystem, drawIcons]
                {
                    renderSystem->SetDrawIcons(drawIcons);
                });
        }

        const std::vector<ecs::EntityID>& GetIconEntities()
        {
            return tIconEntities;
        }

        const std::vector<std::tuple<ecs::EntityID, MeshAssetDataResourcePtr, Float4x4>>& GetMeshEntities()
        {
            return tMeshEntities;
        }

        void RegisterCommonIconEntity(ecs::EntityID entity, const std::string& texturePath);
        void UnRegisterCommonIconEntity(ecs::EntityID entity);

    public:
        static SerializeNode SerializeEditorIconComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
        static void DeserializeEditorIconComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
        static void PostDeserializeEditorIconComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    protected:
        EditorIconSystemG();
        virtual ~EditorIconSystemG();

        void AddPrimitiveData(ecs::EntityID entity, const PrimitiveData* primitiveData, const Float4x4& worldTransformMatrix, MaterialPtr material);

        const char* GetIconFxPath(bool reverseZ);
        const char* GetMeshFxPath(bool reverseZ);
        MaterialPtr GetOrCreateMaterial(const char* materialPath);
        MaterialPtr GetOrCreateIconMaterial(const char* texturePath);
        MaterialPtr GetOrCreateMeshMaterial(const char* texturePath);
        MeshAssetDataResourcePtr GetOrCreateUsedMesh(const char* meshPath);
        void DrawCommonIcon(ecs::EntityID entity, float scale, const char* texturePath);

        std::map<UniqueString, MaterialPtr> mUsedMaterial;
        std::map<UniqueString, MeshAssetDataResourcePtr> mUsedMesh;
        std::vector<ecs::EntityID> tIconEntities;
        std::vector<std::tuple<ecs::EntityID, MeshAssetDataResourcePtr, Float4x4>> tMeshEntities;
        std::unordered_map<ecs::EntityID, std::string> mCommonIconEntities;
        EditorIconSystemR* mRenderEditorIconSystem{ nullptr };
        bool mDrawIcons{ false };
        bool mIsRenderObjectOwner{ true };
        FrameAllocator* mCurFrameAllocator{ nullptr };
    };
}