#include "HybridGIPass.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderPipeline/FFSRenderPipeline.h"

namespace cross
{
    void HybridGIPassSettings::Initialize()
    {
        LOAD_RENDER_PIPELINE_MATERIAL(HybridVisualizeSceneMtl);
        LOAD_RENDER_PIPELINE_MATERIAL(HybridVisualizeSHCoefsMtl);
        LOAD_RENDER_PIPELINE_MATERIAL(HybridVisualizeCubeMtl);
        LOAD_RENDER_PIPELINE_TEXTURE(EnvBRDFTexture);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(UpdateVoxelizeTextureShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(ClearReplacementShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridSceneRadiosityShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridSceneLightingCompositeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVisualizeVoxelsShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVoxelizerUpdateShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridCompactTracesShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherTraceVoxelsShader);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVisualizeSHComputeShader);

        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HiZGeneratorShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFGTraceHZBShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridImportanceSamplingShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridImportanceSamplingHalfResShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherFilteringShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherFilteringHalfResShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherFilteringProbeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherIntegrateShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherTemporalShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherScreenProbeComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridFinalGatherRayDebugShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVoxelizerCompactComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVoxelizerDirectLightingComputeShader);
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(HybridVoxelLightCullingComputeShader);

        mHybridSurfelSetting.Initialize();

        mHybridShortRangeAOSetting.Initialize();
    }

    const cross::HybridGIPostProcessSetting& HybridGIPassSettings::GetGIPostProcess(const GameContext& gameContext)
    {
        static const cross::HybridGIPostProcessSetting defaultSetting;
        return defaultSetting;
        //return gameContext.mRenderPipeline->GetPostProcessVolumeSetting()->BaseSettings.GISetting;
    }

    PassDesc HybridGIPass::GetPassDesc()
    {
        return PassDesc("HybridGIPass", "this is a compute based HybridGI");
    }

    HybridGIPass::~HybridGIPass() 
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        if (mRayDebugFeedbackBuffer[0])
        {
            for (int i = 0; i < RAY_DEBUG_MAX_FLYING_FRAME; i++)
            {
                rendererSystem->DestroyNGIObject(mRayDebugFeedbackBuffer[i]);
            }
        }
        if (mRayDebugFreezeDatas)
        {
            free(mRayDebugFreezeDatas);
        }
    }
    void HybridGIPass::InitializeParams(const GameContext& gameContext)
    {
        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        InitializeHybridCommon(gameContext);
        InitializeViewParams(gameContext);
        InitializeDebugState();

        if (mSetting.IsEnableVoxelGI(gameContext))
        {
            if (!mVoxelRenderer)
            {
                mVoxelRenderer = std::make_unique<HybridVoxelRenderer>(mHybridViewParams, mSetting, mRenderPipeline, mHybridFGCommon);
            }
            mVoxelRenderer->InitializeVoxelLightingState(red, mVisualize);
        }

        if (mSetting.mHybridSurfelSetting.enable)
        {
            if (!mSurfelRenderer)
            {
                mSurfelRenderer = std::make_unique<HybridSurfelRenderer>();
            }
            mSurfelRenderer->SetSurfelInputs(input_gBufferViews, input_objCullingGUIDView, input_sceneColorView, input_depthView, mHybridViewParams, mHybridFGCommon);
        }

        if (mSetting.mHybridShortRangeAOSetting.enable)
        {
            if (!mShortRangeAORenderer)
            {
                mShortRangeAORenderer = std::make_unique<HybridShortRangeAORenderer>();
            }
            mShortRangeAORenderer->SetShortRangeAOInputs(gameContext, mHybridViewParams, mHybridFGCommon);
        }

        InitializeOctahedralSolidAngleTexture(gameContext);
        if (mSetting.mEnableDebugRay)
        {
            mRayDebugRayDataSize = sizeof(Float4) * static_cast<UInt32>(std::pow(mHybridFGCommon.mScreenProbeTracingOctahedronResolution, 2)) * 3 + 1;
            if (mRayDebugFeedbackBuffer[0] == nullptr)
            {
                auto desc = NGIBufferDesc{mRayDebugRayDataSize, NGIBufferUsage::CopyDst};
                for (int i = 0; i < RAY_DEBUG_MAX_FLYING_FRAME; i++)
                {
                    mRayDebugFeedbackBuffer[i] = GetNGIDevice().CreateStagingBuffer(desc);
                }
            }
            if (mRayDebugFreezeDatas == nullptr)
            {
                UInt32 bufferSize = mRayDebugRayDataSize;
                mRayDebugFreezeDatas = static_cast<float*>(cross::Memory::Malloc(bufferSize));
            }
        }
    }

    void HybridGIPass::UpdateSkyLightContext(const GameContext& gameContext)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        const auto* skyLightSys = gameContext.mRenderWorld->GetRenderSystem<SkyLightSystemR>();
        if (auto skyTex = skyLightSys->GetSkyLightTexture(); skyTex)
        {
            red->SetProperty(BuiltInProperty::ce_SkyLightTexture, skyTex);
        }
        else
        {
            red->SetProperty(BuiltInProperty::ce_SkyLightTexture, rendererSystem->GetDefaultTextureCubeView());
        }
        if (auto buffer = skyLightSys->GetDiffuseProbe(); buffer)
        {
            red->SetProperty(BuiltInProperty::ce_UE4AmbientProbeSH, buffer);
        }
        red->SetProperty(NAME_ID("ENABLE_SKY_LIGHT_SH"), mSetting.mDebugTraceSkyLightSH);

        Float3 skyColor = skyLightSys->GetColor();
        red->SetProperty(BuiltInProperty::ce_SkyLightColor, skyColor);
        float skyLightIntensity = skyLightSys->GetSkyLightIntensity();
        red->SetProperty(BuiltInProperty::ce_SkyLightIntensity, skyLightIntensity);
    }

    void HybridGIPass::UpdateHybridGIRenderContext(const GameContext& gameContext)
    {
        UpdateContextHybridCommon(gameContext);
        UpdateContextViewParams(gameContext);
        UpdateSkyLightContext(gameContext);
    }

    void HybridGIPass::InitializeDebugState()
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();

        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        mDebugState.mDebugSceneColor = IRenderPipeline::CreateTextureView2D(
            "Hybrid.DebugSceneColor", viewWidth, viewHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);

        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear DebugSceneColor")->ClearTexture(mDebugState.mDebugSceneColor, clearValue);

        if (mSetting.mDebugShowSH3)
        {
            auto shBufSize = sizeof(float) * mDebugState.mDebugSHCoefsSize;
            for (auto i = 0; i < cDebugProbeNum; i++)
            {
                auto redBuffer = RED->AllocateBuffer("Hybrid.DebugSHCoefs", NGIBufferDesc{shBufSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
                mDebugState.mDebugSHCoefs[i] = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
                RED->AllocatePass("Clear DebugSHCoefs")->ClearBuffer(mDebugState.mDebugSHCoefs[i], 0);
                if (!mDebugState.mReadBackSHData[i])
                {
                    mDebugState.mReadBackSHData[i] = std::shared_ptr<float>(new float[shBufSize](), std::default_delete<float[]>());
                }
            }
        }
    }

    void cross::HybridGIPass::InitializeHybridCommon(const GameContext& gameContext)
    {
        Assert(input_targetView != nullptr && mInitialized == false);
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();
        int GHybridDownsampleFactor = mSetting.GetDownsampleFactor(gameContext);
        float GHybridFinalGatherAdaptiveProbeAllocationFraction = 0.5;
        int GHybridImportanceSamplingNumLevels = 1;
        int GHybridFinalGatherTracingOctahedronResolution = mSetting.IsProbeOctHalfResolution(gameContext) ? 4 : 8;
        int GScreenProbeBRDFOctahedronResolution = mSetting.IsProbeOctHalfResolution(gameContext) ? 4 : 8;
        int GHybridFixedJitterIndex = -1;
        int GHybridFinalGatherNumMips = 1;

        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        UInt2 viewSize(viewWidth, viewHeight);

        auto& out = mHybridFGCommon;
        out.mScreenProbeViewSize = UInt2(static_cast<UInt32>((viewSize.x + GHybridDownsampleFactor - 1) / GHybridDownsampleFactor), static_cast<UInt32>((viewSize.y + GHybridDownsampleFactor - 1) / GHybridDownsampleFactor));
        out.mScreenProbeAtlasViewSize = UInt2(out.mScreenProbeViewSize.x, static_cast<UInt32>(out.mScreenProbeViewSize.y * (1 + GHybridFinalGatherAdaptiveProbeAllocationFraction)));
        out.mScreenProbeAtlasBufferSize = out.mScreenProbeAtlasViewSize;

        out.mNumUniformScreenProbes = out.mScreenProbeViewSize.x * out.mScreenProbeViewSize.y;
        out.mMaxNumAdaptiveProbes = out.mScreenProbeAtlasViewSize.x * (out.mScreenProbeAtlasViewSize.y - out.mScreenProbeViewSize.y);
        out.mFixedJitterIndex = GHybridFixedJitterIndex;

        out.mScreenProbeDownsampleFactor = GHybridDownsampleFactor;
        out.mScreenProbeTracingOctahedronResolution = GHybridFinalGatherTracingOctahedronResolution;
        out.mScreenProbeGatherOctahedronResolution = out.mScreenProbeTracingOctahedronResolution;
        out.mScreenProbeGatherOctahedronResolutionWithBorder = out.mScreenProbeGatherOctahedronResolution + 2 * (1 << (GHybridFinalGatherNumMips - 1));
        out.mScreenProbeGatherMaxMip = static_cast<float>(GHybridFinalGatherNumMips - 1);
        out.mScreenTraceNoFallbackThicknessScale = 2.f; // if hwrt is enabled, it should be 1
        out.mMaxImportanceSamplingOctahedronResolution = out.mScreenProbeTracingOctahedronResolution * (1 << GHybridImportanceSamplingNumLevels);
        out.mScreenProbeBRDFOctahedronResolution = GScreenProbeBRDFOctahedronResolution;

        out.mScreenProbeSceneDepth = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenProbeSceneDepth", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeWorldNormal = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenProbeWorldNormal", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R8G8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeWorldSpeed = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenProbeWorldSpeed", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenProbeTranslatedWorldPosition = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenProbeTranslatedWorldPosition", out.mScreenProbeAtlasBufferSize.x, out.mScreenProbeAtlasBufferSize.y, GraphicsFormat::R32G32B32A32_SFloat, 
            NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        auto redBuffer = RED->AllocateBuffer("Hybrid.NumAdaptiveScreenProbes", NGIBufferDesc{sizeof(UInt32), NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        out.mNumAdaptiveScreenProbes = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        redBuffer = RED->AllocateBuffer("Hybrid.daptiveScreenProbeData", NGIBufferDesc{sizeof(UInt32) * out.mMaxNumAdaptiveProbes, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        out.mAdaptiveScreenProbeData = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32)});
        out.mScreenTileAdaptiveProbeHeader = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenTileAdaptiveProbeHeader", out.mScreenProbeViewSize.x, out.mScreenProbeViewSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mScreenTileAdaptiveProbeIndices = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenTileAdaptiveProbeIndices", viewSize.x, viewSize.y, GraphicsFormat::R16_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        UInt2 traceViewSize = out.GetScreenProbeOctahedronViewSize();
        out.mTraceRadiance =
            IRenderPipeline::CreateTextureView2D("Hybrid.TraceRadiance", traceViewSize.x, traceViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceHit = IRenderPipeline::CreateTextureView2D("Hybrid.TraceHit", traceViewSize.x, traceViewSize.y, GraphicsFormat::R32_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceRadianceFiltered = IRenderPipeline::CreateTextureView2D(
            "Hybrid.TraceRadianceFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        out.mTraceHitFiltered =
            IRenderPipeline::CreateTextureView2D("Hybrid.TraceHitFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
//         out.mTraceMovingFiltered =
//             IRenderPipeline::CreateTextureView2D("Hybrid.TraceMovingFiltered", traceViewSize.x, traceViewSize.y, GraphicsFormat::R8_UNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Hybrid TraceRadiance")->ClearTexture(out.mTraceRadiance, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Hybrid TraceHit")->ClearTexture(out.mTraceHit, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Hybrid TraceRadianceFiltered")->ClearTexture(out.mTraceRadianceFiltered, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Hybrid TraceHitFiltered")->ClearTexture(out.mTraceHitFiltered, NGIClearValue({0, 0, 0, 0}));
        //RED->AllocatePass("Clear Hybrid TraceMovingFiltered")->ClearTexture(out.mTraceMovingFiltered, NGIClearValue({0, 0, 0, 0}));

        out.mDummyBlackTexture = IRenderPipeline::CreateTextureView2D("Hybrid.DummyTexture", 4, 4, GraphicsFormat::R8G8B8A8_SNorm, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear DummyBlackTexture")->ClearTexture(out.mDummyBlackTexture, NGIClearValue({0, 0, 0, 0}));
        if (mSetting.mIrradianceFormatUseSH3)
        {
            out.mScreenProbeRadianceSHAmbient = IRenderPipeline::CreateTextureView2D("Hybrid.ScreenProbeRadianceSHAmbient",
                                                                                     out.mScreenProbeAtlasBufferSize.x,
                                                                                     out.mScreenProbeAtlasBufferSize.y,
                                                                                     GraphicsFormat::R11G11B10_UFloatPack32,
                                                                                     NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid ScreenProbeRadianceSHAmbient")->ClearTexture(out.mScreenProbeRadianceSHAmbient, NGIClearValue({0, 0, 0, 0}));
            out.mScreenProbeRadianceSHDirectional = IRenderPipeline::CreateTextureView2D("Hybrid.ScreenProbeRadianceSHDirectional",
                                                                                         out.mScreenProbeAtlasBufferSize.x * 6,
                                                                                         out.mScreenProbeAtlasBufferSize.y,
                                                                                         GraphicsFormat::R16G16B16A16_SFloat,
                                                                                         NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid ScreenProbeRadianceSHDirectional")->ClearTexture(out.mScreenProbeRadianceSHDirectional, NGIClearValue({0, 0, 0, 0}));
        }
        else
        {
            out.mScreenProbeIrradianceWithBorder = IRenderPipeline::CreateTextureView2D(
                "Hybrid.ScreenProbeIrradianceWithBorder", traceViewSize.x, traceViewSize.y, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid ScreenProbeIrradianceWithBorder")->ClearTexture(out.mScreenProbeIrradianceWithBorder, NGIClearValue({0, 0, 0, 0}));
        }

        UInt2 octWithBorderSize = out.GetScreenProbeOctahedronWithBorderViewSize();
        out.mScreenProbeFilterRadianceWithBorder = IRenderPipeline::CreateTextureView2D(
            "Hybrid.ScreenProbeFilterRadianceWithBorder", octWithBorderSize.x, octWithBorderSize.y, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Hybrid ScreenProbeFilterRadianceWithBorder")->ClearTexture(out.mScreenProbeFilterRadianceWithBorder, NGIClearValue({0, 0, 0, 0}));

        out.mDiffuseIndirect = IRenderPipeline::CreateTextureView2D("Hybrid.DiffuseIndirect", viewWidth, viewHeight, GraphicsFormat::R16G16B16A16_SFloat, 
            NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
        out.mRoughSpecularIndirect =
            IRenderPipeline::CreateTextureView2D("Hybrid.RoughSpecularIndirect", viewWidth, viewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Hybrid DiffuseIndirect")->ClearTexture(out.mDiffuseIndirect, NGIClearValue({0, 0, 0, 0}));
        RED->AllocatePass("Clear Hybrid RoughSpecularIndirect")->ClearTexture(out.mRoughSpecularIndirect, NGIClearValue({0, 0, 0, 0}));

        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear Hybrid ScreenProbeSceneDepth")->ClearTexture(out.mScreenProbeSceneDepth, clearValue);
        RED->AllocatePass("Clear Hybrid ScreenProbeWorldNormal")->ClearTexture(out.mScreenProbeWorldNormal, clearValue);
        RED->AllocatePass("Clear Hybrid ScreenProbeWorldSpeed")->ClearTexture(out.mScreenProbeWorldSpeed, clearValue);
        RED->AllocatePass("Clear Hybrid ScreenProbeTranslatedWorldPosition")->ClearTexture(out.mScreenProbeTranslatedWorldPosition, clearValue);
        RED->AllocatePass("Clear Hybrid ScreenTileAdaptiveProbeHeader")->ClearTexture(out.mScreenTileAdaptiveProbeHeader, clearValue);
        RED->AllocatePass("Clear Hybrid ScreenTileAdaptiveProbeIndices")->ClearTexture(out.mScreenTileAdaptiveProbeIndices, clearValue);
        RED->AllocatePass("Clear Hybrid NumAdaptiveScreenProbes")->ClearBuffer(out.mNumAdaptiveScreenProbes, 0);
        RED->AllocatePass("Clear Hybrid AdaptiveScreenProbeData")->ClearBuffer(out.mAdaptiveScreenProbeData, 0);
    }

    void cross::HybridGIPass::InitializeViewParams(const GameContext& gameContext)
    {
        Assert(input_targetView != nullptr && mInitialized == false);

        auto viewWidth = input_targetView->GetWidth();
        auto viewHeight = input_targetView->GetHeight();

        UInt2 viewSize(viewWidth, viewHeight);
        auto& outViewParams = mHybridViewParams;
        outViewParams.mRectMin = UInt2(0, 0);
        outViewParams.mRectMax = UInt2(viewWidth, viewHeight);
        outViewParams.mSizeAndInvSize = Float4(static_cast<float>(viewSize.x), static_cast<float>(viewSize.y), 1.0f / viewSize.x, 1.0f / viewSize.y);
        outViewParams.mRectArea = viewHeight * viewWidth;
        auto invViewSize = Float2(1.0f / viewSize.x, 1.0f / viewSize.y);
        Float2 UVViewMin = Float2(static_cast<float>(outViewParams.mRectMin.x) / viewSize.x, static_cast<float>(outViewParams.mRectMin.y) / viewSize.y);
        Float2 UVViewMax = Float2(static_cast<float>(outViewParams.mRectMax.x) / viewSize.x, static_cast<float>(outViewParams.mRectMax.y) / viewSize.y);
        // From UE, bilinear UV min max will shrink a little bit
        outViewParams.mUVViewBilinearMin = UVViewMin + 0.5f * invViewSize;
        outViewParams.mUVViewBilinearMax = UVViewMax - 0.5f * invViewSize;
        

        Float2 HZBUvFactor(viewWidth / static_cast<float>(2 * input_HiZView->GetWidth()), viewHeight / static_cast<float>(2 * input_HiZView->GetHeight()));
        outViewParams.mHZBUvFactorAndInvFactor = Float4(HZBUvFactor.x, HZBUvFactor.y, 1.0f / HZBUvFactor.x, 1.0f / HZBUvFactor.y);
        // This is a simple version of UE's GetScreenPositionScaleBias(), it's right by now
        outViewParams.mScreenPositionScaleBias = Float4(0.5, -0.5, 0.5, 0.5);
        // this is mHZBUvFactorAndInvFactor.zw actually
        auto HZBUVToScreenUVScale = Float2(1.f / HZBUvFactor.x, 1.f / HZBUvFactor.y) * Float2(2.f, -2.f) * Float2(outViewParams.mScreenPositionScaleBias.x, outViewParams.mScreenPositionScaleBias.y);
        // this is (0, 0) actually
        auto HZBUVToScreenUVBias = Float2(-1.0f, 1.0f) * Float2(outViewParams.mScreenPositionScaleBias.x, outViewParams.mScreenPositionScaleBias.y) + Float2(outViewParams.mScreenPositionScaleBias.z, outViewParams.mScreenPositionScaleBias.w);
        outViewParams.mHZBUVToScreenUVScaleBias = Float4(HZBUVToScreenUVScale.x, HZBUVToScreenUVScale.y, HZBUVToScreenUVBias.x, HZBUVToScreenUVBias.y);

        if (mSetting.mFixFrameIndex >= 0)
        {
            outViewParams.mFrameIndex = mSetting.mFixFrameIndex;
        }
        else
        {
            outViewParams.mFrameIndex++;
        }
        outViewParams.mPreExposure = 1.0f;


        const RenderCamera* renderCamera = gameContext.mRenderCamera;
        const auto& invViewMatrix = renderCamera->GetInvertViewMatrix();
        const auto& prevInvViewMatrix = renderCamera->GetLastFrameInvertViewMatrix();
        const auto& projMatrix = renderCamera->GetProjMatrix();
        const Float3 preViewTranslation = Float3(-invViewMatrix.m30, -invViewMatrix.m31, -invViewMatrix.m32);
        const auto& invViewProjectMatrix = renderCamera->GetInvertProjMatrix() * renderCamera->GetInvertViewMatrix();

        auto screenToTranslateWorld = Float4x4(
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, projMatrix.m22, 1,
            0.0f, 0.0f, projMatrix.m32, 0.0f)
            * invViewProjectMatrix 
            * Float4x4(
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 1.0f, 0.0f,
            preViewTranslation.x, preViewTranslation.y, preViewTranslation.z, 1.0f);

        outViewParams.mViewMatrix = renderCamera->GetViewMatrix();
        outViewParams.mScreenToTranslatedWorld = screenToTranslateWorld;
        outViewParams.mPreViewTranslation = preViewTranslation;
        outViewParams.mCameraOrigin = -preViewTranslation;
        outViewParams.mPrevPreViewTranslation = Float3(-prevInvViewMatrix.m30, -prevInvViewMatrix.m31, -prevInvViewMatrix.m32);
#ifdef CE_USE_DOUBLE_TRANSFORM
        outViewParams.mPrevPreViewTranslation = outViewParams.mPrevPreViewTranslation + (-renderCamera->GetTilePosition<true>() + renderCamera->GetTilePosition()) * LENGTH_PER_TILE_F;
#endif
        outViewParams.mCameraVelocityOffset = outViewParams.mCameraOrigin + outViewParams.mPrevPreViewTranslation;

        // From UE5
        auto& TanAndInvTanFOV = renderCamera->GetTanAndInvTanHalfFOV();
        outViewParams.mScreenRayLengthMultiplier = renderCamera->GetProjectionMode() == CameraProjectionMode::Perspective ? Float4(TanAndInvTanFOV.x, TanAndInvTanFOV.y, 0.f, 0.f) : Float4(0.f, 0.f, TanAndInvTanFOV.x, TanAndInvTanFOV.y);

        Double4x4 OffsetMat = Double4x4::Identity();
#ifdef CE_USE_DOUBLE_TRANSFORM
        OffsetMat = Double4x4::CreateTranslation(static_cast<Double3>(renderCamera->GetTilePosition() - renderCamera->GetTilePosition<true>()) * LENGTH_PER_TILE);
#endif

        outViewParams.mClipToPrevClip = static_cast<Float4x4>(Double4x4(renderCamera->GetViewProjMatrix()).Inverted() * OffsetMat * Double4x4(renderCamera->GetLastFrameViewProjMatrix()));
    }

    void cross::HybridGIPass::UpdateContextHybridCommon(const GameContext& gameContext)
    {
        auto* red = mRenderPipeline->GetRenderingExecutionDescriptor();

        const auto& context = mHybridFGCommon;
        red->SetProperty(NAME_ID("_ScreenProbeViewSize"), context.mScreenProbeViewSize);
        red->SetProperty(NAME_ID("_ScreenProbeAtlasViewSize"), context.mScreenProbeAtlasViewSize);
        red->SetProperty(NAME_ID("_ScreenProbeAtlasBufferSize"), context.mScreenProbeAtlasBufferSize);
        red->SetProperty(NAME_ID("_ScreenProbeDownsampleFactor"), context.mScreenProbeDownsampleFactor);

        red->SetProperty(NAME_ID("_NumUniformScreenProbes"), context.mNumUniformScreenProbes);
        red->SetProperty(NAME_ID("_MaxNumAdaptiveProbes"), context.mMaxNumAdaptiveProbes);
        red->SetProperty(NAME_ID("_FixedJitterIndex"), context.mFixedJitterIndex);

        red->SetProperty(NAME_ID("_ScreenProbeTracingOctahedronResolution"), context.mScreenProbeTracingOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeGatherOctahedronResolution"), context.mScreenProbeGatherOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeGatherOctahedronResolutionWithBorder"), context.mScreenProbeGatherOctahedronResolutionWithBorder);
        red->SetProperty(NAME_ID("_ScreenProbeGatherMaxMip"), context.mScreenProbeGatherMaxMip);
        red->SetProperty(NAME_ID("_ScreenTraceNoFallbackThicknessScale"), context.mScreenTraceNoFallbackThicknessScale);
        red->SetProperty(NAME_ID("_OctahedralSolidAngleTextureResolutionSq"), context.mOctahedralSolidAngleTextureResolutionSq);
        red->SetProperty(NAME_ID("_MaxImportanceSamplingOctahedronResolution"), context.mMaxImportanceSamplingOctahedronResolution);
        red->SetProperty(NAME_ID("_ScreenProbeBRDFOctahedronResolution"), context.mScreenProbeBRDFOctahedronResolution);

        // should move all HybridGI common params to here, all Hybridgi passes share these params, instead of setting them in each pass

        // Texture related params, don't set them everywhere in each pass
        REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDepthHistoryRT, input_depthView, NGITextureAspect::Color);
        REDTextureView* preSceneColorView = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mPreSceneColorRT, input_sceneColorView);
        red->SetProperty(NAME_ID("SceneDepthTexture"), input_depthView);
        red->SetProperty(NAME_ID("HistorySceneDepthTexture"), depthHistoryTexView);
        red->SetProperty(NAME_ID("FurthestHZBTexture"), input_HiZView);
        red->SetProperty(NAME_ID("PrevSceneColorTexture"), preSceneColorView);
    }

    void cross::HybridGIPass::UpdateContextViewParams(const GameContext& gameContext)
    {
        auto* red = mRenderPipeline->GetRenderingExecutionDescriptor();

        red->SetProperty(NAME_ID("_View_RectMin"), mHybridViewParams.mRectMin);
        red->SetProperty(NAME_ID("_View_RectMax"), mHybridViewParams.mRectMax);
        red->SetProperty(NAME_ID("_View_SceneColorBlinearUVMin"), mHybridViewParams.mUVViewBilinearMin);
        red->SetProperty(NAME_ID("_View_SceneColorBlinearUVMax"), mHybridViewParams.mUVViewBilinearMax);
        red->SetProperty(NAME_ID("_View_ScreenRayLengthMultiplier"), mHybridViewParams.mScreenRayLengthMultiplier);

        red->SetProperty(NAME_ID("_View_SizeAndInvSize"), mHybridViewParams.mSizeAndInvSize);
        red->SetProperty(NAME_ID("_View_BufferSizeAndInvSize"), mHybridViewParams.mSizeAndInvSize);
        red->SetProperty(NAME_ID("_View_ScreenPositionScaleBias"), mHybridViewParams.mScreenPositionScaleBias);
        red->SetProperty(NAME_ID("_View_ScreenToTranslatedWorld"), mHybridViewParams.mScreenToTranslatedWorld);
        red->SetProperty(NAME_ID("_View_StateFrameIndex"), mHybridViewParams.mFrameIndex);
        red->SetProperty(NAME_ID("_View_StateFrameIndexMod8"), mHybridViewParams.mFrameIndex % 8);
        red->SetProperty(NAME_ID("_View_PreExposure"), mHybridViewParams.mPreExposure);
        red->SetProperty(NAME_ID("_View_PrevPreViewTranslation"), mHybridViewParams.mPrevPreViewTranslation);
        red->SetProperty(NAME_ID("_View_PreViewTranslation"), mHybridViewParams.mPreViewTranslation);
        red->SetProperty(NAME_ID("_View_UseReverseZ"), true);

        auto* camera = gameContext.mRenderCamera;
        red->SetProperty(NAME_ID("_View_ClipToPrevClip"), mHybridViewParams.mClipToPrevClip);
        red->SetProperty(NAME_ID("_HZBUvFactorAndInvFactor"), mHybridViewParams.mHZBUvFactorAndInvFactor);
        red->SetProperty(NAME_ID("_HZBUvToScreenUVScaleBias"), mHybridViewParams.mHZBUVToScreenUVScaleBias);
        red->SetProperty(NAME_ID("_View_TranslatedWorldToView"), mHybridViewParams.mViewMatrix);
        red->SetProperty(NAME_ID("_View_TranslatedWorldToClip"), camera->GetViewProjMatrix());
        red->SetProperty(NAME_ID("_View_ViewToClip"), camera->GetProjMatrix());
        red->SetProperty(NAME_ID("_View_ViewToTranslatedWorld"), mHybridViewParams.mViewMatrix.Inverted());
        
        auto tanAndInvTanHalfFOV = camera->GetTanAndInvTanHalfFOV();
        Float4 screenToViewSpace;

        float FovFixX = tanAndInvTanHalfFOV.x;
        float FovFixY = tanAndInvTanHalfFOV.y;
    
        screenToViewSpace.x = mHybridViewParams.mSizeAndInvSize.x * mHybridViewParams.mSizeAndInvSize.z * 2 * FovFixX;
        screenToViewSpace.y = mHybridViewParams.mSizeAndInvSize.y * mHybridViewParams.mSizeAndInvSize.w * -2 * FovFixY;
    
        screenToViewSpace.z = -((mHybridViewParams.mRectMin.x * mHybridViewParams.mSizeAndInvSize.z * 2 * FovFixX) + FovFixX);
        screenToViewSpace.w = (mHybridViewParams.mRectMin.y * mHybridViewParams.mSizeAndInvSize.w * 2 * FovFixY) + FovFixY;

        red->SetProperty(NAME_ID("_View_ScreenToViewSpace"), screenToViewSpace);
    }

    void cross::HybridGIPass::InitializeOctahedralSolidAngleTexture(const GameContext& gameContext)
    {
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* RED = rendererSystem->GetRenderingExecutionDescriptor();
        auto* HybridFinalGatherFilteringShader = mSetting.GetHybridFinalGatherFilteringShader(gameContext);

        static UInt32 GHybridOctahedralSolidAngleTextureSize = 16;
        if (!mHybridFGCommon.mOctahedralSolidAngleTex || !RED->Validate(mHybridFGCommon.mOctahedralSolidAngleTex))
        {
            auto texSize = GHybridOctahedralSolidAngleTextureSize;
            mHybridFGCommon.mOctahedralSolidAngleTextureSize = texSize;
            mHybridFGCommon.mOctahedralSolidAngleTextureResolutionSq = static_cast<float>(texSize) * texSize;
            mHybridFGCommon.mOctahedralSolidAngleTexture = IRenderPipeline::CreateTextureView2D("OctahedralSolidAngleTexture", texSize, texSize, GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource);
            auto* pass = RED->AllocatePass("OctahedralSolidAngleCS");
            mHybridFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("RWOctahedralSolidAngleTexture"), mHybridFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTextureSize"), texSize);

            UInt3 groupSize;
            HybridFinalGatherFilteringShader->GetThreadGroupSize("OctahedralSolidAngleCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(HybridFinalGatherFilteringShader, "OctahedralSolidAngleCS", (texSize + groupSize.x - 1) / groupSize.x, (texSize + groupSize.y - 1) / groupSize.y, 1);
            mHybridFGCommon.mOctahedralSolidAngleTex = mHybridFGCommon.mOctahedralSolidAngleTexture->mTexture;
        }
        else
        {
            mHybridFGCommon.mOctahedralSolidAngleTexture = RED->AllocateTextureView(
                mHybridFGCommon.mOctahedralSolidAngleTex, TextureCreateHelper::GetTexture2DViewDesc(mHybridFGCommon.mOctahedralSolidAngleTex->mDesc.Usage, mHybridFGCommon.mOctahedralSolidAngleTex->mDesc.Format, NGITextureAspect::Color));
        }
        mHybridFGCommon.mOctahedralSolidAngleTex->ExtendLifetime();
    }

    void cross::HybridGIPass::AssembleHybridGIScreenProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto HybridFinalGatherScreenProbeComputeShader = mSetting.HybridFinalGatherScreenProbeComputeShaderR;
        UInt2 viewSize(depthView->mTexture->mDesc.Width, depthView->mTexture->mDesc.Height);

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        auto passSetupCommon = [&](cross::REDPass* pass) {
            // screen textures
            pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
            pass->SetProperty(NAME_ID("_RWScreenProbeSceneDepth"), mHybridFGCommon.mScreenProbeSceneDepth);
            pass->SetProperty(NAME_ID("_RWScreenProbeWorldNormal"), mHybridFGCommon.mScreenProbeWorldNormal);
            pass->SetProperty(NAME_ID("_RWScreenProbeWorldSpeed"), mHybridFGCommon.mScreenProbeWorldSpeed);
            pass->SetProperty(NAME_ID("_RWScreenProbeTranslatedWorldPosition"), mHybridFGCommon.mScreenProbeTranslatedWorldPosition);
        };

        UInt3 groupSize;

        // Uniform screen probe pass.
        auto* uniformProbePass = RED->AllocatePass("Uniform Screen Probe");
        mHybridFGCommon.SetREDProperty(uniformProbePass);
        HybridFinalGatherScreenProbeComputeShader->GetThreadGroupSize("DownsampleDepthUniformCS", groupSize.x, groupSize.y, groupSize.z);
        passSetupCommon(uniformProbePass);
        uniformProbePass->Dispatch(
            HybridFinalGatherScreenProbeComputeShader, "DownsampleDepthUniformCS", (mHybridFGCommon.mScreenProbeViewSize.x + groupSize.x - 1) / groupSize.x, (mHybridFGCommon.mScreenProbeViewSize.y + groupSize.y - 1) / groupSize.y, 1);

        // Adaptive screen probe pass.
        UInt32 placementDownsampleFactor = mHybridFGCommon.mScreenProbeDownsampleFactor;
        if (placementDownsampleFactor >= 2)
        {
            do
            {
                placementDownsampleFactor /= 2;
                UInt2 downsampleSize((viewSize.x + placementDownsampleFactor - 1) / placementDownsampleFactor, (viewSize.y + placementDownsampleFactor - 1) / placementDownsampleFactor);
                auto* adaptiveProbePass = RED->AllocatePass("Adaptive Screen Probe");
                mHybridFGCommon.SetREDProperty(adaptiveProbePass);
                HybridFinalGatherScreenProbeComputeShader->GetThreadGroupSize("AdaptivePlacementCS", groupSize.x, groupSize.y, groupSize.z);
                passSetupCommon(adaptiveProbePass);
                adaptiveProbePass->SetProperty(NAME_ID("_RWNumAdaptiveScreenProbes"), mHybridFGCommon.mNumAdaptiveScreenProbes);
                adaptiveProbePass->SetProperty(NAME_ID("_RWAdaptiveScreenProbeData"), mHybridFGCommon.mAdaptiveScreenProbeData);
                adaptiveProbePass->SetProperty(NAME_ID("_RWScreenTileAdaptiveProbeHeader"), mHybridFGCommon.mScreenTileAdaptiveProbeHeader);
                adaptiveProbePass->SetProperty(NAME_ID("_RWScreenTileAdaptiveProbeIndices"), mHybridFGCommon.mScreenTileAdaptiveProbeIndices);
                adaptiveProbePass->SetProperty(NAME_ID("_PlacementDownsampleFactor"), placementDownsampleFactor);
                adaptiveProbePass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
                adaptiveProbePass->Dispatch(HybridFinalGatherScreenProbeComputeShader, "AdaptivePlacementCS", (downsampleSize.x + groupSize.x - 1) / groupSize.x, (downsampleSize.y * 2 + groupSize.y - 1) / groupSize.y, 1);
            } while (placementDownsampleFactor > static_cast<UInt32>(std::max(mSetting.GetAdaptiveProbeMinDownsampleFactor(gameContext), 1)));
        }
    }

    cross::REDTextureView* HybridGIPass::AssembleHybridGenerateRays(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto HybridImportanceSamplingShader = mSetting.GetImportanceSamplingShader(gameContext);
        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        REDTextureView* pdfView = gameContext.mRenderPipeline->CreateTextureView2D("Hybrid.BRDFProbabilityDensityFunction",
                                                      mHybridFGCommon.mScreenProbeAtlasBufferSize.x * mHybridFGCommon.mScreenProbeBRDFOctahedronResolution,
                                                      mHybridFGCommon.mScreenProbeAtlasBufferSize.y * mHybridFGCommon.mScreenProbeBRDFOctahedronResolution,
                                                      GraphicsFormat::R16_SFloat,
                                                      NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);

        const UInt32 BRDF_SHBufferSize = mHybridFGCommon.mScreenProbeAtlasBufferSize.x * mHybridFGCommon.mScreenProbeAtlasBufferSize.y * 9;
        auto redBuffer = RED->AllocateBuffer("Hybrid.BRDFProbabilityDensityFunctionSH", NGIBufferDesc{sizeof(float) * BRDF_SHBufferSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst});
        REDBufferView* bufferView = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopyDst, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(float)});
        NGIClearValue clearValue{{0, 0, 0, 0}};
        RED->AllocatePass("Clear Hybrid BRDFProbabilityDensityFunction")->ClearTexture(pdfView, clearValue);
        RED->AllocatePass("Clear Hybrid BRDFProbabilityDensityFunctionSH")->ClearBuffer(bufferView, 0);

        auto passSetupCommon = [&](cross::REDPass* pass) {
            mHybridFGCommon.SetREDProperty(pass);
            // screen textures
            pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
        };
        UInt3 groupSize;

        // Compute BRDF PDF
        auto* pdfPass = RED->AllocatePass("ComputeBRDF_PDF");
        passSetupCommon(pdfPass);
        pdfPass->SetProperty(NAME_ID("_RWBRDFProbabilityDensityFunctionSH"), bufferView);
        //pdfPass->SetProperty(NAME_ID("_RWBRDFProbabilityDensityFunction"), pdfView);

        HybridImportanceSamplingShader->GetThreadGroupSize("ComputeBRDFProbabilityDensityFunctionCS", groupSize.x, groupSize.y, groupSize.z);
        pdfPass->Dispatch(HybridImportanceSamplingShader, "ComputeBRDFProbabilityDensityFunctionCS", mHybridFGCommon.mScreenProbeAtlasBufferSize.x, mHybridFGCommon.mScreenProbeAtlasBufferSize.y, 1);


        // Importance Sampling Lighting
        bool bImportanceSampleLighting = true;
        REDTextureView* LightingProbabilityDensityFunction = nullptr;
        if (bImportanceSampleLighting) {
             LightingProbabilityDensityFunction = gameContext.mRenderPipeline->CreateTextureView2D("Hybrid.LightingProbabilityDensityFunction",
                                                                                                  mHybridFGCommon.mScreenProbeAtlasBufferSize.x * mHybridFGCommon.mScreenProbeTracingOctahedronResolution,
                                                                                                  mHybridFGCommon.mScreenProbeAtlasBufferSize.y * mHybridFGCommon.mScreenProbeTracingOctahedronResolution,
                                                                                                  GraphicsFormat::R16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid LightingProbabilityDensityFunction")->ClearTexture(LightingProbabilityDensityFunction, {0, 0, 0, 0});

            auto* lightingPdfPass = RED->AllocatePass("ComputeLightingPDF");
            passSetupCommon(lightingPdfPass);
            lightingPdfPass->SetProperty(NAME_ID("_RWLightingProbabilityDensityFunction"), LightingProbabilityDensityFunction);
            lightingPdfPass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mHybridFGCommon.mOctahedralSolidAngleTexture);
            HybridImportanceSamplingShader->GetThreadGroupSize("ComputeLightingProbabilityDensityFunctionCS", groupSize.x, groupSize.y, groupSize.z);
            lightingPdfPass->Dispatch(HybridImportanceSamplingShader, "ComputeLightingProbabilityDensityFunctionCS", mHybridFGCommon.mScreenProbeAtlasBufferSize.x, mHybridFGCommon.mScreenProbeAtlasBufferSize.y, 1);
        }


        // Generate Rays
        float GHybridImportanceSamplingMinPDFToTrace = .1f;
        UInt2 rayInfosTexSize = mHybridFGCommon.GetScreenProbeOctahedronViewSize();
        REDTextureView* importanceSampleRayInfosForTracing =
            IRenderPipeline::CreateTextureView2D("Hybrid.RayInfosForTracing", rayInfosTexSize.x, rayInfosTexSize.y, GraphicsFormat::R16_UInt, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
        RED->AllocatePass("Clear Hybrid importanceSampleRayInfosForTracing")->ClearTexture(importanceSampleRayInfosForTracing, {0, 0, 0, 0});

        auto* pass = RED->AllocatePass("GenerateRays 8x8");
        mHybridFGCommon.SetREDProperty(pass);
        pass->SetProperty(NAME_ID("_MinPDFToTrace"), GHybridImportanceSamplingMinPDFToTrace);
        //pass->SetProperty(NAME_ID("_BRDFProbabilityDensityFunction"), pdfView);
        pass->SetProperty(NAME_ID("_BRDFProbabilityDensityFunctionSH"), bufferView);
        pass->SetProperty(NAME_ID("_LightingProbabilityDensityFunction"), LightingProbabilityDensityFunction);
        pass->SetProperty(NAME_ID("_RWStructuredImportanceSampledRayInfosForTracing"), importanceSampleRayInfosForTracing);

        pass->Dispatch(HybridImportanceSamplingShader, "GenerateRaysCS", mHybridFGCommon.mScreenProbeAtlasBufferSize.x, mHybridFGCommon.mScreenProbeAtlasBufferSize.y, 1);
        return importanceSampleRayInfosForTracing;
    }

    void cross::HybridGIPass::AssembleHybridFGTraceHZB(const GameContext& gameContext, RenderingExecutionDescriptor* RED, GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* depthPyramid,
                                                     REDTextureView* sceneColorView,
                                                     REDTextureView* rayInfosForTracing)
    {
        auto HybridFGTraceHZBShader = mSetting.HybridFGTraceHZBShaderR;

        auto viewWidth = sceneColorView->mTexture->mDesc.Width;
        auto viewHeight = sceneColorView->mTexture->mDesc.Height;

        REDTextureView* preSceneColorView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mPreSceneColorRT, sceneColorView);
        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});
        REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
        auto ffsRdrPipe = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        auto [prevEmissiveColorRT, curEmissiveColorRTView] = ffsRdrPipe->GetEmissiveColorForHybridGI();
        REDTextureView* emissiveColorTexView = TextureCreateHelper::GetHistoryTextureView(RED, prevEmissiveColorRT, curEmissiveColorRTView);

        UInt2 traceViewSize = mHybridFGCommon.GetScreenProbeOctahedronViewSize();

        UInt3 groupThreadSize;
        HybridFGTraceHZBShader->GetThreadGroupSize("TraceHZBCS", groupThreadSize.x, groupThreadSize.y, groupThreadSize.z);

        Float2 dispatchThreadIdToScreenUV(1.0f / viewWidth, 1.0f / viewHeight);

        auto* csPass = RED->AllocatePass("HybridFGTraceHZB");
        mHybridFGCommon.SetREDProperty(csPass);
        
        auto* camera = gameContext.mRenderCamera;
        csPass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
        csPass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
        csPass->SetProperty(NAME_ID("EmissiveColorTexture"), emissiveColorTexView);
        csPass->SetProperty(NAME_ID("TRACE_SKY_LIGHTING"), !mSetting.IsEnableVoxelGI(gameContext));
        csPass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
        csPass->SetProperty(NAME_ID("ScreenProbeSceneDepth"), mHybridFGCommon.mScreenProbeSceneDepth);
        csPass->SetProperty(NAME_ID("ScreenProbeWorldNormal"), mHybridFGCommon.mScreenProbeWorldNormal);
        //csPass->SetProperty(NAME_ID("SceneDepthTexture"), depthSRV);
        //csPass->SetProperty(NAME_ID("HistorySceneDepthTexture"), depthHistoryTexView);
        //csPass->SetProperty(NAME_ID("FurthestHZBTexture"), depthPyramid);
        //csPass->SetProperty(NAME_ID("PrevSceneColorTexture"), preSceneColorView);
        csPass->SetProperty(NAME_ID("RWTraceRadiance"), mHybridFGCommon.mTraceRadiance);

        REDTextureView* TraceHitUAV =
            RED->AllocateTextureView(mHybridFGCommon.mTraceHit->mTexture,
                                     NGITextureViewDesc{NGITextureUsage::UnorderedAccess, mHybridFGCommon.mTraceHit->mTexture->mDesc.Format, mHybridFGCommon.mTraceHit->GetDesc().ViewDimension, mHybridFGCommon.mTraceHit->GetDesc().SubRange});
        csPass->SetProperty(NAME_ID("RWTraceHit"), TraceHitUAV);
        csPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
        csPass->SetProperty(NAME_ID("VelocityBuffer"), gBufferViews[3]);

        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());
        Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
        csPass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);

        csPass->SetProperty(NAME_ID("_MaxTraceDistance"), mSetting.mMaxScreenTraceDistance);
        csPass->SetProperty(NAME_ID("_MaxScreenTraceFraction"), mSetting.mMaxScreenTraceFraction);
        csPass->SetProperty(NAME_ID("_NumRayCastSteps"), mSetting.mNumRayCastSteps);

        csPass->SetProperty(NAME_ID("_DispatchThreadIdToScreenUV"), dispatchThreadIdToScreenUV);
        csPass->SetProperty(NAME_ID("_PrevSceneColorExposureScale"), mSetting.mSceneColorWithEmissiveExposureScale);
        csPass->SetProperty(NAME_ID("_EmissiveGIIntensity"), mSetting.mEmissiveGIIntensity);
        csPass->SetProperty("DEBUG_TRACE_SKY_LIGHTING_ONLY", mSetting.mDebugTraceSkyLightingOnly);

        csPass->SetProperty(NAME_ID("HIERARCHICAL_SCREEN_TRACING"), mSetting.mUseHiZRayTracing);
        csPass->SetProperty(NAME_ID("ClosestHZBTexture"), output_ClosestHiZView);
        csPass->SetProperty(NAME_ID("_HZBBaseTexelSize"), Float2(1.0f / output_ClosestHiZView->GetWidth(), 1.0f / output_ClosestHiZView->GetHeight()));

        csPass->Dispatch(HybridFGTraceHZBShader, "TraceHZBCS", (traceViewSize.x + groupThreadSize.x - 1) / groupThreadSize.x, (traceViewSize.y + groupThreadSize.y - 1) / groupThreadSize.y, 1);
    }

    void cross::HybridGIPass::AssembleHybridFilteringProbe(const GameContext& gameContext, RenderingExecutionDescriptor* RED)
    {
        float GHybridTemporalFilterProbesHistoryWeight = 0.85f;
        float GHybridTemporalFilterProbesHistoryDistanceThreshold = 100;

        float GHybridFilterMaxRadianceHitAngle = 10.0f;
        float GHybridScreenFilterPositionWeightScale = 1000.0f;
        SInt32 GHybridSpatialFilterHalfKernelSize = 1;

        UInt2 probeOctViewSize = mHybridFGCommon.GetScreenProbeOctahedronViewSize();

        auto HybridFinalGatherFilteringProbeShader = mSetting.HybridFinalGatherFilteringProbeShaderR;

        // TODO(chopperlin) will add a prefilter pass here, to guide temporal and spatial filter when camera move very quickly

        if (mSetting.mEnableProbeTemporalFilter)
        {
            auto traceRadiance = IRenderPipeline::CreateTextureView2D(
                "Hybrid.ScreenProbeTemporallyFilteredRadiance", probeOctViewSize.x, probeOctViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid ScreenProbeTemporallyFilteredRadiance")->ClearTexture(traceRadiance, NGIClearValue({0, 0, 0, 0}));

            REDTextureView* probeHitDistanceHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeHitDistanceHistoryRT, mHybridFGCommon.mTraceHitFiltered);
            REDTextureView* probeRadianceHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeRadianceHistoryRT, mHybridFGCommon.mTraceRadianceFiltered);
            REDTextureView* probeSceneDepthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeSceneDepthHistoryRT, mHybridFGCommon.mScreenProbeSceneDepth);
            REDTextureView* probeTranslatedWorldPositionHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT, mHybridFGCommon.mScreenProbeTranslatedWorldPosition);

            auto* pass = RED->AllocatePass("TemporallyAccumulateRadiance");
            mHybridFGCommon.SetREDProperty(pass);
            auto* camera = gameContext.mRenderCamera;
            pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
            pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("VelocityBuffer"), input_gBufferViews[3]);

            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mHybridFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), traceRadiance);

            pass->SetProperty(NAME_ID("PROBE_TEMPORAL_FILTER_WITH_HIT_DISTANCE"), mSetting.mProbeTemporalFilterWithHitDistance);
            pass->SetProperty(NAME_ID("HistoryScreenProbeHitDistance"), probeHitDistanceHistoryTexView);
            pass->SetProperty(NAME_ID("ScreenProbeHitDistance"), mHybridFGCommon.mTraceHitFiltered);
            pass->SetProperty(NAME_ID("SpatialFilterMaxRadianceHitAngle"), GHybridFilterMaxRadianceHitAngle * PI / 180.0f);

            pass->SetProperty(NAME_ID("HistoryScreenProbeRadiance"), probeRadianceHistoryTexView);
            pass->SetProperty(NAME_ID("HistoryScreenProbeSceneDepth"), probeSceneDepthHistoryTexView);
            pass->SetProperty(NAME_ID("HistoryScreenProbeTranslatedWorldPosition"), probeTranslatedWorldPositionHistoryTexView);

            pass->SetProperty(NAME_ID("ProbeTemporalFilterHistoryWeight"), GHybridTemporalFilterProbesHistoryWeight);
            pass->SetProperty(NAME_ID("HistoryDistanceThreshold"), GHybridTemporalFilterProbesHistoryDistanceThreshold);
            pass->SetProperty(NAME_ID("PrevInvPreExposure"), 1.0f);
            pass->SetProperty(NAME_ID("HistoryScreenPositionScaleBias"), mHybridViewParams.mScreenPositionScaleBias);
            UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());
            Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
            pass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);

            UInt3 groupSize;
            HybridFinalGatherFilteringProbeShader->GetThreadGroupSize("TemporalFilteringCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(HybridFinalGatherFilteringProbeShader, "TemporalFilteringCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
            mHybridFGCommon.mTraceRadianceFiltered = traceRadiance;
        }

        if (mSetting.mEnableProbeSpatialFilter)
        {
            REDTextureView* screenProbeRadiance = mHybridFGCommon.mTraceRadianceFiltered;

            auto* filteredScreenProbeRadiance = IRenderPipeline::CreateTextureView2D(
                "Hybrid.TraceRadianceProbeFiltered", probeOctViewSize.x, probeOctViewSize.y, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource | NGITextureUsage::CopyDst);
            RED->AllocatePass("Clear Hybrid TraceRadianceProbeFiltered", true)->ClearTexture(filteredScreenProbeRadiance, NGIClearValue({0, 0, 0, 0}));

            REDTextureView* screenProbeRadianceTexArray[2] = {screenProbeRadiance, filteredScreenProbeRadiance};
            for (auto passIndex = 0; passIndex < mSetting.mSpatialFilterNumPasses; passIndex++)
            {
                auto* pass = RED->AllocatePass("FilterRadianceWithGather");
                mHybridFGCommon.SetREDProperty(pass);
                pass->SetProperty(NAME_ID("ScreenProbeRadiance"), screenProbeRadianceTexArray[0]);
                pass->SetProperty(NAME_ID("ScreenProbeHitDistance"), mHybridFGCommon.mTraceHitFiltered);
                pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), screenProbeRadianceTexArray[1]);

                pass->SetProperty(NAME_ID("SpatialFilterMaxRadianceHitAngle"), GHybridFilterMaxRadianceHitAngle * PI / 180.0f);
                pass->SetProperty(NAME_ID("SpatialFilterPositionWeightScale"), GHybridScreenFilterPositionWeightScale);
                pass->SetProperty(NAME_ID("SpatialFilterHalfKernelSize"), GHybridSpatialFilterHalfKernelSize);

                UInt3 groupSize;
                HybridFinalGatherFilteringProbeShader->GetThreadGroupSize("SpatialFilteringCS", groupSize.x, groupSize.y, groupSize.z);
                pass->Dispatch(HybridFinalGatherFilteringProbeShader, "SpatialFilteringCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);

                screenProbeRadiance = screenProbeRadianceTexArray[1];
                screenProbeRadianceTexArray[1] = screenProbeRadianceTexArray[0];
                screenProbeRadianceTexArray[0] = screenProbeRadiance;
            }
            mHybridFGCommon.mTraceRadianceFiltered = screenProbeRadiance;
        }

        mTemporalState.mScreenProbeHitDistanceHistoryRT = mHybridFGCommon.mTraceHitFiltered->mTexture;
        mTemporalState.mScreenProbeRadianceHistoryRT = mHybridFGCommon.mTraceRadianceFiltered->mTexture;
        mTemporalState.mScreenProbeSceneDepthHistoryRT = mHybridFGCommon.mScreenProbeSceneDepth->mTexture;
        mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT = mHybridFGCommon.mScreenProbeTranslatedWorldPosition->mTexture;
        mTemporalState.mScreenProbeHitDistanceHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeRadianceHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeSceneDepthHistoryRT->ExtendLifetime();
        mTemporalState.mScreenProbeTranslatedWorldPositionHistoryRT->ExtendLifetime();
    }

    void cross::HybridGIPass::AssembleHybridConvertToIrradiance(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* rayInfosForTracing)
    {
        auto* HybridFinalGatherFilteringShader = mSetting.GetHybridFinalGatherFilteringShader(gameContext);
        if(mSetting.mEnableCompositeTraces)
        {
            auto* pass = RED->AllocatePass("CompositeTraces");
            mHybridFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mHybridFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("MaxRayIntensity"), 20.f);
            pass->SetProperty(NAME_ID("TraceRadiance"), mHybridFGCommon.mTraceRadiance);
            //REDTextureView* TraceHitSRV = RED->AllocateTextureView(
            //    mHybridFGCommon.mTraceHit->mTexture,
            //    NGITextureViewDesc{NGITextureUsage::ShaderResource, mHybridFGCommon.mTraceHit->mTexture->mDesc.Format, mHybridFGCommon.mTraceHit->GetDesc().ViewDimension, mHybridFGCommon.mTraceHit->GetDesc().SubRange});
            //pass->SetProperty(NAME_ID("TraceHit"), TraceHitSRV);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), mHybridFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeHitDistance"), mHybridFGCommon.mTraceHitFiltered);
            //pass->SetProperty(NAME_ID("RWScreenProbeTraceMoving"), mHybridFGCommon.mTraceMovingFiltered);

            UInt2 probeOctViewSize = mHybridFGCommon.GetScreenProbeOctahedronViewSize();
            UInt3 groupSize;
            HybridFinalGatherFilteringShader->GetThreadGroupSize("CompositeTracesCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(HybridFinalGatherFilteringShader, "CompositeTracesCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
        else 
        {
            mHybridFGCommon.mTraceRadianceFiltered = mHybridFGCommon.mTraceRadiance;
        }

        AssembleHybridFilteringProbe(gameContext, RED);

        {
            auto* pass = RED->AllocatePass("HybridConvertToIrradiance");
            mHybridFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("OctahedralSolidAngleTexture"), mHybridFGCommon.mOctahedralSolidAngleTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mHybridFGCommon.mTraceRadianceFiltered);

            pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
            if (mSetting.mIrradianceFormatUseSH3)
            {
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHAmbient"), mHybridFGCommon.mScreenProbeRadianceSHAmbient);
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHDirectional"), mHybridFGCommon.mScreenProbeRadianceSHDirectional);

                pass->SetProperty(NAME_ID("RWScreenProbeIrradianceWithBorder"), mHybridFGCommon.mDummyBlackTexture);
            }
            else
            {
                pass->SetProperty(NAME_ID("RWScreenProbeIrradianceWithBorder"), mHybridFGCommon.mScreenProbeIrradianceWithBorder);

                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHAmbient"), mHybridFGCommon.mDummyBlackTexture);
                pass->SetProperty(NAME_ID("RWScreenProbeRadianceSHDirectional"), mHybridFGCommon.mDummyBlackTexture);
            }

            UInt2 probeOctViewSize = mHybridFGCommon.GetScreenProbeOctahedronViewSize();
            UInt3 groupSize;
            HybridFinalGatherFilteringShader->GetThreadGroupSize("ConvertToIrradianceCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(HybridFinalGatherFilteringShader, "ConvertToIrradianceCS", (probeOctViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
        {
            auto* pass = RED->AllocatePass("FixupBorders");
            mHybridFGCommon.SetREDProperty(pass);
            pass->SetProperty(NAME_ID("ScreenProbeRadiance"), mHybridFGCommon.mTraceRadianceFiltered);
            pass->SetProperty(NAME_ID("RWScreenProbeRadiance"), mHybridFGCommon.mScreenProbeFilterRadianceWithBorder);

            UInt2 probeOctWithBorderViewSize = mHybridFGCommon.GetScreenProbeOctahedronWithBorderViewSize();
            UInt3 groupSize;
            HybridFinalGatherFilteringShader->GetThreadGroupSize("FixupBordersCS", groupSize.x, groupSize.y, groupSize.z);
            pass->Dispatch(HybridFinalGatherFilteringShader, "FixupBordersCS", (probeOctWithBorderViewSize.x + groupSize.x - 1) / groupSize.x, (probeOctWithBorderViewSize.y + groupSize.y - 1) / groupSize.y, 1);
        }
    }

    void cross::HybridGIPass::AssembleHybridIntegrate(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* sceneColorView, REDTextureView* depthView)
    {
        auto* HybridFinalGatherIntegrateShader = mSetting.GetHybridFinalGatherIntegrateShader();
        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());

        REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                             NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                depthView->mTexture->mDesc.Format,
                                                                                NGITextureType::Texture2D,
                                                                                {
                                                                                    NGITextureAspect::Depth,
                                                                                    0,
                                                                                    1,
                                                                                    0,
                                                                                    1,
                                                                                }});

        auto* pass = RED->AllocatePass("HybridIntegrate(" + std::to_string(viewSize.x) + "," + std::to_string(viewSize.y) + ")");
        mHybridFGCommon.SetREDProperty(pass);
        Float2 ScreenProbeGatherOctahedronResolutionWithBorder = Float2(static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder), static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder));
        Float2 ScreenProbeGatherOctahedronResolution = Float2(static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolution), static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolution));

        Float2 InvAtlasWithBorderBufferSize =
            Float2(1.0f, 1.0f) / (ScreenProbeGatherOctahedronResolutionWithBorder * Float2(static_cast<float>(mHybridFGCommon.mScreenProbeAtlasBufferSize.x), static_cast<float>(mHybridFGCommon.mScreenProbeAtlasBufferSize.y)));
        Float2 SampleRadianceProbeUVMul = ScreenProbeGatherOctahedronResolution * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceProbeUVAdd = std::exp2(mHybridFGCommon.mScreenProbeGatherMaxMip) * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceAtlasUVMul = Float2(ScreenProbeGatherOctahedronResolutionWithBorder) * InvAtlasWithBorderBufferSize;
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVMul"), SampleRadianceProbeUVMul);
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVAdd"), SampleRadianceProbeUVAdd);
        pass->SetProperty(NAME_ID("SampleRadianceAtlasUVMul"), SampleRadianceAtlasUVMul);

        pass->SetProperty(NAME_ID("MaxRoughnessToTrace"), mSetting.GetMaxRoughnessToTrace(gameContext));
        pass->SetProperty(NAME_ID("RoughnessFadeLength"), mSetting.GetInvRoughnessFadeLength(gameContext));

        pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
        pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
        pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
        pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);

        if (input_aoView)
        {
            pass->SetProperty(NAME_ID("ao_texture"), input_aoView);
            pass->SetProperty(NAME_ID("ENABLE_AO"), true);
        }
        else
        {
            pass->SetProperty(NAME_ID("ENABLE_AO"), false);
        }

        pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
        pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
        if (mSetting.mIrradianceFormatUseSH3)
        {
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mHybridFGCommon.mScreenProbeRadianceSHAmbient);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mHybridFGCommon.mScreenProbeRadianceSHDirectional);

            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mHybridFGCommon.mDummyBlackTexture);
        }
        else
        {
            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mHybridFGCommon.mScreenProbeIrradianceWithBorder);

            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mHybridFGCommon.mDummyBlackTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mHybridFGCommon.mDummyBlackTexture);
        }
        pass->SetProperty(NAME_ID("ScreenProbeRadianceWithBorder"), mHybridFGCommon.mScreenProbeFilterRadianceWithBorder);

        pass->SetProperty(NAME_ID("RWDiffuseIndirect"), mHybridFGCommon.mDiffuseIndirect);
        pass->SetProperty(NAME_ID("RWRoughSpecularIndirect"), mHybridFGCommon.mRoughSpecularIndirect);

        UInt3 groupSize;
        HybridFinalGatherIntegrateShader->GetThreadGroupSize("LightingIntegrateCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(HybridFinalGatherIntegrateShader, "LightingIntegrateCS", (viewSize.x + groupSize.x - 1) / groupSize.x, (viewSize.y + groupSize.y - 1) / groupSize.y, 1);
    }

    void HybridGIPass::AssembleDebugSHReadPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, int probeIndex, Float2 debugScreenPos, REDTextureView* sceneColorView)
    {
        auto* HybridVisualizeSHComputeShader = mSetting.HybridVisualizeSHComputeShaderR;
        auto* pass = RED->AllocatePass("HybridDebugReadProbeSH", true);
        mHybridFGCommon.SetREDProperty(pass);

        REDTextureView* depthSRV = RED->AllocateTextureView(input_depthView->mTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               input_depthView->mTexture->mDesc.Format,
                                                                               NGITextureType::Texture2D,
                                                                               {
                                                                                   NGITextureAspect::Depth,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});

        Float2 ScreenProbeGatherOctahedronResolutionWithBorder =
            Float2(static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder), static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolutionWithBorder));
        Float2 ScreenProbeGatherOctahedronResolution = Float2(static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolution), static_cast<float>(mHybridFGCommon.mScreenProbeGatherOctahedronResolution));

        Float2 InvAtlasWithBorderBufferSize =
            Float2(1.0f, 1.0f) / (ScreenProbeGatherOctahedronResolutionWithBorder * Float2(static_cast<float>(mHybridFGCommon.mScreenProbeAtlasBufferSize.x), static_cast<float>(mHybridFGCommon.mScreenProbeAtlasBufferSize.y)));
        Float2 SampleRadianceProbeUVMul = ScreenProbeGatherOctahedronResolution * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceProbeUVAdd = std::exp2(mHybridFGCommon.mScreenProbeGatherMaxMip) * InvAtlasWithBorderBufferSize;
        Float2 SampleRadianceAtlasUVMul = Float2(ScreenProbeGatherOctahedronResolutionWithBorder) * InvAtlasWithBorderBufferSize;
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVMul"), SampleRadianceProbeUVMul);
        pass->SetProperty(NAME_ID("SampleRadianceProbeUVAdd"), SampleRadianceProbeUVAdd);
        pass->SetProperty(NAME_ID("SampleRadianceAtlasUVMul"), SampleRadianceAtlasUVMul);
        pass->SetProperty(NAME_ID("ScreenPosition"), UInt2(static_cast<UInt32>(debugScreenPos.x * input_depthView->GetWidth()), static_cast<UInt32>(debugScreenPos.y * input_depthView->GetHeight())));

        pass->SetProperty(NAME_ID("RWDebugSHCoefs"), mDebugState.mDebugSHCoefs[probeIndex]);
        pass->SetProperty(NAME_ID("_GBuffer0"), input_gBufferViews[0]);
        pass->SetProperty(NAME_ID("_GBuffer1"), input_gBufferViews[1]);
        pass->SetProperty(NAME_ID("_GBuffer2"), input_gBufferViews[2]);
        pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);

        pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
        pass->SetProperty(NAME_ID("PROBE_IRRADIANCE_FORMAT"), mSetting.GetProbeIrradianceFormat());
        if (mSetting.mIrradianceFormatUseSH3)
        {
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mHybridFGCommon.mScreenProbeRadianceSHAmbient);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mHybridFGCommon.mScreenProbeRadianceSHDirectional);

            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mHybridFGCommon.mDummyBlackTexture);
        }
        else
        {
            pass->SetProperty(NAME_ID("ScreenProbeIrradianceWithBorder"), mHybridFGCommon.mScreenProbeIrradianceWithBorder);

            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHAmbient"), mHybridFGCommon.mDummyBlackTexture);
            pass->SetProperty(NAME_ID("ScreenProbeRadianceSHDirectional"), mHybridFGCommon.mDummyBlackTexture);
        }
        pass->SetProperty(NAME_ID("ScreenProbeRadianceWithBorder"), mHybridFGCommon.mScreenProbeFilterRadianceWithBorder);

        pass->Dispatch(HybridVisualizeSHComputeShader, "ReadProbeSHCoefsCS", 1, 1, 1);

        auto bufSize = mDebugState.mDebugSHCoefs[probeIndex]->GetDesc().SizeInBytes;
        NGICopyBuffer copyRegion{0, 0, bufSize};
        auto *debugSHCoefsStagingBuffer = GetNGIDevice().CreateStagingBuffer(NGIBufferDesc{bufSize, NGIBufferUsage::CopyDst});
        auto stagingBufferRED = RED->AllocateBuffer("HybridCopySH", debugSHCoefsStagingBuffer);
        auto* copyPass = RED->AllocatePass("HybridCopySH");
        copyPass->CopyBufferToBuffer(stagingBufferRED, mDebugState.mDebugSHCoefs[probeIndex]->mBuffer, 1, &copyRegion);
        stagingBufferRED->SetExternalState(NGIResourceState::HostRead);

        auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
        mDebugState.mPendingCopyBackTasks.emplace(frameCount, debugSHCoefsStagingBuffer, mDebugState.mReadBackSHData[probeIndex].get(), bufSize);
    }

    void HybridGIPass::UpdateReadBackBuffer(const GameContext& gameContext, RenderingExecutionDescriptor* RED) 
    {
        while (!mDebugState.mPendingCopyBackTasks.empty()) 
        {
            auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            auto& [frame, stagingBuffer, dst, range] = mDebugState.mPendingCopyBackTasks.front();
            if (frameCount - frame >= CmdSettings::Inst().gMaxQueuedFrame)
            {
                auto* src = stagingBuffer->MapRange(NGIBufferUsage::CopyDst, 0, range);
                memcpy(dst, src, range);
                stagingBuffer->UnmapRange(0, range);
                mDebugState.mPendingCopyBackTasks.pop();
            }
            else
            {
                break;
            }
        }
    }

    void HybridGIPass::AssembleDebugSHDrawPass(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView* outputRT, REDTextureRef depthView)
    {
        auto viewWidth = outputRT->GetWidth();
        auto viewHeight = outputRT->GetHeight();

        auto colorAttachmentRT =
            RED->AllocateTextureView(outputRT->mTexture, NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource, outputRT->mDesc.Format, NGITextureType::Texture2D, {NGITextureAspect::Color, 0, 1, 0, 1}});
        REDColorTargetDesc colorTargetDesc{colorAttachmentRT, NGILoadOp::Load, NGIStoreOp::Store, NGIClearValue{}};

        NGITextureViewDesc viewDesc{NGITextureUsage::DepthStencil, GraphicsFormat::D24_UNorm_S8_UInt, NGITextureType::Texture2D, {NGITextureAspect::Depth | NGITextureAspect::Stencil, 0, 1, 0, 1}};
        REDTextureView* depthStencilView = RED->AllocateTextureView(depthView, viewDesc);

        NGIClearValue clearValue;
        clearValue.depthStencil = {0, 0};
        REDDepthStencilTargetDesc depthStencilTargetDesc{depthStencilView, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Load, NGIStoreOp::DontCare, clearValue};

        NGIViewport viewport{0, 0, static_cast<float>(viewWidth), static_cast<float>(viewHeight), 0, 1};
        NGIScissor scissor{0, 0, viewWidth, viewHeight};

        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        MaterialR* material = mSetting.HybridVisualizeSHCoefsMtlR;
        auto shMtlState = material->GetMaterialRenderState("BasePass");

        MaterialR* cubeMat = mSetting.HybridVisualizeCubeMtlR;
        auto cubeMtlState = cubeMat->GetMaterialRenderState("BasePass");
        
        auto GetPipelineDesc = [](REDPass* pass, MaterialR::MaterialRenderState& mtlState, InputLayoutDesc& inputLayoutDesc, PrimitiveTopology topology, const void* shaderConst) {
            NGIGraphicsPipelineStateDesc pipelineDesc{pass->GetRenderPass(),
                                                      pass->GetSubpass(),
                                                      {mtlState.mProgram->mGUID.low, mtlState.mProgram->mGUID.high},
                                                      &mtlState.mProgram->mGraphicsProgramDesc,
                                                      mtlState.mProgram->mPipelineLayout,
                                                      inputLayoutDesc.GetHash().GetHash(),
                                                      &inputLayoutDesc,
                                                      topology,
                                                      *mtlState.mRaterizationState,
                                                      *mtlState.mBlendState,
                                                      *mtlState.mDepthStencilState,
                                                      0,
                                                      shaderConst};
            return pipelineDesc;
        };

        auto GetSphereInputLayoutDesc = []() {
            InputLayoutDesc sphereInputLayoutDesc{};
            VertexStreamLayout streamLayout{};
            streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float3, 0});
            streamLayout.AddVertexChannelLayout({VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(Float3)});
            sphereInputLayoutDesc.AddVertexStreamLayout(streamLayout);
            return sphereInputLayoutDesc;
        };

        auto GetCubeInputLayoutDesc = []() {
            InputLayoutDesc cubeInputLayoutDesc{};
            VertexStreamLayout streamLayout{};
            streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float4, 0});
            streamLayout.AddVertexChannelLayout({VertexChannel::Normal0, VertexFormat::Float4, sizeof(Float3)});
            cubeInputLayoutDesc.AddVertexStreamLayout(streamLayout);
            return cubeInputLayoutDesc;
        };
        InputLayoutDesc sphereInputLayoutDesc = GetSphereInputLayoutDesc();
        InputLayoutDesc cubeInputLayoutDesc = GetCubeInputLayoutDesc();

        RED->BeginRenderPass("HybridDebugShowSH", 1, &colorTargetDesc, &depthStencilTargetDesc);
        auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
        auto* subPass = RED->AllocateSubRenderPass("HybridDebugShowSH", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth);

        for (auto i = 0; i < cDebugProbeNum; i++)
        {
            float* shData = mDebugState.mReadBackSHData[i].get();
            Float3 worldPos(shData[120], shData[121], shData[122]);
            worldPos = worldPos + mSetting.mDebugSHOffset;
            Float4x4 worldMat = Float4x4::Compose(mSetting.mDebugSHScale, Quaternion(), worldPos);

            Float3 normalWS(shData[124], shData[125], shData[126]);
            Float4 color(shData[128], shData[129], shData[130], 1.0f);
            float lineScale = mSetting.mDebugSHScale.x;
            Float4x4 shLineWorldMat = Float4x4::Compose(Float3(lineScale * 3.f, lineScale * 0.1f, lineScale * 0.1f), Quaternion::CreateFrom2Vectors(Float3(1, 0, 0), normalWS), worldPos);

            subPass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
                auto* renderPrimitives = rendererSystem->GetRenderPrimitives();

                cmdList->SetViewports(1, &viewport);
                cmdList->SetScissors(1, &scissor);

                {
                    subPass->SetProperty(BuiltInProperty::ce_World, worldMat);
                    subPass->SetProperty(NAME_ID("_GIProbeSH"), shData, sizeof(Float4) * 7);

                    NGIBuffer* vbs[]{renderPrimitives->GetUnitSphere()};
                    NGIBuffer* ibs{renderPrimitives->GetUnitSphereIndex()};
                    cmdList->SetVertexBuffers(1, vbs, nullptr);
                    cmdList->SetIndexBuffer(ibs, 0, GraphicsFormat::R16_UInt);

                    const void* shaderConst = nullptr;
                    NGIGraphicsPipelineStateDesc pipelineDesc = GetPipelineDesc(pass, shMtlState, sphereInputLayoutDesc, PrimitiveTopology::TriangleList, shaderConst);
                    auto* pipeline = rendererSystem->GetPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
                    cmdList->SetGraphicsPipelineState(pipeline);

                    auto* pg = pass->GetContext().GetPassResourceGroup(shMtlState.mProgram->mResourceGroupLayouts[ShaderParamGroup_Pass], shMtlState.mProgram->mPipelineLayout, ShaderParamGroup_Pass);
                    if (pg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
                    }

                    auto* mg = shMtlState.GetResourceBinding();
                    if (mg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
                    }
                    cmdList->DrawIndexedInstanced(renderPrimitives->GetUnitSphereIndexCount(), 1, 0, 0, 0);
                }

                {
                    subPass->SetProperty(BuiltInProperty::ce_World, shLineWorldMat);
                    subPass->SetProperty(NAME_ID("_BaseColor"), color);

                    NGIBuffer* vbs[]{renderPrimitives->GetUnitCube()};
                    NGIBuffer* ibs{renderPrimitives->GetUnitCubeIndex()};
                    cmdList->SetVertexBuffers(1, vbs, nullptr);
                    cmdList->SetIndexBuffer(ibs, 0, GraphicsFormat::R16_UInt);

                    const void* shaderConst = nullptr;
                    NGIGraphicsPipelineStateDesc pipelineDesc = GetPipelineDesc(pass, cubeMtlState, cubeInputLayoutDesc, PrimitiveTopology::TriangleList, shaderConst);
                    auto* pipeline = rendererSystem->GetPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
                    cmdList->SetGraphicsPipelineState(pipeline);

                    auto* pg = pass->GetContext().GetPassResourceGroup(cubeMtlState.mProgram->mResourceGroupLayouts[ShaderParamGroup_Pass], cubeMtlState.mProgram->mPipelineLayout, ShaderParamGroup_Pass);
                    if (pg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
                    }

                    auto* mg = cubeMtlState.GetResourceBinding();
                    if (mg)
                    {
                        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
                    }
                    cmdList->DrawIndexedInstanced(renderPrimitives->GetUnitCubeIndexCount(), 1, 0, 0, 0);
                }
            });
        }

        RED->EndRenderPass();
    }

    NGITextureViewDesc TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage usage, GraphicsFormat format, NGITextureAspect aspect)
    {
        return NGITextureViewDesc{usage, format, NGITextureType::Texture2D, {aspect, 0, 1, 0, 1}};
    }

    NGITextureDesc TextureCreateHelper::GetTexture2DDesc(GraphicsFormat format, UInt32 width, UInt32 height, NGITextureUsage usage)
    {
        return NGITextureDesc{format, NGITextureType::Texture2D, 1, 1, width, height, 1, 1, usage};
    }

    REDTextureView* TextureCreateHelper::GetHistoryTextureView(RenderingExecutionDescriptor* RED, REDTextureRef historyTex, REDTextureView* currentTexView, NGITextureAspect aspect)
    {
        REDTextureView* ret;
        if (RED->Validate(historyTex) && historyTex->mDesc.Width == currentTexView->GetWidth() && historyTex->mDesc.Height == currentTexView->GetHeight())
        {
            ret = RED->AllocateTextureView(historyTex, GetTexture2DViewDesc(historyTex->mDesc.Usage, historyTex->mDesc.Format, aspect));
        }
        else
        {
            ret = currentTexView;
        }
        Assert(ret);
        return ret;
    }

    cross::REDTextureRef TextureCreateHelper::AllocateNewTemporalTexture(RenderingExecutionDescriptor* RED, std::string_view name, NGITextureUsage usage, GraphicsFormat format, UInt32 width, UInt32 height) 
    {
        REDTextureRef newTex = RED->AllocateTexture(name, GetTexture2DDesc(format, width, height, usage));
        newTex->ExtendLifetime();
        Assert(newTex);
        return newTex;
    }

    void HybridGIPass::AssembleHybridScreenTemporalFilter(const GameContext& gameContext, RenderingExecutionDescriptor* RED, REDTextureView*& diffuseIndirect, REDTextureView*& specularIndirect)
    {
        auto HybridFinalGatherTemporalShader = mSetting.HybridFinalGatherTemporalShaderR;

        UInt2 viewSize(input_targetView->GetWidth(), input_targetView->GetHeight());

        REDTextureView* depthSRV = RED->AllocateTextureView(input_depthView->mTexture,
                                                            NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                               input_depthView->mTexture->mDesc.Format,
                                                                               NGITextureType::Texture2D,
                                                                               {
                                                                                   NGITextureAspect::Depth,
                                                                                   0,
                                                                                   1,
                                                                                   0,
                                                                                   1,
                                                                               }});
        REDTextureView* depthHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
        auto depthFormat = GraphicsFormat::R16_UNorm;
        mTemporalState.mDepthHistoryRT = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Hybrid.DepthHistoryRT", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, depthFormat, viewSize.x, viewSize.y);
        REDTextureView* newDepthHistoryTexView = RED->AllocateTextureView(mTemporalState.mDepthHistoryRT, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, depthFormat));
        
        auto* normalTexView = input_gBufferViews[1];
        REDTextureView* normalHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mNormalHistoryRT, normalTexView);
        mTemporalState.mNormalHistoryRT = normalTexView->mTexture;
        mTemporalState.mNormalHistoryRT->ExtendLifetime();

        auto halfFloat = GraphicsFormat::R16G16B16A16_SFloat;

        REDTextureView* diffuseIndirectHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mDiffuseIndirectHistoryRT, diffuseIndirect);
        REDTextureRef RWNewHistoryDiffuseTex = TextureCreateHelper::AllocateNewTemporalTexture(
            RED, "Hybrid.DiffuseIndirect", NGITextureUsage::RenderTarget | NGITextureUsage::CopyDst | NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, halfFloat, viewSize.x, viewSize.y);
        mTemporalState.mDiffuseIndirectHistoryRT = RWNewHistoryDiffuseTex;
        REDTextureView* newHistoryDiffuseIndirectTexView = RED->AllocateTextureView(RWNewHistoryDiffuseTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, halfFloat));

        REDTextureView* specularIndirectHistoryTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mRoughSpecularIndirectHistoryRT, specularIndirect);
        REDTextureRef RWNewHistorySpecularTex = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Hybrid.RoughSpecularIndirect", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, halfFloat, viewSize.x, viewSize.y);
        mTemporalState.mRoughSpecularIndirectHistoryRT = RWNewHistorySpecularTex;
        REDTextureView* newHistorySpecularIndirectTexView = RED->AllocateTextureView(RWNewHistorySpecularTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, halfFloat));

        {
            mTemporalState.mDefaultR8RT = RED->AllocateTexture("Hybrid.DefaultR8", TextureCreateHelper::GetTexture2DDesc(GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y, NGITextureUsage::ShaderResource));
            mTemporalState.mDefaultR8View = RED->AllocateTextureView(mTemporalState.mDefaultR8RT, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm));
        }

        REDTextureRef RWNewNumFramesAccumulatedTex =
            TextureCreateHelper::AllocateNewTemporalTexture(RED, "Hybrid.NumHistoryFramesAccumulated", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y);
        REDTextureView* newNumFramesAccumulatedTexView = RED->AllocateTextureView(RWNewNumFramesAccumulatedTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, GraphicsFormat::R8_UNorm));
        REDTextureView* historyNumFramesAccumulatedTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mNumFramesAccumulatedRT, mTemporalState.mDefaultR8View);
        mTemporalState.mNumFramesAccumulatedRT = RWNewNumFramesAccumulatedTex;

        REDTextureRef RWNewFastUpdateModeTex = TextureCreateHelper::AllocateNewTemporalTexture(RED, "Hybrid.FastUpdateMode", NGITextureUsage::UnorderedAccess | NGITextureUsage::ShaderResource, GraphicsFormat::R8_UNorm, viewSize.x, viewSize.y);
        REDTextureView* newFastUpdateModeTexView = RED->AllocateTextureView(RWNewFastUpdateModeTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::UnorderedAccess, GraphicsFormat::R8_UNorm));
        REDTextureView* historyFastUpdateModeTexView = TextureCreateHelper::GetHistoryTextureView(RED, mTemporalState.mFastUpdateModeHistoryRT, mTemporalState.mDefaultR8View);
        mTemporalState.mFastUpdateModeHistoryRT = RWNewFastUpdateModeTex;

        auto* pass = RED->AllocatePass("HybridScreenTemporalReprojection");
        mHybridFGCommon.SetREDProperty(pass);
        auto* camera = gameContext.mRenderCamera;
        pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
        pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
        pass->SetProperty(NAME_ID("VelocityBuffer"), input_gBufferViews[3]);

        pass->SetProperty(NAME_ID("HistoryScreenPositionScaleBias"), mHybridViewParams.mScreenPositionScaleBias);
        Float4 HistoryUVMinMax{0.5f / viewSize.x, 0.5f / viewSize.y, (viewSize.x - 0.5f) / viewSize.x, (viewSize.y - 0.5f) / viewSize.y};
        pass->SetProperty(NAME_ID("HistoryUVMinMax"), HistoryUVMinMax);
        pass->SetProperty(NAME_ID("PrevSceneColorPreExposureCorrection"), 1.0f);
        pass->SetProperty(NAME_ID("InvFractionOfLightingMovingForFastUpdateMode"), mSetting.mFastUpdateScale);
        pass->SetProperty(NAME_ID("MaxFastUpdateModeAmount"), 0.9f); // seems useless
        pass->SetProperty(NAME_ID("MaxFramesAccumulated"), mSetting.mMaxFramesAccumulated);
        pass->SetProperty(NAME_ID("NumHistoryAccumulateThres"), mSetting.mNumHistoryAccumulateThres);
        pass->SetProperty(NAME_ID("HistoryNormalCosThreshold"), 0.707f); // cos(45degree)
        pass->SetProperty(NAME_ID("HistoryDistanceThreshold"), mSetting.mHistoryDistanceThreshold);
        pass->SetProperty(NAME_ID("DisocclusionDistanceThreshold"), mSetting.mDisocclusionDistanceThreshold);

        pass->SetProperty(NAME_ID("SceneDepthTexture"), depthSRV);
        pass->SetProperty(NAME_ID("DiffuseIndirectDepthHistory"), depthHistoryTexView);
        pass->SetProperty(NAME_ID("RWDepthHistory"), newDepthHistoryTexView);

        pass->SetProperty(NAME_ID("_GBuffer1"), normalTexView);
        pass->SetProperty(NAME_ID("NormalHistory"), normalHistoryTexView);

        pass->SetProperty(NAME_ID("DiffuseIndirect"), diffuseIndirect);
        pass->SetProperty(NAME_ID("DiffuseIndirectHistory"), diffuseIndirectHistoryTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryDiffuseIndirect"), newHistoryDiffuseIndirectTexView);

        pass->SetProperty(NAME_ID("RoughSpecularIndirect"), specularIndirect);
        pass->SetProperty(NAME_ID("RoughSpecularIndirectHistory"), specularIndirectHistoryTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryRoughSpecularIndirect"), newHistorySpecularIndirectTexView);

        pass->SetProperty(NAME_ID("HistoryNumFramesAccumulated"), historyNumFramesAccumulatedTexView);
        pass->SetProperty(NAME_ID("RWNumHistoryFramesAccumulated"), newNumFramesAccumulatedTexView);

        pass->SetProperty(NAME_ID("FastUpdateModeHistory"), historyFastUpdateModeTexView);
        pass->SetProperty(NAME_ID("RWNewHistoryFastUpdateMode"), newFastUpdateModeTexView);

        UInt3 groupSize;
        HybridFinalGatherTemporalShader->GetThreadGroupSize("TemporalReprojectionCS", groupSize.x, groupSize.y, groupSize.z);
        pass->Dispatch(HybridFinalGatherTemporalShader, "TemporalReprojectionCS", (viewSize.x + groupSize.x - 1) / groupSize.x, (viewSize.y + groupSize.y - 1) / groupSize.y, 1);

        diffuseIndirect = RED->AllocateTextureView(RWNewHistoryDiffuseTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, halfFloat));
        specularIndirect = RED->AllocateTextureView(RWNewHistorySpecularTex, TextureCreateHelper::GetTexture2DViewDesc(NGITextureUsage::ShaderResource, halfFloat));
    }

    bool HybridGIPass::ExecuteImp(const GameContext& gameContext)
    {
        auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        //bool needCompositeLighting = !mSetting.mDebugShowVoxels || !mSetting.IsEnableVoxelGI(gameContext);

        if (mStageStatus == StageStatus::Hybrid_GI_STAGE)
        {
            red->BeginRegion("HybridGIInit");

            InitializeParams(gameContext);

            red->EndRegion();

            red->BeginRegion("HybridGI");
            UpdateHybridGIRenderContext(gameContext);

            if (mSetting.IsEnableVoxelGI(gameContext))
            {
                QUICK_SCOPED_CPU_TIMING("AssembleVoxelLighting");
                REDTextureView* preSceneColor = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mPreSceneColorRT, input_sceneColorView, NGITextureAspect::Color);
                REDTextureView* depthSRV = red->AllocateTextureView(input_depthView->mTexture,
                    NGITextureViewDesc{NGITextureUsage::ShaderResource,
                        input_depthView->mTexture->mDesc.Format,
                        NGITextureType::Texture2D,
                        {
                            NGITextureAspect::Depth,
                            0,
                            1,
                            0,
                            1
                        }});
                REDTextureView* preDepth = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDepthHistoryRT, depthSRV, NGITextureAspect::Color);
                REDTextureView* preDiffuse = TextureCreateHelper::GetHistoryTextureView(red, mTemporalState.mDiffuseIndirectHistoryRT, input_sceneColorView, NGITextureAspect::Color);
                if (!mTemporalState.mDiffuseIndirectHistoryRT)
                {
                    preDiffuse = nullptr;
                }
                mVoxelRenderer->AssembleVoxelLighting(gameContext, red, input_depthView->mTexture, input_shadowProperties, mDebugState.mDebugSceneColor,
                            preSceneColor, preDepth, preDiffuse, input_gBufferViews);
            }

            // surfel light cache
            if (mSetting.mHybridSurfelSetting.enable)
            {
                mSurfelRenderer->RenderSurfels(gameContext, mDebugState.mDebugSceneColor);
                
            }

            // short range ao
            if (mSetting.mHybridShortRangeAOSetting.enable)
            {
                mShortRangeAORenderer->ComputeShortRangeAO(gameContext, mSetting.mHybridShortRangeAOSetting);
            }

            AssembleHybridGIScreenProbe(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);
            REDTextureView* rayInfosForTracing = AssembleHybridGenerateRays(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);

            // Default enable HZB trace
            AssembleHybridFGTraceHZB(gameContext, red, input_gBufferViews, input_depthView, input_HiZView, input_sceneColorView, rayInfosForTracing);

            if (mSetting.IsEnableVoxelGI(gameContext))
            {
                QUICK_SCOPED_CPU_TIMING("AssembleScreenProbeTraceVoxels");
                mVoxelRenderer->AssembleScreenProbeTraceVoxels(gameContext, red, rayInfosForTracing);
            }
            if(mSetting.mEnableDebugRay)
            {
                AssembleRayDebug(gameContext, red, input_gBufferViews, input_depthView, rayInfosForTracing);
            }
            AssembleHybridConvertToIrradiance(gameContext, red, rayInfosForTracing);
            if (mSetting.mDebugShowSH3 && !mSetting.mLockShowSH3)
            {
                AssembleDebugSHReadPass(gameContext, red, 0, mSetting.mDebugScreenPosition, input_sceneColorView);
                AssembleDebugSHReadPass(gameContext, red, 1, mSetting.mDebugScreenPosition2, input_sceneColorView);
            }
            AssembleHybridIntegrate(gameContext, red, input_gBufferViews, input_sceneColorView, input_depthView);
            if (mSetting.mEnableTemporalFilter)
            {
                    AssembleHybridScreenTemporalFilter(gameContext, red, mHybridFGCommon.mDiffuseIndirect, mHybridFGCommon.mRoughSpecularIndirect);
            }
        
            output_diffuseIndirectView = red->AllocateTextureView(mHybridFGCommon.mDiffuseIndirect->mTexture,
                                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                      mHybridFGCommon.mDiffuseIndirect->mTexture->mDesc.Format,
                                                                                      NGITextureType::Texture2D,
                                                                                      {
                                                                                          NGITextureAspect::Color,
                                                                                          0,
                                                                                          1,
                                                                                          0,
                                                                                          1,
                                                                                      }});
            output_specularIndirectView = red->AllocateTextureView(mHybridFGCommon.mRoughSpecularIndirect->mTexture,
                                                                   NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                      mHybridFGCommon.mRoughSpecularIndirect->mTexture->mDesc.Format,
                                                                                      NGITextureType::Texture2D,
                                                                                      {
                                                                                          NGITextureAspect::Color,
                                                                                          0,
                                                                                          1,
                                                                                          0,
                                                                                          1,
                                                                                      }});

            if (mSetting.mDebugShowSH3)
            {
                UpdateReadBackBuffer(gameContext, red);
                AssembleDebugSHDrawPass(gameContext, red, output_compositeIndirectView, input_depthView->mTexture);
            }

            red->EndRegion();
        }
        else if (mStageStatus == StageStatus::Hybrid_GI_COMPLETED_STAGE)
        {
            ExtendPreSceneColorRT(input_sceneColorView->mTexture);
        }

        return true;
    }


    void HybridGIPass::ExtendPreSceneColorRT(REDTextureRef sceneColorRT)
    {
        mTemporalState.mPreSceneColorRT = sceneColorRT;
        if (mTemporalState.mPreSceneColorRT->mType != REDResourceType::ExtendedToNext)
        {
            mTemporalState.mPreSceneColorRT->ExtendLifetime();
        }
    }

    void HybridGIPass::AssembleRayDebug(const GameContext& gameContext, RenderingExecutionDescriptor* RED, const GBufferTextures& gBufferViews, REDTextureView* depthView, REDTextureView* rayInfosForTracing) 
    {
        auto* HybridFinalGatherRayDebugShader = mSetting.GetHybridFinalGatherRayDebugShader();
        {
            /*buffer structure, | Ray Origin | Ray End + Color |...*/
            UInt32 bufferSize = mRayDebugRayDataSize;
            auto redBuffer = RED->AllocateBuffer("Hybrid.RayDebugOutputBuffer", NGIBufferDesc{bufferSize, NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::CopySrc});
            auto* redBufferView = RED->AllocateBufferView(redBuffer, NGIBufferViewDesc{redBuffer->mDesc.Usage, 0, redBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(Float4)});
            REDTextureView* depthSRV = RED->AllocateTextureView(depthView->mTexture,
                                                                NGITextureViewDesc{NGITextureUsage::ShaderResource,
                                                                                   depthView->mTexture->mDesc.Format,
                                                                                   NGITextureType::Texture2D,
                                                                                   {
                                                                                       NGITextureAspect::Depth,
                                                                                       0,
                                                                                       1,
                                                                                       0,
                                                                                       1,
                                                                                   }});

            auto passSetupCommon = [&](cross::REDPass* pass) {
                // screen textures
                pass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
                pass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
                pass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
                pass->SetProperty(NAME_ID("_DepthMap"), depthSRV);
                pass->SetProperty(NAME_ID("_RWScreenProbeSceneDepth"), mHybridFGCommon.mScreenProbeSceneDepth);
                pass->SetProperty(NAME_ID("_RWScreenProbeWorldNormal"), mHybridFGCommon.mScreenProbeWorldNormal);
                pass->SetProperty(NAME_ID("_RWScreenProbeWorldSpeed"), mHybridFGCommon.mScreenProbeWorldSpeed);
                pass->SetProperty(NAME_ID("_RWScreenProbeTranslatedWorldPosition"), mHybridFGCommon.mScreenProbeTranslatedWorldPosition);
                pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
            };

            // Generate Ray Debug Info
            {
                auto* pass = RED->AllocatePass("GenerateRayDebug", true);
                mHybridFGCommon.SetREDProperty(pass);
                passSetupCommon(pass);
                pass->SetProperty(NAME_ID("PROBE_INTERPOLATION_WITH_NORMAL"), mSetting.mProbeInterpolationWithNormal);
                pass->SetProperty(NAME_ID("StructuredImportanceSampledRayInfosForTracing"), rayInfosForTracing);
                pass->SetProperty(NAME_ID("TraceRadiance"), mHybridFGCommon.mTraceRadiance);
                REDTextureView* TraceHitSRV = RED->AllocateTextureView(
                    mHybridFGCommon.mTraceHit->mTexture,
                    NGITextureViewDesc{NGITextureUsage::ShaderResource, mHybridFGCommon.mTraceHit->mTexture->mDesc.Format, mHybridFGCommon.mTraceHit->GetDesc().ViewDimension, mHybridFGCommon.mTraceHit->GetDesc().SubRange});
                pass->SetProperty(NAME_ID("TraceHit"), TraceHitSRV);
                pass->SetProperty(NAME_ID("DebugRayInfoOutput"), redBufferView);
                pass->SetProperty(NAME_ID("RayScreenPosition"), UInt2(static_cast<UInt32>(mSetting.mDebugRayPosition.x * input_depthView->GetWidth()), static_cast<UInt32>(mSetting.mDebugRayPosition.y * input_depthView->GetHeight())));
                pass->Dispatch(HybridFinalGatherRayDebugShader, "GenerateRayDebugInfo", 1, 1, 1);
            }

            auto frameCount = EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            UInt32 currentWrite = frameCount % RAY_DEBUG_MAX_FLYING_FRAME;
            UInt32 currentRead = frameCount >= RAY_DEBUG_MAX_FLYING_FRAME ? (frameCount - RAY_DEBUG_MAX_FLYING_FRAME + 1) % RAY_DEBUG_MAX_FLYING_FRAME : 0;

            // Read back to Feedback buffer.
            {
                auto stagingBufferRED = RED->AllocateBuffer("FeedbackbufferCopy", mRayDebugFeedbackBuffer[currentWrite]);
                auto* vtFeedbackBufferCopyPass = RED->AllocatePass("VirtualTextureFeedbackCopy");
                const NGICopyBuffer region{
                    0,
                    0,
                    bufferSize,
                };
                vtFeedbackBufferCopyPass->CopyBufferToBuffer(stagingBufferRED, redBuffer, 1, &region);
                stagingBufferRED->SetExternalState(NGIResourceState::CopyDst);
            }

            // Draw Primitive next frame.
            {
                if (mRayDebugCurrentFreezeState != mSetting.mEnableDebugRayFreeze)
                {
                    mRayDebugCurrentFreezeState = mSetting.mEnableDebugRayFreeze;
                    if (mRayDebugCurrentFreezeState)
                    {
                        float* data = static_cast<float*>(mRayDebugFeedbackBuffer[currentRead]->MapRange(NGIBufferUsage::CopyDst, 0, bufferSize));
                        memcpy(mRayDebugFreezeDatas, data, bufferSize);
                    }
                }
                float* data = nullptr;
                if (mRayDebugCurrentFreezeState)
                {
                    data = mRayDebugFreezeDatas;
                }
                else
                {
                    data = static_cast<float*>(mRayDebugFeedbackBuffer[currentRead]->MapRange(NGIBufferUsage::CopyDst, 0, bufferSize));
                }
                {
                    UInt32 count = static_cast<UInt32>(std::pow(mHybridFGCommon.mScreenProbeTracingOctahedronResolution, 2));
                    std::vector<Float3> starts;
                    std::vector<Float3> ends;
                    std::vector<Float2> traceCoords;
                    std::vector<Float3> colors;
                    starts.resize(count);
                    ends.resize(count);
                    colors.resize(count);
                    traceCoords.resize(count);
                    for (UInt32 i = 0; i < count; i++)
                    {
                        starts[i] = Float3(data[0], data[1], data[2]);
                    }
                    UInt32 debugInfoStrip = 4*3;
                    static int preDebugRayIndex = -1;
                    for (UInt32 i = 0; i < count; i++)
                    {
                        ends[i] = Float3(data[4 + i * debugInfoStrip + 0], data[4 + i * debugInfoStrip + 1], data[4 + i * debugInfoStrip + 2]);
                        float distance = data[4 + i * debugInfoStrip + 3];
                        Float3 color = Float3(data[4 + i * debugInfoStrip + 4], data[4 + i * debugInfoStrip + 5], data[4 + i * debugInfoStrip + 6]);
                        colors[i] = color;
                        bool isSkyTrace = std::abs(mSetting.mMaxVoxelTraceDistance - distance) < 1 || std::abs(mSetting.mMaxScreenTraceDistance - distance) < 1;
                        if (isSkyTrace)
                        {
                            //ends[i] = starts[i];
                        }
                        traceCoords[i] = Float2(data[4 + i * debugInfoStrip + 8], data[4 + i * debugInfoStrip + 9]);
                        if (static_cast<UInt32>(mSetting.mDebugRayIndex) == i)
                        {
                            if (preDebugRayIndex != mSetting.mDebugRayIndex)
                            {
                                LOG_ERROR("DebugRayIndex:{}, traceCoords:({}, {}), color:({}, {}, {}), distance:{}, isSkyTrace:{}, startPos=({}, {}, {}), endPos=({}, {}, {})",
                                          mSetting.mDebugRayIndex,
                                          traceCoords[i].x,
                                          traceCoords[i].y,
                                          color.x,
                                          color.y,
                                          color.z,
                                          distance,
                                          isSkyTrace,
                                          starts[i].x,
                                          starts[i].y,
                                          starts[i].z,
                                          ends[i].x,
                                          ends[i].y,
                                          ends[i].z);
                                preDebugRayIndex = mSetting.mDebugRayIndex;
                            }
                            colors[i] = Float3(0.0, 1.0, 0.0);
                        }
                    }
                    PrimitiveGenerator::GenerateRays(&primitive[currentRead], starts.data(), ends.data(), colors.data(), count);
                    // Set TilePosition info
                    primitive[currentRead].SetTilePosition(gameContext.mRenderCamera->GetTilePosition());

                    auto primitiveSystem = gameContext.mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();
                    MaterialR* material = primitiveSystem->GetBeforeToneMappingPureVertexMaterial();
                    primitiveSystem->BatchPrimitive(&primitive[currentRead], material, false, true);
                }
            }
        }
    }

    void HybridGIPass::SetupSkyLightingGameContext(const GameContext& gameContext, REDPass* pass)
    {
        if (mSetting.IsEnableVoxelGI(gameContext))
        {
            QUICK_SCOPED_CPU_TIMING("AssembleScreenProbeTraceVoxels_ForSkyLighting");
            auto red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
            mVoxelRenderer->AssembleScreenProbeTraceVoxels_ForSkyLighting(gameContext, red, pass);
        }
    }

    void HybridGIPostProcessSetting::Initialize() {}

    namespace ComputeShaderUtils
    {
        UInt3 GetGroupCount(const UInt32 ThreadCount, const UInt32 GroupSize)
        {
            return UInt3(math::DivideAndRoundUp(ThreadCount, GroupSize), 1, 1);
        }

        UInt3 GetGroupCount(const UInt2 ThreadCount, const UInt32 GroupSize)
        {
            return UInt3(math::DivideAndRoundUp(ThreadCount.x, GroupSize), math::DivideAndRoundUp(ThreadCount.y, GroupSize), 1);
        }

        UInt3 GetGroupCount(ComputeShaderR* shader, const NameID& kernel, UInt3 dispatchSize)
        {
            UInt3 groupSize;
            shader->GetThreadGroupSize(kernel, groupSize.x, groupSize.y, groupSize.z);

            return GetGroupCount(dispatchSize, groupSize);
        }

        void SetCameraShaderParameters(REDPass* pass, RenderCamera* camera)
        {
            // This Frame View
            pass->SetProperty(NAME_ID("ce_CameraPos"), camera->GetCameraOrigin());
            pass->SetProperty(NAME_ID("ce_CameraTilePosition"), camera->GetTilePosition<>());
            pass->SetProperty(NAME_ID("ce_View"), camera->GetViewMatrix());
            pass->SetProperty(NAME_ID("ce_Projection"), camera->GetProjMatrix());
            pass->SetProperty(NAME_ID("ce_InvView"), camera->GetInvertViewMatrix());
            pass->SetProperty(NAME_ID("ce_InvProjection"), camera->GetInvertProjMatrix());
            pass->SetProperty(NAME_ID("ce_InvViewProjMatrix"), camera->GetInvertProjMatrix() * camera->GetInvertViewMatrix());

            // Last Frame View
            const Float4x4A& LastFrameViewMatrix = camera->GetLastFrameViewMatrix();
            pass->SetProperty(NAME_ID("_LastFrame_View"), camera->GetLastFrameViewMatrix());
            pass->SetProperty(NAME_ID("_LastFrame_Projection"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreViewMatrix"), LastFrameViewMatrix);
            pass->SetProperty(NAME_ID("ce_PreProjMatrix"), camera->GetLastFrameProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvViewMatrix"), camera->GetLastFrameInvertViewMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvProjMatrix"), camera->GetLastFrameInvertProjMatrix());
            pass->SetProperty(NAME_ID("ce_PreInvViewProjMatrix"), camera->GetLastFrameInvertProjMatrix() * camera->GetLastFrameInvertViewMatrix());

            // TODO: LastFrameCameraPos is incorrect
            Float3 LastFrameCameraPos = -Float3(
                LastFrameViewMatrix.m03, LastFrameViewMatrix.m13, LastFrameViewMatrix.m23
            );
            pass->SetProperty(NAME_ID("ce_PreCameraPos"), LastFrameCameraPos);
            Float4x4A OffsetMat = Float4x4::CreateTranslation(camera->GetTilePosition() - camera->GetTilePosition<true>() * LENGTH_PER_TILE);
            Float4x4 ClipToPrevClip = static_cast<Float4x4>(camera->GetViewProjMatrix()).Inverted() * OffsetMat * camera->GetLastFrameViewProjMatrix();
            pass->SetProperty(NAME_ID("_View_ClipToPrevClip"), ClipToPrevClip);
            pass->SetProperty(NAME_ID("ce_ClipToPrevClipMat"), ClipToPrevClip);
            // Not Used now ?
            pass->SetProperty(NAME_ID("ce_ClipToPrevClipMatNoJitter"), ClipToPrevClip);
            pass->SetProperty(NAME_ID("ce_TemporalJitter"), Float2(camera->GetJitterIntensity(), camera->GetJitterIntensity()));

            // Use Reverse Z
            pass->SetProperty(NAME_ID("_View_UseReverseZ"), true);
        }

        void SetCommonTextureShaderParameters(REDPass* pass, std::optional<REDTextureView*> sceneColor, std::optional<REDTextureView*> preSceneColor,
            std::optional<std::array<REDTextureView*, 4>> gBuffer, std::optional<REDTextureView*> depthMap, std::optional<REDTextureView*> preDepthMap)
        {
            // SceneColor
            if (sceneColor.has_value())
            {
                pass->SetProperty("_SceneColor", sceneColor.value());

                UInt2 ViewSize = sceneColor.value()->GetSize();
                Float4 ViewSizeAndInvSize;
                ViewSizeAndInvSize.x = static_cast<float>(ViewSize.x);
                ViewSizeAndInvSize.y = static_cast<float>(ViewSize.y);
                ViewSizeAndInvSize.z = 1.f / static_cast<float>(ViewSize.x);
                ViewSizeAndInvSize.w = 1.f / static_cast<float>(ViewSize.y);
                pass->SetProperty("_View_SizeAndInvSize", ViewSizeAndInvSize);
            }

            // Last Frame SceneColor
            if (sceneColor.has_value())
            {
                pass->SetProperty("_PrevSceneColor", preSceneColor.value());
            }

            // GBuffer
            if (gBuffer.has_value())
            {
                pass->SetProperty(NAME_ID("_GBuffer0"), gBuffer.value()[0]);
                pass->SetProperty(NAME_ID("_GBuffer1"), gBuffer.value()[1]);
                pass->SetProperty(NAME_ID("_GBuffer2"), gBuffer.value()[2]);
                pass->SetProperty(NAME_ID("_GBuffer3"), gBuffer.value()[3]);

                UInt2 gBufferSize = gBuffer.value()[0]->GetSize();
                Float4 BufferSizeAndInvSize;
                BufferSizeAndInvSize.x = static_cast<float>(gBufferSize.x);
                BufferSizeAndInvSize.y = static_cast<float>(gBufferSize.y);
                BufferSizeAndInvSize.z = 1.f / static_cast<float>(gBufferSize.x);
                BufferSizeAndInvSize.w = 1.f / static_cast<float>(gBufferSize.y);
                pass->SetProperty(NAME_ID("_View_BufferSizeAndInvSize"), BufferSizeAndInvSize);
            }

            // Depth Map
            if (depthMap.has_value())
            {
                pass->SetProperty(NAME_ID("_DepthMap"), depthMap.value());
            }

            // Last Frame Depth Map
            if (preDepthMap.has_value())
            {
                pass->SetProperty(NAME_ID("_PrevDepthMap"), preDepthMap.value());
            }

            pass->SetProperty(NAME_ID("_View_ScreenPositionScaleBias"), Float4(0.5, -0.5, 0.5, 0.5));
        }

        void SetReprojectPassShaderParameters(REDPass* pass, RenderCamera* camera, std::optional<REDTextureView*> sceneColor, std::optional<REDTextureView*> preSceneColor,
            std::optional<std::array<REDTextureView*, 4>> gBuffer, std::optional<REDTextureView*> depthMap, std::optional<REDTextureView*> preDepthMap)
        {
            SetCameraShaderParameters(pass, camera);
            SetCommonTextureShaderParameters(pass, sceneColor, preSceneColor, gBuffer, depthMap, preDepthMap);
        }
    }
}
