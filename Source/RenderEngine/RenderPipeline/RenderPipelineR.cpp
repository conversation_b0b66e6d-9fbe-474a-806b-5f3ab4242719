#include "EnginePrefix.h"
#include "RenderPipelineR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/BuiltinShaderParamsNames.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/SkeletonSystemR.h"
#include "RenderEngine/CloudSystemR.h"
#include "RenderEngine/ShadowSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/CanvasSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"
#include "ParticleSystem/NParticleSystemGPUDriven.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/REDdebugGUI.h"

static cross::Float4x4 CalClipToPrevClipMat(const cross::Float4x4& curView, const cross::Float4x4& curProj, const cross::Float4x4& preView, const cross::Float4x4& preProj)
{
    return curProj.Inverted() * curView.Inverted() * preView * preProj;
}

static cross::Float4x4 CalClipToPrevClipMat(const cross::Float4x4& curView, const cross::Float4x4& curProj, const cross::Float3& curCameraTilePosition, const cross::Float4x4& preView, const cross::Float4x4& preProj,
                                            const cross::Float3& prevCameraTilePosition)
{
    cross::Float4x4 OffsetMat = cross::Float4x4::CreateTranslation((curCameraTilePosition - prevCameraTilePosition) * LENGTH_PER_TILE_F);
    return curProj.Inverted() * curView.Inverted() * OffsetMat * preView * preProj;
}

cross::IRenderPipeline::IRenderPipeline()
{
    VertexStreamLayout streamLayout{};
    streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float3, 0});
    streamLayout.AddVertexChannelLayout({VertexChannel::TexCoord0, VertexFormat::Float2, sizeof(float) * 3});
    mPostProcessInputLayoutDesc.AddVertexStreamLayout(streamLayout);
    mUIMergeInputLayoutDesc.AddVertexStreamLayout(streamLayout);

    constexpr static std::array<GraphicsFormat, 2> gAlternativeDepthStencilFormats{
        GraphicsFormat::D32_SFloat_S8_UInt,
        GraphicsFormat::D24_UNorm_S8_UInt,
    };

    DispatchRenderingCommandWithToken([=] {
        auto ret = std::find_if(
            gAlternativeDepthStencilFormats.begin(), gAlternativeDepthStencilFormats.end(), [](GraphicsFormat format) { return EnumHasAnyFlags(GetNGIDevice().GetFormatCapability(format).TextureCapability, NGITextureUsage::DepthStencil); });

        if (ret != gAlternativeDepthStencilFormats.end())
        {
            mDepthStencilFormat = *ret;
        }
        else
        {
            LOG_FATAL("No suitable depth stencil format");
        }
    });
}

cross::IRenderPipeline::~IRenderPipeline()
{
    EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->CancelReadBack(mReadBackSession);
}

void cross::IRenderPipeline::PostProcess(RenderingExecutionDescriptor* red, std::vector<REDTextureView*>& dstViews, MaterialR* material, NameID const& passID, RenderContext&& ctx, bool clearTarget)
{
    auto dstWidth = dstViews[0]->mTexture->mDesc.Width >> dstViews[0]->mDesc.SubRange.MostDetailedMip;
    auto dstHeight = dstViews[0]->mTexture->mDesc.Height >> dstViews[0]->mDesc.SubRange.MostDetailedMip;

    auto dstCount = dstViews.size();
    NGIClearValue clearValue{{0, 0, 0, 0}};
    std::vector<REDColorTargetDesc> renderTargetDescs(dstCount);
    std::vector<NGIRenderPassTargetIndex> colorTargetIndexs(dstCount);
    for (int index = 0; index < dstCount; index++)
    {
        renderTargetDescs[index] = {
            dstViews[index],
            clearTarget ? NGILoadOp::Clear : NGILoadOp::Load,
            NGIStoreOp::Store,
            clearValue,
        };
        colorTargetIndexs[index] = static_cast<NGIRenderPassTargetIndex>(index);
    }

    auto* rendererSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    red->BeginRenderPass(passID.GetName(), static_cast<UInt32>(dstCount), renderTargetDescs.data(), nullptr);

    NGIViewport viewport{0, 0, static_cast<float>(dstWidth), static_cast<float>(dstHeight), 0, 1};
    NGIScissor scissor{0, 0, dstWidth, dstHeight};

    // auto& programDesc = material->GetProgram(passID);
    auto mtlState = material->GetMaterialRenderState(passID);
    // Assert(programDesc.PipelineLayout);

    auto* postSubPass = red->AllocateSubRenderPass(passID.GetName(), 0, nullptr, static_cast<UInt32>(dstCount), colorTargetIndexs.data(), REDPassFlagBit{0});
    postSubPass->Execute([=](REDPass* pass, NGIBundleCommandList* cmdList) mutable {
        cmdList->SetViewports(1, &viewport);
        cmdList->SetScissors(1, &scissor);
        NGIBuffer* vbs[]{rendererSystem->GetFullScreenTriangle()};
        cmdList->SetVertexBuffers(1, vbs, nullptr);

        NGIGraphicsPipelineStateDesc pipelineDesc
        {
            pass->GetRenderPass(),
            pass->GetSubpass(),
            mtlState.mProgram->mGUID,
            &mtlState.mProgram->mGraphicsProgramDesc,
            mtlState.mProgram->mPipelineLayout,

            mPostProcessInputLayoutDesc.GetHash().GetHash(),
            &mPostProcessInputLayoutDesc,

            PrimitiveTopology::TriangleList,
            *mtlState.mRaterizationState,
            *mtlState.mBlendState,
            *mtlState.mDepthStencilState,
            mtlState.mProgram->mShaderConstantLayout ? mtlState.mProgram->mShaderConstantLayout->ByteSize : 0,
            mtlState.mShaderConstants,
        };

        auto* pipeline = rendererSystem->GetPipelineStatePool()->AllocateGraphicsPipelineState(pipelineDesc);
        cmdList->SetGraphicsPipelineState(pipeline);

        auto* pg = pass->GetContext().GetPassResourceGroup(mtlState.mProgram->mResourceGroupLayouts[ShaderParamGroup_Pass], mtlState.mProgram->mPipelineLayout, ShaderParamGroup_Pass);
        if (pg)
        {
            cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, pg);
        }

        auto* mg = mtlState.GetResourceBinding();
        if (mg)
        {
            cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Material, mg);
        }
        cmdList->DrawInstanced(3, 1, 0, 0);
    });

    postSubPass->SetRenderContext(std::move(ctx));

    postSubPass->AddPassResourceReferences(mtlState.mProgram->mPipelineLayout);

    red->EndRenderPass();
}

cross::REDTextureView* cross::IRenderPipeline::CreateTextureView2D(std::string_view name, UInt2 size, GraphicsFormat format, NGITextureUsage usage, UInt16 mipCount, UInt16 sampleCount)
{
    return CreateTextureView2D(name, size.x, size.y, format, usage, mipCount, sampleCount);
}

cross::REDTextureView* cross::IRenderPipeline::CreateTextureView2D(std::string_view name, UInt32 width, UInt32 height, GraphicsFormat format, NGITextureUsage usage, UInt16 mipCount, UInt16 sampleCount, bool mutableFormat)
{
    return CreateTextureView2D(name, width, height, format, usage, usage, mipCount, sampleCount, mutableFormat);
}


cross::REDTextureView* cross::IRenderPipeline::CreateTextureView2D(std::string_view name, UInt32 width, UInt32 height, GraphicsFormat format, NGITextureUsage usage, NGITextureUsage viewUsage,
    UInt16 mipCount, UInt16 sampleCount, bool mutableFormat)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* renderGraphContext = rendererSystem->GetRenderingExecutionDescriptor();

    auto texture = renderGraphContext->AllocateTexture(name, NGITextureDesc{ format, NGITextureType::Texture2D, mipCount, sampleCount, width, height, 1, 1, usage, mutableFormat});

    return CreateTextureView2D(texture, format, viewUsage);
}


cross::REDTextureView* cross::IRenderPipeline::CreateTextureView3D(std::string_view name, UInt32 width, UInt32 height, UInt32 depth, GraphicsFormat format, NGITextureUsage usage, UInt16 mipCount, UInt16 sampleCount)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* renderGraphContext = rendererSystem->GetRenderingExecutionDescriptor();

    auto texture = renderGraphContext->AllocateTexture(name, NGITextureDesc{format, NGITextureType::Texture3D, mipCount, sampleCount, width, height, static_cast<UInt16>(depth), 1, usage});

    NGITextureAspect aspect{0};
    if (auto [hasDepth, hasStencil] = FormatHasDepthStencil(format); hasDepth || hasStencil)
    {
        if (hasDepth)
            aspect |= NGITextureAspect::Depth;
        if (hasStencil)
            aspect |= NGITextureAspect::Stencil;
    }
    else
    {
        aspect = NGITextureAspect::Color;
    }

    auto* textureView = renderGraphContext->AllocateTextureView(texture,
                                                                NGITextureViewDesc{usage,
                                                                                   format,
                                                                                   NGITextureType::Texture3D,
                                                                                   {
                                                                                       aspect,
                                                                                       0,
                                                                                       1,
                                                                                       0,
                                                                                       1,
                                                                                   }});
    return textureView;
}

cross::REDTextureView* cross::IRenderPipeline::CreateTextureView2D(REDTextureRef texture, GraphicsFormat format, NGITextureUsage usage)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* renderGraphContext = rendererSystem->GetRenderingExecutionDescriptor();
    NGITextureAspect aspect{0};
    if (auto [hasDepth, hasStencil] = FormatHasDepthStencil(format); hasDepth || hasStencil)
    {
        if (hasDepth)
            aspect |= NGITextureAspect::Depth;
        if (hasStencil)
            aspect |= NGITextureAspect::Stencil;
    }
    else
    {
        aspect = NGITextureAspect::Color;
    }
    auto* textureView = renderGraphContext->AllocateTextureView(texture,
                                                                NGITextureViewDesc{usage,
                                                                                   format,
                                                                                   NGITextureType::Texture2D,
                                                                                   {
                                                                                       aspect,
                                                                                       0,
                                                                                       1,
                                                                                       0,
                                                                                       1,
                                                                                   }});
    return textureView;
}

cross::REDTextureView* cross::IRenderPipeline::CreateTextureView2D(REDTextureView* tetxureView, NGITextureUsage usage)
{
    return CreateTextureView2D(tetxureView->GetTexture(), tetxureView->GetDesc().Format, usage);
}

void cross::IRenderPipeline::UpdateContext(RenderContext& context, RenderWorld* world, MaterialR* mtl, bool UIPass)
{
    // For GPU_SKIN
    const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
    if (settingMgr->GetGPUSkinEnable())
    {
        // todo xtnwang-2-13
        // auto* skeletonSys = TYPE_CAST(SkeletonSystemR*, world->GetRenderSystem<SkeletonSystemR>());
        // context.SetProperty(NAME_ID("matrix_texture"), skeletonSys->GetBoneTextureView());
    }

    const RenderCamera* camera = GetRenderCamera();
    auto viewMatrix = camera->GetViewMatrix();
    auto invViewMatrix = camera->GetInvertViewMatrix();
    Float4x4 preProjMatrix;
    preProjMatrix = camera->GetLastFrameProjMatrix();
    Float4x4 projectionMatrix;
    Float4x4 invProjectionMatrix;
    // the ui part is not in involved in taa, so close it
    if ((mUpscale || mTAA) && !UIPass && mType != ViewType::PrefabProxy)
    {
        projectionMatrix = mJitterData.jitterProjMat;
        preProjMatrix = mJitterData.lastProjMat;   // no jitter proj matrix for cancel jitterd motion vector
        invProjectionMatrix = mJitterData.jitterProjMat.Inverted();
    }
    else
    {
        projectionMatrix = camera->GetProjMatrix();
        invProjectionMatrix = camera->GetInvertProjMatrix();
    }
    Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);

    Float4 skyLight{0.6f, 0.6f, 0.8f, 1.0f};
    Float4 gndLight{0.5f, 0.5f, 0.4f, 1.0f};
    Float4 skyDir{0, 1.0f, 0, 1.0f};

    if (mtl)
    {
        if (auto val = mtl->GetVector("sky_light"); val)
        {
            memcpy(skyLight.data(), &val.value(), sizeof(Float4));
        }

        if (auto val = mtl->GetVector("gnd_light"); val)
        {
            memcpy(gndLight.data(), &val.value(), sizeof(Float4));
        }

        if (auto val = mtl->GetVector("sky_dir"); val)
        {
            memcpy(skyDir.data(), &val.value(), sizeof(Float4));
        }
    }
    context.SetProperty(BuiltInProperty::ce_View, viewMatrix);
    context.SetProperty(BuiltInProperty::ce_Projection, projectionMatrix);
    context.SetProperty(BuiltInProperty::ce_CameraPos, cameraPos);
#if defined(CE_USE_DOUBLE_TRANSFORM)
    context.SetProperty(BuiltInProperty::ce_CameraTilePosition, camera->GetTilePosition<false>());
    context.SetProperty(BuiltInProperty::ce_PrevCameraTilePosition, camera->GetTilePosition<true>());
#else
    mRED->SetProperty(BuiltInProperty::ce_ClipToPrevClipMat, CalClipToPrevClipMat(viewMatrix, projMatrix, camera->GetLastFrameViewMatrix(), preProjMatrix));
#endif
    context.SetProperty(BuiltInProperty::ce_AmbientGroundLight, gndLight);
    context.SetProperty(BuiltInProperty::ce_AmbientSkyLight, skyLight);
    context.SetProperty(BuiltInProperty::ce_AmbientSkyDirection, skyDir);
    context.SetProperty(BuiltInProperty::ce_InvView, viewMatrix.Inverted());
    context.SetProperty(BuiltInProperty::ce_InvProjection, invProjectionMatrix);
}

void cross::IRenderPipeline::UpdateLightContext()
{
    UpdateLightsInfoBuffer();
}

void cross::IRenderPipeline::UpdateLightsInfoBuffer()
{
    const auto* lightSystem = mWorld->GetRenderSystem<LightSystemR>();
    const auto* shadowSys = mWorld->GetRenderSystem<ShadowSystemR>();
    auto* atmoSys = mWorld->GetRenderSystem<SkyAtmosphereSystemR>();
    auto* skyLightSys = mWorld->GetRenderSystem<SkyLightSystemR>();
    auto camTile = GetRenderCamera()->GetTilePosition();

    auto& lights = GetLightList();
    auto& lightIndices = GetLightIndexList();
    int lightSize = static_cast<int>(lightIndices.size());

    std::vector<LightInfo> lightsData(lightSize);
    std::vector<Float4> lightOutSpaceColorsData(lightSize);
    std::array<Float4, 3> envSH;
    std::vector<UInt32> directionalLightsId;

    memset(envSH.data(), 0, sizeof(envSH));
    constexpr float POINT_LIGHT = -1.0f;
    constexpr float RECT_LIGHT = -2.0f;
    constexpr float SKY_LIGHT = 1.f;
    for (int i = 0; i < lightSize; i++)
    {
        auto lightIdx = lightIndices[i];
        auto lightEntity = lightSystem->GetLightEntityID(lightIdx);
        auto lightTrans = lightSystem->GetLightTransform(lightIdx);

        auto shadowComp = mWorld->GetComponent<ShadowCameraComponentR>(lightEntity);
        Float3 color = lightSystem->GetLightColor(lightIdx);
        color *= lightSystem->GetLightIntensity(lightIdx);
        Float3 colorOutSpace = lightSystem->GetLightOutSpaceColor(lightIdx);

        lightsData[i].LightColor = Float4(color.x, color.y, color.z, POINT_LIGHT);
        lightOutSpaceColorsData[i] = Float4(colorOutSpace.x, colorOutSpace.y, colorOutSpace.z, 1.0f);
        // lightsData[i].LightShadowDataIndice = lightSystem->GetLightShadowDataIndex(lightComp.Read());

        float prtIntensity = lightSystem->GetLightPrtIntensity(lightIdx);

        auto light_data = lightSystem->GetLightRenderData(lightIdx);


        lightsData[i].SpecularLightIntensity =  light_data->mSpecularIntensity;

        switch (lightSystem->GetLightType(lightIdx))
        {
        case LightType::Directional:
        {
            lightsData[i].LightDirPos = lightTrans.rotation.Float4Rotate(Float4(0, 0, 1, 0));
            lightsData[i].LightDirPos.w = 0.0f;
            lightsData[i].LightTilePos = Float4(camTile, 0.f);
            lightsData[i].LightAttenuation = Float4{0.0f, 0.0f, 0.00000001f, 100000000.0f};

            float sourceAngleInDeg = lightSystem->GetLightSourceAngleOrRadius(lightIdx);
            float sourceRadius = 1.f * sin(Deg2Rad(sourceAngleInDeg * .5f));
            lightsData[i].SourceRadius = sourceRadius;
            float softSourceAngleInDeg = lightSystem->GetLightSoftSourceAngleOrRadius(lightIdx);
            float softSourceRadius = 1.f * sin(Deg2Rad(softSourceAngleInDeg * .5f));
            lightsData[i].SoftSourceRadius = softSourceRadius;
            
            if (lightSystem->GetEnableTransmittance(lightIdx))
            {

                Float3 trans = atmoSys->GetTransmittanceTowardsSunAtRenderCamera(-lightsData[i].LightDirPos.XYZ(), GetRenderCamera());
                lightsData[i].LightColor *= Float4(trans, 1);
            }
            directionalLightsId.emplace_back(static_cast<UInt32>(i));
            cross::SHUtils::SHEvalDirectionalLight(2, Float3(lightsData[i].LightDirPos.x, -lightsData[i].LightDirPos.z, lightsData[i].LightDirPos.y) * -1.f, color * prtIntensity, &envSH[0].x, &envSH[1].x, &envSH[2].x);

            auto* virtualShadowMapClipmap = shadowSys->GetVirtualShadowMapClipmap(shadowComp.Read(), mCamera);
            if (virtualShadowMapClipmap != nullptr)
            {
                lightsData[i].VirtualShadowMapId = virtualShadowMapClipmap->GetShadowMapId();
            }
            break;
        }
        /*
         * NOTE: CE default local light photometric uint is cd(lm / sr)
         */
        case LightType::Point:
        {
            auto range = lightSystem->GetLightRange(lightIdx);
            auto range2 = range * range;
            // it's not reasonable to transfer light position to camera tile here, the same task is done in shader.
            lightsData[i].LightDirPos = Float4(lightTrans.translate, std::max(1.f, range));   
            lightsData[i].LightTilePos = Float4(lightTrans.tilePos, 0.f);
            lightsData[i].LightAttenuation = Float4(-1.f, 1.f, 1.0f / range2, range2);
            lightsData[i].SourceRadius = lightSystem->GetLightSourceAngleOrRadius(lightIdx);
            lightsData[i].SoftSourceRadius = lightSystem->GetLightSoftSourceAngleOrRadius(lightIdx);
            
            if (mSetting->LocalLightInverseSquared)
            {
                lightsData[i].LightColor = {color * 100.f * 100.f, POINT_LIGHT};  // NOTE: cd -> cd * m^2 / cm^2, since ce's length uint is cm
            }
            break;
        }
        case LightType::Spot:
        {
            auto range = lightSystem->GetLightRange(lightIdx);
            auto range2 = range * range;
            auto spotInnerCosAngle = lightSystem->GetLightInnerCosAngle(lightIdx);
            auto spotOuterCosAngle = lightSystem->GetLightOuterCosAngle(lightIdx);
            const float InvCosConeDifference = 1.0f / (spotInnerCosAngle - spotOuterCosAngle);
            auto fadeIntensity = lightSystem->GetLightConeFadeIntensity(lightIdx);
            auto overflowLength = lightSystem->GetLightConeOverFlowLength(lightIdx);
            auto spotDistanceExp = lightSystem->GetLightSpotDistanceExp(lightIdx);

            auto spotRotEuler = QuaternionA::QuaternionToEuler(lightTrans.rotation);

            //it's not reasonable to transfer light position to camera tile here, the same task is done in shader.
            lightsData[i].LightDirPos = Float4(lightTrans.translate, range);
            lightsData[i].LightTilePos = Float4(lightTrans.tilePos, overflowLength);
            lightsData[i].LightSpotDirection = Float4(spotRotEuler, fadeIntensity);
            lightsData[i].LightAttenuation = Float4(spotOuterCosAngle, InvCosConeDifference, 1.0f/range2, range2);
            lightsData[i].SourceRadius = lightSystem->GetLightSourceAngleOrRadius(lightIdx);
            lightsData[i].SoftSourceRadius = lightSystem->GetLightSoftSourceAngleOrRadius(lightIdx);
            
            if (mSetting->LocalLightInverseSquared)
            {
                lightsData[i].LightColor = {color * 100.f * 100.f, spotDistanceExp};  // NOTE: cd -> cd * m^2 / cm^2, since ce's length uint is cm
            }
            else
            {
                lightsData[i].LightColor = {color, spotDistanceExp};
            }
            break;
        }
        case LightType::Rect:
        {
            float range = lightSystem->GetLightRange(lightIdx);
            float rectSourceWidth = lightSystem->GetLightSourceWidth(lightIdx);
            float rectSourceHeight = lightSystem->GetLightSourceHeight(lightIdx);
            float rectBarnDoorAngle = lightSystem->GetLightBarnDoorAngle(lightIdx);
            float rectBarnDoorLength = lightSystem->GetLightBarnDoorLength(lightIdx);
            Float3 rectRotEuler = QuaternionA::QuaternionToEuler(lightTrans.rotation);
            lightsData[i].SourceRadius = rectSourceWidth * .5f;
            lightsData[i].SoftSourceRadius = 0.f;

            lightsData[i].LightDirPos = Float4(lightTrans.translate, std::max(1.f, range));
            lightsData[i].LightTilePos = Float4(lightTrans.tilePos, 0.f);
            lightsData[i].LightSpotDirection = Float4(rectRotEuler, 0.0f);
            lightsData[i].LightAttenuation = Float4(rectSourceWidth/2.0f, rectSourceHeight/2.0f, Cos(MathUtils::ConvertToRadians(rectBarnDoorAngle)), rectBarnDoorLength);
            // reference: UE 5.5.4 FRectLightSceneProxy::GetLightShaderParameters
            lightsData[i].LightColor = {color * 100.f * 100.f / (.5f * rectSourceWidth * rectSourceHeight), RECT_LIGHT};
            
            break;
        }
        default:
            break;
        }
    }   // LightLoop

    if (skyLightSys->HasSkyLight())
    {
        LightInfo skyLight = lightsData.emplace_back();
        memset(&skyLight, 0, sizeof(LightInfo));
        skyLight.LightDirPos = Float4(0.f, 0.f, 0.f, SKY_LIGHT);
    }

    auto* rendererSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    {
        UInt32 dataByteSize = sizeof(LightInfo) * lightSize;
        UInt32 dataBufferSize = std::max(dataByteSize, 1u);
        auto* scratchBuffer = rendererSys->GetScratchBuffer();
        mLightsBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataBufferSize);
        if (lightSize)
        {
            mLightsBufferWrap.MemWrite(0, lightsData.data(), dataByteSize);
        }

        mLightsBufferView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                mLightsBufferWrap.GetNGIOffset(),
                dataBufferSize,
                GraphicsFormat::Unknown,
                sizeof(LightInfo),
            },
            mLightsBufferWrap.GetNGIBuffer());

        mRED->SetProperty(NAME_ID("ce_Lights"), mLightsBufferView);
    }

    {
        UInt32 dataByteSize = sizeof(Float4) * lightSize;
        UInt32 dataBufferSize = std::max(dataByteSize, 1u);
        auto* scratchBuffer = rendererSys->GetScratchBuffer();
        auto dataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataBufferSize);
        dataBufferWrap.MemWrite(0, lightOutSpaceColorsData.data(), dataByteSize);

        mLightOutSpaceColorBufferView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                dataBufferWrap.GetNGIOffset(),
                dataBufferSize,
                GraphicsFormat::Unknown,
                sizeof(Float4),
            },
            dataBufferWrap.GetNGIBuffer());

        mRED->SetProperty(NAME_ID("ce_LightOutSpaceColorsBuffer"), mLightOutSpaceColorBufferView);
    }

    if (directionalLightsId.size())
    {
        SizeType dataByteSize = sizeof(UInt32) * directionalLightsId.size();
        auto* scratchBuffer = rendererSys->GetScratchBuffer();
        auto dataBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::StructuredBuffer, dataByteSize);
        dataBufferWrap.MemWrite(0, directionalLightsId.data(), dataByteSize);

        auto* mDirectionalLightsIdBufferView = rendererSys->GetTransientResourceManager()->AllocateBufferView(
            NGIBufferViewDesc{
                NGIBufferUsage::StructuredBuffer,
                dataBufferWrap.GetNGIOffset(),
                dataByteSize,
                GraphicsFormat::Unknown,
                sizeof(UInt32),
            },
            dataBufferWrap.GetNGIBuffer());

        mRED->SetProperty(NAME_ID("ce_DirectionalLightsId"), mDirectionalLightsIdBufferView);
    }
    
    skyLightSys->AddSkyDiffuseSH(envSH);

    ////
    // mRED->SetProperty(BuiltInProperty::ce_LightOutSpaceColors, lightOutSpaceColorsData.data(), lightOutSpaceColorsData.size() * sizeof(Float4));
    mRED->SetProperty(BuiltInProperty::ce_GlobalEnvSH, envSH.data(), envSH.size() * sizeof(Float4));
    mRED->SetProperty(NAME_ID("_NumVisibleLights"), static_cast<UInt32>(lightSize));
    mRED->SetProperty(NAME_ID("_NumDirectionalLights"), static_cast<UInt32>(directionalLightsId.size()));
}

void cross::IRenderPipeline::WaitAndPrepareVisibleLightList()
{
    mLightList = mVisibleLightCullingResult->GetLightList();
    mLightIndexList = mVisibleLightCullingResult->GetLightIndexList();
    mLightScreenRatio = mVisibleLightCullingResult->GetLightScreenRatio();
    mLightViewBound = mVisibleLightCullingResult->GetlightViewBound();
}

void cross::IRenderPipeline::UpdateLightShadowDataIndices()
{
    auto* lightSystem = TYPE_CAST(const LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());

    auto lightIndices = GetLightIndexList();
    int lightSize = static_cast<int>(lightIndices.size());
    for (int i = 0; i < lightSize; i++)
    {
        auto lightIdx = lightIndices[i];
        int lightShadowDataIndice = lightSystem->GetLightShadowDataIndex(lightIdx);
        size_t bufferOffset = sizeof(LightInfo) * i + offsetof(LightInfo, LightShadowDataIndice);
        mLightsBufferWrap.MemWrite(bufferOffset, &lightShadowDataIndice, sizeof(lightShadowDataIndice));
    }
    //if (mLightsBufferView)
    //{
    //    mRED->SetProperty(NAME_ID("ce_Lights"), mLightsBufferView);
    //}
}

void cross::IRenderPipeline::RegisterCustomPass(GeneralPass pass, bool bAfterFSR2)
{
    if (mExtendPasses.find(HashString(pass.GetPassName().data())) == mExtendPasses.end())
    {
        mExtendPasses.emplace(pass.GetPassName().data(), pass);
        if (bAfterFSR2)
            mPassRefsAfterFSR2.emplace_back(&mExtendPasses.find(pass.GetPassName().data())->second);
        else
            mPassRefs.emplace_back(&mExtendPasses.find(pass.GetPassName().data())->second);
    }
}

void cross::IRenderPipeline::UpdateCloudShadowContext(REDPass* pass) const
{

}




UInt32 cross::IRenderPipeline::GetFrameCount() const
{
    return mWorldRenderPipeline->GetCurrentFrameParam()->GetFrameCount();
}


void cross::IRenderPipeline::UpdateTargetView(REDTextureView* mTarget)
{
    auto RTWidth = mTarget->mTexture->mDesc.Width;
    auto RTHeight = mTarget->mTexture->mDesc.Height;

    Float4 screenSizeVector = {static_cast<float>(RTWidth), static_cast<float>(RTHeight), 1.0f / RTWidth, 1.0f / RTHeight};
    mRED->SetProperty(BuiltInProperty::ce_ScreenParams, screenSizeVector);
}

void cross::IRenderPipeline::UpdateCameraContext(RenderCamera* camera) const
{
    auto& cameraView = camera->GetCameraView();

    auto invViewMatrix = camera->GetInvertViewMatrix();

    cameraView.mRelativeOrigin = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);
    cameraView.mForward = Float3(-invViewMatrix.m20, -invViewMatrix.m21, -invViewMatrix.m22);
    cameraView.mRight = Float3(invViewMatrix.m00, invViewMatrix.m01, invViewMatrix.m02);
    cameraView.mUp = Float3(invViewMatrix.m10, invViewMatrix.m11, invViewMatrix.m12);
    cameraView.mTemporalJitter = Float4A::Zero();

    if ((mUpscale || mTAA) && mType != ViewType::ReflectionProbe && mType != ViewType::PrefabProxy)
    {
        cameraView.mProjMatrixJitter = mJitterData.jitterProjMat;
        cameraView.mInvertProjMatrixJitter = cameraView.mProjMatrixJitter.Inverted();
        cameraView.mTemporalJitter = {mJitterData.jitterInProj.x, mJitterData.jitterInProj.y, mJitterData.lastJitterInProj.x, mJitterData.lastJitterInProj.y};
    }
    else
    {
        cameraView.mProjMatrixJitter = camera->GetProjMatrix();
        cameraView.mInvertProjMatrixJitter = camera->GetInvertProjMatrix();
    }

    cameraView.mViewProjMatrixJitter = camera->GetViewMatrix() * cameraView.mProjMatrixJitter;
    cameraView.mClipToPrevClipMatrix = camera->GetReprojectionClipToPrevClip();
#if defined(CE_USE_DOUBLE_TRANSFORM)
    cameraView.mClipToPrevClipMatrixJitter = CalClipToPrevClipMat(
        camera->GetViewMatrix(), camera->GetProjMatrixJitter(), camera->GetTilePosition(), camera->GetViewMatrix<true>(), camera->GetProjMatrixJitter<true>(), camera->GetTilePosition<true>());
#else
    cameraView.mClipToPrevClipMatrixJitter = CalClipToPrevClipMat(camera->GetViewMatrix(), camera->GetProjMatrixJitter(), camera->GetViewMatrix<true>(), camera->GetProjMatrixJitter<true>());
#endif
    cameraView.mInvertViewProjMatrixJitter = cameraView.mInvertProjMatrixJitter * camera->GetInvertViewMatrix();

    mRED->SetProperty(BuiltInProperty::ce_View, camera->GetViewMatrix());
    mRED->SetProperty(BuiltInProperty::ce_Projection, camera->GetProjMatrixJitter());
    mRED->SetProperty(BuiltInProperty::ce_ProjectionNoJitter, camera->GetProjMatrix());
    mRED->SetProperty(BuiltInProperty::ce_CameraPos, camera->GetCameraOrigin());
    mRED->SetProperty(BuiltInProperty::ce_PreCameraPos, camera->GetCameraOrigin<true>());
    mRED->SetProperty(BuiltInProperty::ce_CameraForward, camera->GetForward());
#if defined(CE_USE_DOUBLE_TRANSFORM)
    mRED->SetProperty(BuiltInProperty::ce_CameraTilePosition, camera->GetTilePosition());
    mRED->SetProperty(BuiltInProperty::ce_PrevCameraTilePosition, camera->GetTilePosition<true>());
#endif
    mRED->SetProperty(BuiltInProperty::ce_ClipToPrevClipMat, camera->GetClipToPrevClipMatrixJitter());
    mRED->SetProperty(BuiltInProperty::ce_ClipToPrevClipMatNoJitter, camera->GetClipToPrevClipMatrix());
    mRED->SetProperty(BuiltInProperty::ce_InvView, camera->GetInvertViewMatrix());
    mRED->SetProperty(BuiltInProperty::ce_InvProjection, camera->GetInvertProjMatrixJitter());
    mRED->SetProperty(BuiltInProperty::ce_InvViewProjMatrix, camera->GetInvertViewProjMatrixJitter());
    mRED->SetProperty(BuiltInProperty::ce_PreViewMatrix, camera->GetViewMatrix<true>());
    mRED->SetProperty(BuiltInProperty::ce_PreProjMatrix, camera->GetProjMatrix<true>());    // no jitter proj matrix for cancel jittered motion vector
    mRED->SetProperty(BuiltInProperty::ce_PreProjMatrixJitter, camera->GetProjMatrixJitter<true>());
    mRED->SetProperty(BuiltInProperty::ce_TemporalJitter, camera->GetTemporalJitter());
}

void cross::IRenderPipeline::AssembleOutlinePass(RenderWorld* world, REDTextureView* colorView)
{
    auto camera = GetRenderCamera();
    if (camera->GetIsRenderToTargetCamera())
    {
        return;
    }

    AssembleOutlinePassInternal(world, colorView);
}

void cross::IRenderPipeline::GenerateJitterData(const RenderCamera* renderCamera, const Float2& targetViewSize, const float upscaleValue)
{
    float jitterIntensity = renderCamera->GetJitterIntensity();

    mJitterData.lastJitter = mJitterData.jitter;
    mJitterData.lastJitterInProj = mJitterData.jitterInProj;
    mJitterData.lastViewMat = mJitterData.viewMat;
    mJitterData.lastJitterProjMat = mJitterData.jitterProjMat;

    mJitterData.lastProjMat = mJitterData.lastJitterProjMat;
    mJitterData.lastProjMat.m20 -= mJitterData.jitterInProj.x;
    mJitterData.lastProjMat.m21 -= mJitterData.jitterInProj.y;

#ifdef CE_USE_DOUBLE_TRANSFORM
    mJitterData.lastTilePosition = mJitterData.tilePosition;
#endif

    static constexpr float basePhaseCount = 8.0f;
    mJitterData.mTemporalJitterSequenceLength = static_cast<UInt32>(basePhaseCount * std::max(1.0f, 1.0f * upscaleValue * upscaleValue) + 0.5f);
    mJitterData.mTemporalSampleIndex = (mJitterData.mTemporalSampleIndex + 1) % mJitterData.mTemporalJitterSequenceLength;

    Float2 offset = Float2(Random::GetHaltonValue(mJitterData.mTemporalSampleIndex + 1, 2), Random::GetHaltonValue(mJitterData.mTemporalSampleIndex + 1, 3));

    // convert to unit pixel space
    mJitterData.jitter.x = offset.x - 0.5f, mJitterData.jitter.y = offset.y - 0.5f;
    mJitterData.jitter.x *= jitterIntensity, mJitterData.jitter.y *= jitterIntensity;

    mJitterData.jitterProjMat = renderCamera->GetProjMatrix();

    // convert to projection space
    mJitterData.jitterInProj.x = 2.0f * mJitterData.jitter.x / targetViewSize.x;
    mJitterData.jitterInProj.y = -2.0f * mJitterData.jitter.y / targetViewSize.y;
    mJitterData.jitterProjMat.m20 += mJitterData.jitterInProj.x;
    mJitterData.jitterProjMat.m21 += mJitterData.jitterInProj.y;

    mJitterData.viewMat = renderCamera->GetViewMatrix();
#ifdef CE_USE_DOUBLE_TRANSFORM
    mJitterData.tilePosition = renderCamera->GetTilePosition();
#endif
}

//static bool GetRenderNodeGPUSkinEnable(const cross::SubMeshRenderNode& inRenderNode)
//{
//    if (auto* material = inRenderNode.GetMaterial(); material)
//    {
//        if (auto enable = material->GetBool("GPU_SKIN"); enable && *enable)
//        {
//            return true;
//        }
//    }
//    return false;
//}

void cross::IRenderPipeline::AssembleOutlinePassInternal(RenderWorld* world, REDTextureView* colorView)
{
    constexpr auto uiPassName = "OutLineColor";

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    auto* outline_color_view = CreateTextureView2D(uiPassName, colorView->mTexture->mDesc.Width, colorView->mTexture->mDesc.Height, GraphicsFormat::R8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    REDColorTargetDesc colorDesc{
        outline_color_view,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        NGIClearValue{{0, 0, 0, 0}},
    };

    auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(GetRenderCamera())});
    cross::REDDrawUnitList* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{uiPassName, 0, gRenderGroupUI - 1, RenderEffectTag::EditorHighlight | RenderEffectTag::Highlight, RenderNodeType{0}, mEditorEffectMtl});

    red->BeginRenderPass(uiPassName, 1, &colorDesc, nullptr);

    auto target = NGIRenderPassTargetIndex::Target0;
    auto* uiPass = red->AllocateSubRenderPass(uiPassName, 0, nullptr, 1, &target, REDPassFlagBit{0});
    UpdateContext(uiPass->GetContext(), world, nullptr, true);

    uiPass->RenderDrawUnits({drawUnitList});
    
    red->EndRenderPass();

    auto* outline_color_view_down =
        CreateTextureView2D(fmt::format("{}_down", uiPassName), colorView->mTexture->mDesc.Width, colorView->mTexture->mDesc.Height, GraphicsFormat::R8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    Float4 src_size{
        static_cast<float>(colorView->mTexture->mDesc.Width),
        static_cast<float>(colorView->mTexture->mDesc.Height),
        1.0f / colorView->mTexture->mDesc.Width,
        1.0f / colorView->mTexture->mDesc.Height,
    };

    PostProcess(
        [&](REDPass* pass) {
            pass->SetProperty("src_texture", outline_color_view);
            pass->SetProperty("src_size", src_size);
        },
        mEditorEffectMtl,
        "outline_blur_down",
        true,
        outline_color_view_down);

    PostProcess(
        [&](REDPass* pass) {
            pass->SetProperty("src_texture", outline_color_view_down);
            pass->SetProperty("ref_texture", outline_color_view);
            pass->SetProperty("src_size", src_size);
        },
        mEditorEffectMtl,
        "outline_blur_up",
        false,
        colorView);
}

void cross::IRenderPipeline::AssembleDebugGUIPass(REDTextureView* colorView, REDTextureView* depthStencilView)
{
    if (GetRenderCamera()->GetIsRenderToTargetCamera() || !IsMainCamera())
    {
        return;
    }

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();
    auto textureWidth = colorView->mTexture->mDesc.Width;
    auto textureHeight = colorView->mTexture->mDesc.Height;

    UInt2 Size = UInt2(textureWidth, textureHeight);

    switch (rendererSystem->GetREDGUIState())
    {
    case REDGUIState::DebugWindow:
        rendererSystem->GetREDdebugGUI()->OnPaint(Size);
        if (colorView)
        {
            REDColorTargetDesc desc{
                colorView,
                NGILoadOp::Load,
                NGIStoreOp::Store,
                {{0, 0, 0, 0}},
            };
            red->BeginRenderPass(gREDdebugGUIPassName, 1, &desc, nullptr);
            auto colorIndex = NGIRenderPassTargetIndex::Target0;
            auto* pass = red->AllocateSubRenderPass(gREDdebugGUIPassName, 0, nullptr, 1, &colorIndex, REDPassFlagBit{0});
            pass->AddPayload(std::make_shared<REDImGuiPayload>(colorView));
            red->EndRenderPass();
        }
        break;
    case REDGUIState::REDVisualizer:
        rendererSystem->GetRenderingExecutionDescriptor()->MarkNeedThumbnail();
        rendererSystem->GetREDVisualizer()->OnPaint(Size);
        if (colorView)
        {
            REDColorTargetDesc desc{
                colorView,
                NGILoadOp::Load,
                NGIStoreOp::Store,
                {{0, 0, 0, 0}},
            };
            red->BeginRenderPass(gREDdebugGUIPassName, 1, &desc, nullptr);
            auto colorIndex = NGIRenderPassTargetIndex::Target0;
            auto* pass = red->AllocateSubRenderPass(gREDdebugGUIPassName, 0, nullptr, 1, &colorIndex, REDPassFlagBit{0});
            pass->AddPayload(std::make_shared<REDImGuiPayload>(colorView));
            red->EndRenderPass();
        }
        break;
    default:
        break;
    }
}

void cross::IRenderPipeline::AssembleUIPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* outputView, REDTextureView* depthStencilView)
{
    if (GetRenderCamera()->GetIsRenderToTargetCamera() || !IsMainCamera())
    {
        NGICopyTexture region{ 0, {}, 0, {}, {colorView->GetWidth(), colorView->GetHeight(), 1}};
        mRED->AllocatePass("UICopyColor")->CopyTextureToTexture(outputView->mTexture, colorView->mTexture, 1, &region);
        return;
    }

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    auto textureWidth = colorView->mTexture->mDesc.Width;
    auto textureHeight = colorView->mTexture->mDesc.Height;
    Float4 screenSizeVector = {static_cast<float>(textureWidth), static_cast<float>(textureHeight), 1.0f / textureWidth, 1.0f / textureHeight};
    NGIClearValue clearValue{{0, 0, 0, 0}};

    auto uiTextureDesc = colorView->mTexture->mDesc;
    uiTextureDesc.Usage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource;
    uiTextureDesc.Format = GraphicsFormat::R8G8B8A8_SRGB;
    NGITextureViewDesc uiTexureViewDesc{uiTextureDesc.Usage, uiTextureDesc.Format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}};
    
    // UI and nanovg
    auto* canvasSys = mWorld->GetRenderSystem<CanvasSystemR>();

    auto uiTexturePack = canvasSys->GetValidUITexView(mRED, uiTextureDesc, uiTexureViewDesc);
    UInt64 key = std::get<0>(uiTexturePack);
    REDTextureView* uiTextureView = std::get<1>(uiTexturePack);

    clearValue.depthStencil.stencil = 0;
    REDDepthStencilTargetDesc depthStencilDesc = {depthStencilView, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};
    REDDepthStencilTargetDesc nvgDepthStencilDesc = {depthStencilView, NGILoadOp::Load, NGIStoreOp::DontCare, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};

    NGIResolveType depthResolveType = NGIResolveType::Average;
    NGIResolveType stencilResolveType = NGIResolveType::SampleZero;
    bool gEnableMSAA = GetNGIDevice().IsSupportMSAA(depthResolveType, stencilResolveType) && GetSetting()->UIMSAA;
    // gEnableMSAA = false;

    REDColorTargetDesc uiMSAAColorTargetDesc;
    REDDepthStencilTargetDesc uiMSAADepthStencilDesc;

    if (gEnableMSAA)
    {
        const UInt16 MSAASampleCount = 4;

        auto uiMSAATextureDesc = uiTextureView->mTexture->mDesc;
        uiMSAATextureDesc.Usage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource;
        uiMSAATextureDesc.Format = GraphicsFormat::R8G8B8A8_SRGB;
        uiMSAATextureDesc.SampleCount = MSAASampleCount;
        auto uiMSAATexture = mRED->AllocateTexture("UI Texture MSAA", uiMSAATextureDesc);

        NGITextureViewDesc uiMSAATexureViewDesc{uiMSAATextureDesc.Usage, uiMSAATextureDesc.Format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Color, 0, 1, 0, 1}};
        auto* uiMSAATextureView = mRED->AllocateTextureView(uiMSAATexture, uiMSAATexureViewDesc);

        auto depthStencilMSAATextureDesc = depthStencilView->mTexture->mDesc;
        depthStencilMSAATextureDesc.Usage = NGITextureUsage::DepthStencil | NGITextureUsage::CopyDst;
        depthStencilMSAATextureDesc.SampleCount = MSAASampleCount;
        auto depthStencilMSAATexture = mRED->AllocateTexture("UI Depth Stencil MSAA", depthStencilMSAATextureDesc);

        NGITextureViewDesc depthStencilMSAATextureViewDesc{depthStencilMSAATextureDesc.Usage,
                                                           depthStencilMSAATextureDesc.Format,
                                                           NGITextureType::Texture2D,
                                                           NGITextureSubRange{
                                                               NGITextureAspect::Depth | NGITextureAspect::Stencil,
                                                               0,
                                                               1,
                                                               0,
                                                               1,
                                                           }};
        auto* depthStencilMSAATextureView = mRED->AllocateTextureView(depthStencilMSAATexture, depthStencilMSAATextureViewDesc);

        {   // ui copy depth
            NGITextureViewDesc depthStencilViewDesc{NGITextureUsage::ShaderResource,
                                                    depthStencilView->mDesc.Format,
                                                    NGITextureType::Texture2D,
                                                    NGITextureSubRange{
                                                        NGITextureAspect::Depth,
                                                        0,
                                                        1,
                                                        0,
                                                        1,
                                                    }};
            auto* depthStencilTextureView = mRED->AllocateTextureView(depthStencilView->mTexture, depthStencilViewDesc);
            mGameContext.mRenderPipeline->PostProcessDepthStencil([&](auto pass) { pass->SetProperty(BuiltInProperty::ce_Scene_Depth, depthStencilTextureView); }, mUIProcessMtl, "UICopyDepth", true, true, depthStencilMSAATextureView);
        }

        uiMSAAColorTargetDesc = {uiMSAATextureView, NGILoadOp::Clear, NGIStoreOp::Resolve, clearValue, uiTextureView};

        clearValue.depthStencil = {mUseReverseZ ? 0.f : 1.f, 0};
        uiMSAADepthStencilDesc = {depthStencilMSAATextureView,
                                  NGILoadOp::Load,
                                  NGIStoreOp::DontCare,
                                  NGILoadOp::Clear,
                                  NGIStoreOp::DontCare,
                                  clearValue,

                                  depthStencilView,
                                  depthResolveType,
                                  stencilResolveType};
    }
    cross::REDDrawUnitList* drawUnitList = nullptr;

    auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(GetRenderCamera())});
    drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"UI", gRenderGroupUI, gRenderGroupGizmoWithSceneDepth - 1});
    
    auto renderSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* sampler_ClampAnisotropic = renderSys->GetRenderPrimitives()->mAnisotropicClampSampler.get();

    if (key > 0)
    {   // ui pass
        REDColorTargetDesc uiColorTargetDesc = {uiTextureView, NGILoadOp::Clear, NGIStoreOp::Store, clearValue};
        red->BeginRenderPass("UI", 1, gEnableMSAA ? &uiMSAAColorTargetDesc : &uiColorTargetDesc, gEnableMSAA ? &uiMSAADepthStencilDesc : &depthStencilDesc);

        auto uiTarget = NGIRenderPassTargetIndex::Target0;
        auto* uiPass = red->AllocateSubRenderPass("UI", 0, nullptr, 1, &uiTarget, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
        UpdateContext(uiPass->GetContext(), world, nullptr, true);
        uiPass->SetProperty(BuiltInProperty::ce_ScreenParams, screenSizeVector);
        uiPass->SetProperty(BuiltInProperty::ce_Scene_Color, colorView);
        uiPass->SetProperty(NAME_ID("Sampler_ClampAnisotropic"), sampler_ClampAnisotropic);

        uiPass->OnCulling([=](REDPass* pass) { uiPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView()); });
        uiPass->SetProperty(BuiltInProperty::CE_INSTANCING, true);
        uiPass->RenderDrawUnits({drawUnitList});

        red->EndRenderPass();
    }

    mGameContext.mRenderPipeline->PostProcess(
        [&](auto pass) {
            pass->SetProperty("ui_texture", uiTextureView);
            pass->SetProperty(BuiltInProperty::ce_Scene_Color, colorView, NGIResourceState::PixelShaderShaderResource);
        },
        mUIProcessMtl,
        "UIMerge",
        false,
        outputView);
}

void cross::IRenderPipeline::AssembleGizmoPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* depthStencilView, bool skipIsRenderToTargetCamera)
{
    auto camera = GetRenderCamera();

    if (skipIsRenderToTargetCamera)
    {
        if (camera->GetIsRenderToTargetCamera())
        {
            return;
        }
    }

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    REDColorTargetDesc colorDesc{
        colorView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        {},
    };

    {
        REDDepthStencilTargetDesc depthStencilDesc{
            depthStencilView,
            NGILoadOp::Load,
            NGIStoreOp::Store,
            NGILoadOp::Load,
            NGIStoreOp::Store,
        };     
        cross::REDDrawUnitList* drawUnitList = nullptr;

        auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(camera)});
        drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"Gizmo", gRenderGroupGizmoWithSceneDepth, gRenderGroupGizmoWithGizmoDepth - 1});

        red->BeginRenderPass("Gizmo", 1, &colorDesc, &depthStencilDesc);
        auto target = NGIRenderPassTargetIndex::Target0;
        auto* gizmoSceneDepthPass = red->AllocateSubRenderPass("GizmoSceneDepth", 0, nullptr, 1, &target, REDPassFlagBit::NeedDepth);
        UpdateContext(gizmoSceneDepthPass->GetContext(), world, nullptr, true);
        auto textureWidth = colorView->mTexture->mDesc.Width;
        auto textureHeight = colorView->mTexture->mDesc.Height;
        Float4 screenSizeVector = {static_cast<float>(textureWidth), static_cast<float>(textureHeight), 1.0f / textureWidth, 1.0f / textureHeight};

        gizmoSceneDepthPass->SetProperty(BuiltInProperty::ce_ScreenParams, screenSizeVector);
        gizmoSceneDepthPass->RenderDrawUnits({drawUnitList});
        
        red->EndRenderPass();
    }

    {
        auto gizmoDepthTexture = red->AllocateTexture("Gizmo Depth Texture",
                                                       NGITextureDesc{
                                                           mDepthStencilFormat,
                                                           NGITextureType::Texture2D,
                                                           1,
                                                           1,
                                                           colorView->mTexture->mDesc.Width,
                                                           colorView->mTexture->mDesc.Height,
                                                           1,
                                                           1,
                                                           NGITextureUsage::DepthStencil,
                                                       });

        auto* gizmoDepthTexturelView = red->AllocateTextureView(gizmoDepthTexture,
                                                                NGITextureViewDesc{NGITextureUsage::DepthStencil,
                                                                                   mDepthStencilFormat,
                                                                                   NGITextureType::Texture2D,
                                                                                   {
                                                                                       NGITextureAspect::Depth | NGITextureAspect::Stencil,
                                                                                       0,
                                                                                       1,
                                                                                       0,
                                                                                       1,
                                                                                   }});

        NGIClearValue clearValue{};
        clearValue.depthStencil = {mUseReverseZ ? 0.f : 1.f, 0};
        REDDepthStencilTargetDesc depthStencilDesc{
            gizmoDepthTexturelView,
            NGILoadOp::Clear,
            NGIStoreOp::DontCare,
            NGILoadOp::Clear,
            NGIStoreOp::DontCare,
            clearValue,
        };
        cross::REDDrawUnitList* drawUnitList = nullptr;

        auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(camera)});
        drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"Gizmo", gRenderGroupGizmoWithGizmoDepth, gRenderGroupOverlay - 1});

        red->BeginRenderPass("Gizmo", 1, &colorDesc, &depthStencilDesc);
        auto target = NGIRenderPassTargetIndex::Target0;
        auto* gizmoSelfDepthPass = red->AllocateSubRenderPass("GizmoSelfDepth", 0, nullptr, 1, &target, REDPassFlagBit::NeedDepth);
        UpdateContext(gizmoSelfDepthPass->GetContext(), world, nullptr, true);

        gizmoSelfDepthPass->RenderDrawUnits({drawUnitList});
        
        red->EndRenderPass();
    }
}

void cross::IRenderPipeline::AssembleBeforeToneMappingGizmoPass(RenderWorld* world, REDTextureView* colorView, REDTextureView* depthStencilView, bool skipIsRenderToTargetCamera)
{
    auto camera = GetRenderCamera();

    if (skipIsRenderToTargetCamera)
    {
        if (camera->GetIsRenderToTargetCamera())
        {
            return;
        }
    }

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    REDColorTargetDesc colorDesc{
        colorView,
        NGILoadOp::Load,
        NGIStoreOp::Store,
        {},
    };

    {
        REDDepthStencilTargetDesc depthStencilDesc{
            depthStencilView,
            NGILoadOp::Load,
            NGIStoreOp::Store,
            NGILoadOp::Load,
            NGIStoreOp::Store,
        };
        auto* cullingResult = mRED->Cull(REDCullingDesc{mWorld, const_cast<RenderCamera*>(camera)});
        cross::REDDrawUnitList* drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{"Gizmo_BeforeToneMapping", gRenderGroupGizmoWithSceneDepth, gRenderGroupGizmoWithGizmoDepth - 1});
        red->BeginRenderPass("Gizmo_BeforeToneMapping", 1, &colorDesc, &depthStencilDesc);
        auto target = NGIRenderPassTargetIndex::Target0;
        auto* gizmoSceneDepthPass = red->AllocateSubRenderPass("Gizmo_BeforeToneMapping", 0, nullptr, 1, &target, REDPassFlagBit::NeedDepth);
        UpdateContext(gizmoSceneDepthPass->GetContext(), world, nullptr, true);
        auto textureWidth = colorView->mTexture->mDesc.Width;
        auto textureHeight = colorView->mTexture->mDesc.Height;
        Float4 screenSizeVector = {static_cast<float>(textureWidth), static_cast<float>(textureHeight), 1.0f / textureWidth, 1.0f / textureHeight};
        gizmoSceneDepthPass->GetContext().SetProperty(BuiltInProperty::ce_ScreenParams, screenSizeVector);

        gizmoSceneDepthPass->RenderDrawUnits({drawUnitList});

        red->EndRenderPass();
    }
}

bool cross::IRenderPipeline::IsEnable() const
{
    auto camH = mWorld->GetComponent<CameraComponentR>(mCamera);
    auto camSys = mWorld->GetRenderSystem<CameraSystemR>();
    auto camEnabled = camSys->GetCameraEnable(camH.Read());
    return camEnabled;
}

void cross::IRenderPipeline::PreAssemble()
{
    mVisibleLightCullingResult = mRED->CullVisibleLight(mWorld, const_cast<RenderCamera*>(GetRenderCamera()));
    WaitAndPrepareVisibleLightList();
}



void copy_and_swap(cross::FrameStdVector<cross::GeneralPass*>& data, cross::RenderingExecutionDescriptor* red)
{
    auto newTempData = std::move(cross::FrameStdVector<cross::GeneralPass*>(red->GetREDFrameAllocator()));
    newTempData.insert(newTempData.begin(), data.begin(), data.end());
    data = newTempData;
}

void copy_and_swap(cross::FrameStdHashMap<cross::HashString, cross::GeneralPass>& data, cross::RenderingExecutionDescriptor* red)
{
    auto newTempData = std::move(cross::FrameStdHashMap<cross::HashString, cross::GeneralPass>(red->GetREDFrameAllocator()));
    for (auto & itr: data)
    {
        newTempData.insert(itr);
    }
    data = newTempData;
}

void cross::IRenderPipeline::PrepareRenderData()
{
    auto* cameraSystem = TYPE_CAST(CameraSystemR*, mWorld->GetRenderSystem<CameraSystemR>());
    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (cameraSystem->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }
    if (useTestCullingCamera)
    {
        ecs::EntityID cameraEntity = cameraSystem->GetTestCullingCamera();
        auto cullCameraR = mWorld->GetComponent<cross::CameraComponentR>(cameraEntity);
        SetCullingRenderCamera(mWorld->GetRenderSystem<cross::CameraSystemR>()->GetRenderCamera(cullCameraR.Read()));
    }

    auto DisplayView = mTargetView;
    GetBuiltInTexture<PassSemanticName::DisplayColor>() = DisplayView;

    // Display Target Size
    REDTextureRef& depthStencilTexForDisplay = mDepthStencilTexForDisplay;   // no reuse last frame depth stencil texture
    AllocateDepthStencilView(GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>(),
                             GetBuiltInTexture<PassSemanticName::DisplayDepthOnly>(),
                             depthStencilTexForDisplay,
                             UInt2{GetBuiltInTexture<PassSemanticName::DisplayColor>()->mTexture->mDesc.Width, GetBuiltInTexture<PassSemanticName::DisplayColor>()->mTexture->mDesc.Height});

    if (mUpscale && !mRenderDebugVisualize && mType != ViewType::ReflectionProbe && mType != ViewType::PrefabProxy)
    {
        auto& displayDesc = GetBuiltInTexture<PassSemanticName::DisplayColor>()->mTexture->mDesc;
        UInt32 renderWidth = static_cast<UInt32>(displayDesc.Width * 1.0f / mUpscaleValue);
        UInt32 renderHeight = static_cast<UInt32>(displayDesc.Height * 1.0f / mUpscaleValue);
        mTargetView = CreateTextureView2D("TargetView", renderWidth, renderHeight, displayDesc.Format, displayDesc.Usage);

        // Render Target Size
        REDTextureRef& depthStencilTexForRender = mDepthStencilTexForRender;
        AllocateDepthStencilView(GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>(), GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>(), depthStencilTexForRender, UInt2{renderWidth, renderHeight});
        GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>() = depthStencilTexForRender;
    }
    else
    {
        GetBuiltInTexture<PassSemanticName::DepthStencilTexRenderResolution>() = depthStencilTexForDisplay;
        GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>() = GetBuiltInTexture<PassSemanticName::DisplayDepthStencil>();
        GetBuiltInTexture<PassSemanticName::DepthOnlyBeforeUpScale>() = GetBuiltInTexture<PassSemanticName::DisplayDepthOnly>();
    }

    // Generate Jitter Data For Temporal based AA
    if ((mUpscale || mTAA) && mType != ViewType::ReflectionProbe)
    {
        auto& targetDesc = mTargetView->mTexture->mDesc;
        Float2 viewSize{static_cast<float>(targetDesc.Width), static_cast<float>(targetDesc.Height)};
        GenerateJitterData(GetRenderCamera(), viewSize, mUpscaleValue);
    }



    //mExtendPasses = std::move(FrameStdHashMap<HashString, GeneralPass>(mRED->GetREDFrameAllocator()));
    //mPassRefsAfterFSR2 = std::move(FrameStdVector<GeneralPass*>(mRED->GetREDFrameAllocator()));
    //mPassRefsAfterFSR2 = std::move(FrameStdVector<GeneralPass*>(mRED->GetREDFrameAllocator()));

    copy_and_swap(mExtendPasses, mRED);
    copy_and_swap(mPassRefsAfterFSR2, mRED);
    copy_and_swap(mPassRefsAfterFSR2, mRED);
}

void cross::IRenderPipeline::UpdateRenderContext()
{
    if (mTargetView)
    {
        // set some share global context;
        UpdateTargetView(mTargetView);
    }

    // set gamecamera pass
    // be careful, camera can change in following pass!!!
    UpdateCameraContext(GetRenderCamera());

    // curious parameter, but add it for now
    // mGameContext.mGlobalRenderContext->SetProperty("u_SunDirection", sunLightDir)

    // update each implementation's own shared context
    UpdatePipelineSharedContext();
}

void cross::IRenderPipeline::PrepareAssembleRenderContext()
{
    PrepareRenderData();
    UpdateRenderContext();
}

inline void cross::IRenderPipeline::Assemble()
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

    if (!mTargetView)
    {
        // readback camera with render target
        if (mRenderCamera->GetIsRenderToTargetCamera() && !IsMainCamera())
        {
            mTargetView = mRenderCamera->GetRenderTextureR()->GetREDTextureView();
        }
        // main camera
        else if (IsMainCamera())
        {
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeStandAlone)
            {
                mTargetView = rendererSystem->GetWindowRenderTarget();
            }
            else
            {
                auto renderTexture = mWorld->GetRenderSystem<RenderPipelineSystemR>()->GetEditorSceneViewTexture();
                mTargetView = renderTexture->GetREDTextureView();
            }
        }
    }

    if (mTargetView)
    {
        mRED->BeginRegion(fmt::format("Render World: {} with Camera: {}", mWorld->GetName().GetCString(), fmt::ptr(mRenderCamera)));

        REDTextureView* pre_resolved_view = nullptr;
        if (GetSetting()->CinematicScale > 1)
        {
            auto& targetDesc = mTargetView->mTexture->mDesc;
            UInt32 renderWidth = static_cast<UInt32>(targetDesc.Width) * GetSetting()->CinematicScale;
            UInt32 renderHeight = static_cast<UInt32>(targetDesc.Height) * GetSetting()->CinematicScale;
            pre_resolved_view = mTargetView;
            mTargetView = CreateTextureView2D("TargetView", renderWidth, renderHeight, targetDesc.Format, targetDesc.Usage | NGITextureUsage::ShaderResource);
        }

        PrepareAssembleRenderContext();
        
        // After Jitter Data is generated
        if (mType != ViewType::ReflectionProbe && IsMainCamera())
            GetWorldRenderPipeline()->GetNParticleSystemGpuDriven()->PostInitViews(mRED, mWorld, this, GetRenderCamera());

        Assemble(mTargetView);

        auto cinematic_scale = GetSetting()->CinematicScale;
        if (cinematic_scale > 1)
        {
            mSSAAResolve.Initialize(GetSetting()->mSSAAResolvePassSetting);
            mSSAAResolve.Execute(mGameContext, GetBuiltInTexture<PassSemanticName::DisplayColor>(), pre_resolved_view);
        }

        mRED->EndRegion();

        mTargetView = nullptr;
        REDTextureView* nulldisplay = nullptr;
        GetBuiltInTexture<PassSemanticName::DisplayColor>() = nulldisplay;
    }
    GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>() = nullptr;
}

void cross::IRenderPipeline::AssembleWireFramePass(RenderWorld* world, REDTextureView* colorView)
{
    if (GetRenderCamera()->GetIsRenderToTargetCamera())
        return;
    constexpr auto passName = "WireFrame";

    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* red = rendererSystem->GetRenderingExecutionDescriptor();

    REDColorTargetDesc colorDesc{
        colorView,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        NGIClearValue{{0, 0, 0, 0}},
    };

    red->BeginRenderPass(passName, 1, &colorDesc, nullptr);

    auto target = NGIRenderPassTargetIndex::Target0;
    auto* pass = red->AllocateSubRenderPass(passName, 0, nullptr, 1, &target, REDPassFlagBit{0});
    UpdateContext(pass->GetContext(), world, nullptr, true);
    //REDDrawFilter filter{
    //    world,
    //    GetRenderCamera(),
    //    {},
    //    0,
    //    gRenderGroupUI - 1,
    //    0,
    //    mEditorWireFrameMtl,
    //};
    //pass->DrawWorld(filter);

    red->EndRenderPass();
}

bool cross::IRenderPipeline::IsMainCamera() const
{
    return mCamera == mWorld->GetRenderSystem<cross::CameraSystemR>()->GetMainCamera();
}

void cross::IRenderPipeline::AllocateDepthStencilView(REDTextureView*& dsView, REDTextureView*& depthOnlyView, REDTextureRef& dsTex, UInt2 size)
{
    auto RTWidth = size.x;
    auto RTHeight = size.y;

    NGITextureViewDesc viewDesc{
        NGITextureUsage::DepthStencil | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
        mDepthStencilFormat,
        NGITextureType::Texture2D,
        {NGITextureAspect::Depth | NGITextureAspect::Stencil, 0, 1, 0, 1},
    };

    bool fromLastFrame = dsTex && mRED->Validate(dsTex) && dsTex->mDesc.Width == size.x && dsTex->mDesc.Height == size.y;

    if (fromLastFrame)
    {
        dsView = mRED->AllocateTextureView(dsTex, viewDesc);
    }
    else
    {
        NGITextureDesc depthStencilDesc{
            mDepthStencilFormat,
            NGITextureType::Texture2D,
            1,
            1,
            RTWidth,
            RTHeight,
            1,
            1,
            NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
        };
        dsTex = mRED->AllocateTexture("DepthStencil", depthStencilDesc);
        dsView = mRED->AllocateTextureView(dsTex, viewDesc);

        auto* pass = mRED->AllocatePass("Clear DepthStencil");
        NGIClearValue clearValue{};
        clearValue.depthStencil = {mUseReverseZ ? 0.f : 1.f, 0};
        pass->ClearTexture(dsView, clearValue);
    }

    viewDesc.SubRange.Aspect = NGITextureAspect::Depth;
    viewDesc.Usage |= NGITextureUsage::ShaderResource;
    depthOnlyView = mRED->AllocateTextureView(dsTex, viewDesc);

    dsTex->ExtendLifetime();
}

void cross::RenderPipelineR::ClearRefleProbeList()
{
    mReflecProbeList.clear();
}

void cross::RenderPipelineR::AddVisibleRefleProbe(ecs::EntityID rpEntity)
{
    mReflecProbeList.push_back(rpEntity);
}
