#include "EnginePrefix.h"
#include "BuiltInRenderPipeline.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/VisibilitySystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/ShadowSystemR.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/SkeletonSystemR.h"
#include "CECommon/Common/FrameAllocatorPool.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
cross::BuiltInRenderPipeline::~BuiltInRenderPipeline()
{
}

void cross::BuiltInRenderPipeline::UpdateSetting(const RenderPipelineSetting* setting)
{
    IRenderPipeline::UpdateSetting(setting);
    
    mPostProcessMtl = GetSetting()->PostProcessMtlR;
    mTAA = GetSetting()->TAA;
}

void cross::BuiltInRenderPipeline::Assemble(REDTextureView* targetView)
{
    if (GetSetting()->TAA)
    {
        GenerateJitterData(GetRenderCamera(), Float2(static_cast<float>(mTargetView->mTexture->mDesc.Width), static_cast<float>(mTargetView->mTexture->mDesc.Height)));
    }

    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;

    auto* sceneColorView = CreateTextureView2D("Scene Color Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::SubpassInput | NGITextureUsage::ShaderResource);

    REDTextureView* sceneDepthStencilView = nullptr;
    if (GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>())
    {
        sceneDepthStencilView = GetBuiltInTexture<PassSemanticName::DepthStencilBeforeUpScale>();
    }
    else
    {
        auto sceneDepthStencilTexture =
            mRED->AllocateTexture("DepthStencil Texture", NGITextureDesc{mDepthStencilFormat, NGITextureType::Texture2D, 1, 1, gameViewWidth, gameViewHeight, 1, 1, NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource});

        sceneDepthStencilView = mRED->AllocateTextureView(sceneDepthStencilTexture,
                                                          NGITextureViewDesc{NGITextureUsage::DepthStencil,
                                                                             mDepthStencilFormat,
                                                                             NGITextureType::Texture2D,
                                                                             {
                                                                                 NGITextureAspect::Depth | NGITextureAspect::Stencil,
                                                                                 0,
                                                                                 1,
                                                                                 0,
                                                                                 1,
                                                                             }});
    }

    auto* normalDepthView = CreateTextureView2D("Encode Normal Depth Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R16G16B16A16_SFloat, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
    auto* specularRoughnessView = CreateTextureView2D("Specular Roughness Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    // Shadow
    std::array<REDTextureView*, 4> shadowViews = {nullptr};
    std::array<Float4x4, 4> shadowMatrices;
    std::array<Float4, 4> shadowSplitDatas;
#if !CROSSENGINE_ANDROID
    AssembleShadow(shadowViews, shadowMatrices, shadowSplitDatas);
#else
    if (EngineGlobal::GetSettingMgr()->GetRenderMode() == NGIPlatform::OpenGLES3)
        AssembleShadow(shadowViews, shadowMatrices, shadowSplitDatas);
#endif

    AssembleForward(mWorld, shadowViews, shadowMatrices, shadowSplitDatas, sceneColorView, sceneDepthStencilView);

    REDTextureView* ssrView = nullptr;
    if (GetSetting()->SSR)
    {
        AssembleEncodeNormalDepth(mWorld, normalDepthView, specularRoughnessView, sceneDepthStencilView);
        ssrView = CreateTextureView2D("SSR Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
        if (*mPostProcessMtl->GetBool("_IsStochasticSSR")) {
            REDTextureView* hizView = nullptr;
            if (!(*mPostProcessMtl->GetBool("_IsLinearTrace")))
            {
                AssembleHiZ(mWorld, sceneDepthStencilView, hizView);
            }
            AssembleStochasticSSR(mWorld, sceneColorView, normalDepthView, specularRoughnessView, hizView, ssrView);
        }
        else
        {
            AssembleSSR(mWorld, sceneColorView, normalDepthView, specularRoughnessView, ssrView);
        }
    }
    else
    {
        ssrView = sceneColorView;
    }

    REDTextureView* bloomView = nullptr;

    if (GetSetting()->Bloom)
    {
        bloomView = CreateTextureView2D("Scene Color Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
        AssembleBloom_KinoMethod(mWorld, ssrView, bloomView);
    }
    else
    {
        bloomView = ssrView;
    }

    REDTextureView* taaView = nullptr;
    //TAA Render
    if (GetSetting()->TAA)
    {
        AssembleTAA_Deprecated(mWorld, bloomView, sceneDepthStencilView, taaView);
    }
    else
    {
        taaView = bloomView;
    }

    REDTextureView* colorView = CreateTextureView2D("Color View Texture", gameViewWidth, gameViewHeight, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);
    auto pool = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    if (GetSetting()->ColorGrading)
    {
        // --------- TONE MAPPING -------------------
        auto* lutView = CreateTextureView2D("Lut Texture", 1024, 32, GraphicsFormat::R8G8B8A8_UNorm, NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);     
        std::vector<REDTextureView*> lutViews = { lutView };
        PostProcess(mRED, lutViews, mPostProcessMtl, "lut", RenderContext(pool));

        RenderContext toneMappingCtx(pool);
        toneMappingCtx.SetProperty("src_texture", taaView);
        toneMappingCtx.SetProperty("lut_texture", lutView);
        std::vector<REDTextureView*> toneMappingViews = { colorView };
        PostProcess(mRED, toneMappingViews, mPostProcessMtl, "tone_mapping", std::move(toneMappingCtx));
    }
    else
    {
        RenderContext blitCtx(pool);
        blitCtx.SetProperty("_ColorTex", taaView);
        std::vector<REDTextureView*> blitViews = { colorView };
        PostProcess(mRED, blitViews, mPostProcessMtl, "CopyColor", std::move(blitCtx));
    }

    // UI
    AssembleUIPass(mWorld, colorView, mTargetView, sceneDepthStencilView);

    AssembleOutlinePass(mWorld, mTargetView);

    if (GetSetting()->ViewMode == ViewMode::WIREFRAME)
    {
        AssembleWireFramePass(mWorld, mTargetView);
    }

    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpType::AppStartUpTypeStandAlone)
    {
        AssembleGizmoPass(mWorld, mTargetView, sceneDepthStencilView);
    }
}

void cross::BuiltInRenderPipeline::AssembleShadow(std::array<REDTextureView*, 4>& shadowViews, std::array<Float4x4, 4>& shadowMatrices, std::array<Float4, 4>& shadowSplitDatas)
{
    //temp code to put hard code to one place
    auto* shadowCameraSys = TYPE_CAST(ShadowSystemR*, mWorld->GetRenderSystem<ShadowSystemR>());
    UInt32 cascadeCount = shadowCameraSys->GetCascadeCount();
    UInt32 shadowSize = GetSetting()->ShadowMapResolution;

    //auto* transformSystem = TYPE_CAST(TransformSystemR*, mWorld->GetRenderSystem(TransformSystemR::GetDesc().mID));
    auto* lightSystem = TYPE_CAST(LightSystemR*, mWorld->GetRenderSystem<LightSystemR>());
    auto* shadowCameraSystem = TYPE_CAST(ShadowSystemR*, mWorld->GetRenderSystem<ShadowSystemR>());
    auto* rendererSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* renderGraphContext = rendererSystem->GetRenderingExecutionDescriptor();
    auto& lights = GetLightList();

    for (int i = 0; i < 4; i++)
    {
        REDTextureRef shadowMap =
            renderGraphContext->AllocateTexture("ShadowMap", NGITextureDesc{GraphicsFormat::D32_SFloat, NGITextureType::Texture2D, 1, 1, shadowSize, shadowSize, 1, 1, NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource});

        shadowViews[i] = renderGraphContext->AllocateTextureView(
            shadowMap, NGITextureViewDesc{NGITextureUsage::DepthStencil | NGITextureUsage::ShaderResource, GraphicsFormat::D32_SFloat, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Depth, 0, 1, 0, 1}});
    }

    if (lights.size() == 0)
        return;

    for (UInt32 index = 0; index < cascadeCount; index++)
    {
        NGIClearValue clearValue{};
        clearValue.depthStencil.depth = 1.f;
        clearValue.depthStencil.stencil = 0;
        REDDepthStencilTargetDesc depthStencilTargetDesc{
            shadowViews[index],
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            NGILoadOp::Clear,
            NGIStoreOp::DontCare,
            clearValue,
        };

        auto [lightComp, shadowCameraComp] = mWorld->GetComponent<LightComponentR, ShadowCameraComponentR>(lights.front());
        if (lightSystem->GetLightType(lightComp.Read()) != LightType::Directional || !lightSystem->GetLightCastShadow(lightComp.Read()) || GetSetting()->Shadow == false)
        {
            renderGraphContext->BeginRenderPass("ShadowCaster", 0, nullptr, &depthStencilTargetDesc);
            renderGraphContext->AllocateSubRenderPass("shadow", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);
        }
        else
        {
            const DirectionalLightShadowCamera* shadowCamera = &(*shadowCameraSystem->GetDirectionalShadowCameras(shadowCameraComp.Read(), GetCamera()))[index];
            Assert(shadowCamera);

            shadowMatrices[index] = shadowCamera->GetViewProjMatrix();
            //Float3 boundingSphereCenter = shadowCamera->GetBoundingSphereCenter();
            //float boundingSphereRadius = shadowCamera->GetBoundingSphereRadiusSquare();
            //shadowSplitDatas[index] = {boundingSphereCenter.x, boundingSphereCenter.y, boundingSphereCenter.z, boundingSphereRadius};

            renderGraphContext->BeginRenderPass("ShadowCaster", 0, nullptr, &depthStencilTargetDesc);
            auto* shadowPass = renderGraphContext->AllocateSubRenderPass("shadow", 0, nullptr, 0, nullptr, REDPassFlagBit::NeedDepth);

            // For GPU_SKIN
            const auto settingMgr = EngineGlobal::Inst().GetSettingMgr();
            if (settingMgr->GetGPUSkinEnable())
            {   
                // todo xtnwang-2-13
                //auto* skeletonSys = TYPE_CAST(SkeletonSystemR*, mWorld->GetRenderSystem<SkeletonSystemR>());
                //shadowPass->SetProperty("matrix_texture", skeletonSys->GetBoneTextureView());
            }
            shadowPass->SetProperty(BuiltInProperty::ce_View, shadowCamera->GetViewMatrix());
            shadowPass->SetProperty(BuiltInProperty::ce_Projection, shadowCamera->GetProjMatrix());
            //shadowPass->DrawWorld({mWorld, shadowCamera, "shadow", 0, UINT16_MAX});
        }

        renderGraphContext->EndRenderPass();
    }
}

void cross::BuiltInRenderPipeline::AssembleForward(RenderWorld* world, std::array<REDTextureView*, 4>& shadowViews, std::array<Float4x4, 4>& shadowMatrices,
                                                              std::array<Float4, 4>& shadowSplitDatas, REDTextureView* colorView, REDTextureView* depthView)
{
    auto textureWidth = depthView->mTexture->mDesc.Width;
    auto textureHeight = depthView->mTexture->mDesc.Height;
    Float4 screenSizeVector = {static_cast<float>(textureWidth), static_cast<float>(textureHeight), 1.0f / textureWidth, 1.0f / textureHeight};
    auto* cameraSystem = TYPE_CAST(CameraSystemR*, mWorld->GetRenderSystem<CameraSystemR>());
    //temp code to put hard code to one place
    auto* shadowCameraSys = TYPE_CAST(ShadowSystemR*, mWorld->GetRenderSystem<ShadowSystemR>());
    UInt32 cascadeCount = shadowCameraSys->GetCascadeCount();

    NGIResolveType depthResolveType = NGIResolveType::Average;
    NGIResolveType stencilResolveType = NGIResolveType::DontResolve;
    const bool gEnableMSAA = GetNGIDevice().IsSupportMSAA(depthResolveType, stencilResolveType);

    REDColorTargetDesc colorTargetDesc;
    REDDepthStencilTargetDesc depthStencilTargetDesc;

    if (gEnableMSAA && GetSetting()->MSAA)
    {
        auto colorTexMSAADesc = colorView->mTexture->mDesc;
        colorTexMSAADesc.Usage = NGITextureUsage::RenderTarget;
        auto msaaSampleCount = GetSetting()->MSAASampleCount;
        colorTexMSAADesc.SampleCount = msaaSampleCount;

        auto colorTexMSAA = mRED->AllocateTexture("Scene Color Texture MSAA", colorTexMSAADesc);
        auto* colorViewMSAA = mRED->AllocateTextureView(colorTexMSAA,
                                                       NGITextureViewDesc{
                                                           NGITextureUsage::RenderTarget,
                                                           colorTexMSAADesc.Format,
                                                           NGITextureType::Texture2D,
                                                           {
                                                               NGITextureAspect::Color,
                                                               0,
                                                               1,
                                                               0,
                                                               1,
                                                           },
                                                       });

        auto depthTexMSAADesc = depthView->mTexture->mDesc;
        depthTexMSAADesc.Usage = NGITextureUsage::DepthStencil;
        depthTexMSAADesc.SampleCount = msaaSampleCount;
        auto depthTexMSAA = mRED->AllocateTexture("Depth Stencil Texture MSAA", depthTexMSAADesc);
        auto* depthViewMSAA = mRED->AllocateTextureView(depthTexMSAA,
                                                       NGITextureViewDesc{NGITextureUsage::DepthStencil,
                                                                          depthTexMSAADesc.Format,
                                                                          NGITextureType::Texture2D,
                                                                          {
                                                                              NGITextureAspect::Depth | NGITextureAspect::Stencil,
                                                                              0,
                                                                              1,
                                                                              0,
                                                                              1,
                                                                          }});

        NGIClearValue clearValue{{0, 0, 0, 0}};
        colorTargetDesc = {
            colorViewMSAA,
            NGILoadOp::Clear,
            NGIStoreOp::Resolve,
            clearValue,
            colorView,
        };

        clearValue.depthStencil = {1.f, 0};
        depthStencilTargetDesc = {
            depthViewMSAA,
            NGILoadOp::Clear,
            NGIStoreOp::Resolve,
            NGILoadOp::Clear,
            NGIStoreOp::DontCare,
            clearValue,

            depthView,
            NGIResolveType::Average,
            NGIResolveType::DontResolve,
        };
    }
    else
    {
        NGIClearValue clearValue{{0, 0, 0, 0}};
        colorTargetDesc = {
            colorView,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            clearValue,
        };

        clearValue.depthStencil = {1.f, 0};
        depthStencilTargetDesc = {
            depthView,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            clearValue,
        };
    }

    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (cameraSystem->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }
    auto* drawCamera = useTestCullingCamera ? GetCullingRenderCamera() : GetRenderCamera();

    mRED->BeginRenderPass("ForwardShading", 1, &colorTargetDesc, &depthStencilTargetDesc);

    auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
    auto* forwardPass = mRED->AllocateSubRenderPass("ForwardShading", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
    UpdateContext(forwardPass->GetContext(), mWorld, mPostProcessMtl);

    if (shadowViews[0])
    {
        forwardPass->SetProperty("shadow_matrices", shadowMatrices.data(), shadowMatrices.size() * sizeof(Float4x4));
        forwardPass->SetProperty("shadow_SplitDatas", shadowSplitDatas.data(), shadowSplitDatas.size() * sizeof(Float4));
        forwardPass->SetProperty("shadow_texture0", shadowViews[0]);
        forwardPass->SetProperty("shadow_texture1", shadowViews[1]);
        forwardPass->SetProperty("shadow_texture2", shadowViews[2]);
        forwardPass->SetProperty("shadow_texture3", shadowViews[3]);
        Float4 shadowSize(static_cast<float>(shadowViews[0]->mTexture->mDesc.Width), static_cast<float>(shadowViews[0]->mTexture->mDesc.Height), 1.0f / shadowViews[0]->mTexture->mDesc.Width, 1.0f / shadowViews[0]->mTexture->mDesc.Height);
        forwardPass->SetProperty("shadow_size", shadowSize);
        forwardPass->SetProperty("shadow_CascadeShadowCount", static_cast<float>(cascadeCount));
        forwardPass->SetProperty("shadow_lerpBorder", 1.0f - GetSetting()->CSMTransitionFactor);

        forwardPass->AddTextureReference(shadowViews[0], REDResourceState{NGIResourceState::PixelShaderShaderResource});
        forwardPass->AddTextureReference(shadowViews[1], REDResourceState{NGIResourceState::PixelShaderShaderResource});
        forwardPass->AddTextureReference(shadowViews[2], REDResourceState{NGIResourceState::PixelShaderShaderResource});
        forwardPass->AddTextureReference(shadowViews[3], REDResourceState{NGIResourceState::PixelShaderShaderResource});
    }

    Float4 maxRayCount_NumStep_DepthBias_LengthScale;
    Float4 lightR_TexelDitherS_Band_I0Steps;
    Float4 i1StepScale_SMRTOn_SoftPCFOn_RandType;
    if (auto val = mPostProcessMtl->GetVector("MaxRayCount_NumStep_DepthBias_LengthScale"); val)
    {
        memcpy(maxRayCount_NumStep_DepthBias_LengthScale.data(), &val.value(), sizeof(Float4));
    }
    if (auto val = mPostProcessMtl->GetVector("LightR_TexelDitherS_Band_I0Steps"); val)
    {
        memcpy(lightR_TexelDitherS_Band_I0Steps.data(), &val.value(), sizeof(Float4));
    }
    if (auto val = mPostProcessMtl->GetVector("I1StepScale_SMRTOn_SoftPCFOn_RandType"); val)
    {
        memcpy(i1StepScale_SMRTOn_SoftPCFOn_RandType.data(), &val.value(), sizeof(Float4));
    }
    forwardPass->SetProperty("MaxRayCount_NumStep_DepthBias_LengthScale", maxRayCount_NumStep_DepthBias_LengthScale);
    forwardPass->SetProperty("LightR_TexelDitherS_Band_I0Steps", lightR_TexelDitherS_Band_I0Steps);
    forwardPass->SetProperty("I1StepScale_SMRTOn_SoftPCFOn_RandType", i1StepScale_SMRTOn_SoftPCFOn_RandType);

    //auto* skyLightSys = mWorld->GetRenderSystem<SkyLightSystemR>();
    //forwardPass->SetProperty("ce_UE4AmbientProbeSH", skyLightSys->GetDiffuseProbe().data(), static_cast<UInt32>(sizeof(SkyLightDiffuseProbe)));
    //forwardPass->SetProperty("ce_SkyLightColor", skyLightSys->GetColor().data(), sizeof(Float3));
    //forwardPass->SetProperty("ce_LightMapIntensity", skyLightSys->GetLightMapIntensityDebug());
    //UpdateSkyLightContext(forwardPass, skyLightSys);

    forwardPass->SetProperty("ce_ScreenParams", screenSizeVector);

    //forwardPass->DrawWorld({mWorld, drawCamera, gForwardID, gRenderGroupOpaque, gRenderGroupUI - 1});

    forwardPass->SetProperty("u_SunDirection", sunLightDir);

    auto* scatteringpass = mRED->AllocateSubRenderPass("scattering", 0, nullptr, 1, &colorTargetIndex, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
    //scatteringpass->DrawWorld({mWorld, drawCamera, "scattering", gRenderGroupOpaque, gRenderGroupUI - 1});

    mRED->EndRenderPass();
}

void cross::BuiltInRenderPipeline::AssembleEncodeNormalDepth(RenderWorld* world, REDTextureView* normalDepthView, REDTextureView* specularRoughnessView, REDTextureView* depthView)
{
    auto* cameraSystem = TYPE_CAST(CameraSystemR*, mWorld->GetRenderSystem<CameraSystemR>());
    // REDColorTargetDesc colorTargetDesc;
    REDDepthStencilTargetDesc depthStencilTargetDesc;

    NGIClearValue clearValue{{0, 0, 1, 0}};

    std::array<REDColorTargetDesc, 2> encodePassTargets{{{
                                                             normalDepthView,
                                                             NGILoadOp::Clear,
                                                             NGIStoreOp::Store,
                                                             clearValue,
                                                         },
                                                         {
                                                             specularRoughnessView,
                                                             NGILoadOp::Clear,
                                                             NGIStoreOp::Store,
                                                             NGIClearValue{0, 0, 0, 0},
                                                         }}};

    clearValue.depthStencil = {1.f, 0};
    depthStencilTargetDesc = {
        depthView,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        NGILoadOp::Clear,
        NGIStoreOp::Store,
        clearValue,
    };

    bool useTestCullingCamera = false;
    if (EngineGlobal::GetSettingMgr()->GetCullingVisualizationEnable())
    {
        if (cameraSystem->GetTestCullingCamera())
        {
            useTestCullingCamera = true;
        }
    }
    auto* drawCamera = useTestCullingCamera ? GetCullingRenderCamera() : GetRenderCamera();

    mRED->BeginRenderPass("EncodeDepthNormalShading", 2, encodePassTargets.data(), &depthStencilTargetDesc);

    // auto colorTargetIndex = NGIRenderPassTargetIndex::Target0;
    NGIRenderPassTargetIndex colorTargetIndices[2] = {NGIRenderPassTargetIndex::Target0, NGIRenderPassTargetIndex::Target1};
    auto* mPass = mRED->AllocateSubRenderPass("EncodeDepthNormalPass", 0, nullptr, 2, colorTargetIndices, REDPassFlagBit::NeedDepth | REDPassFlagBit::NeedStencil);
    UpdateContext(mPass->GetContext(), mWorld, mPostProcessMtl);
    //mPass->DrawWorld({mWorld, drawCamera, "encode_nd", gRenderGroupOpaque, gRenderGroupUI - 1});

    mRED->EndRenderPass();
}

void cross::BuiltInRenderPipeline::AssembleSSR(RenderWorld* world, REDTextureView* sceneView, REDTextureView* normalDepthView, REDTextureView* specularRoughnessView, REDTextureView* ssrView)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;
    const RenderCamera* renderCamera = GetRenderCamera();
    
    auto& viewMatrix = renderCamera->GetViewMatrix();
    auto& invViewMatrix = renderCamera->GetInvertViewMatrix();
    Float4x4 projMatrix = renderCamera->GetProjMatrix();
    Float4x4 invProjMatrix = renderCamera->GetInvertProjMatrix();
    if (GetSetting()->TAA)
    {
        projMatrix =  GetJitterData()->jitterProjMat;
        invProjMatrix = projMatrix.Inverted();
    }
    float nearPlane = renderCamera->GetNearPlane();
    float farPlane = renderCamera->GetFarPlane();
    Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);

    auto minSize = gameViewWidth < gameViewHeight ? gameViewWidth : gameViewHeight;
    UInt16 lodCount = static_cast<UInt16>(std::log(minSize) / std::log(2));
    lodCount = minSize - std::pow(2, lodCount) < std::pow(2, lodCount + 1) - minSize ? lodCount : lodCount + 1;
    UInt16 size = static_cast<UInt16>(std::pow(static_cast<UInt16>(2), lodCount));
    // downsample size/=2;
    // upsample size*=2;

    const UInt16 maxLod = 5;
    lodCount = lodCount - 3 < maxLod ? lodCount - 3 : maxLod;

   
    UInt16 sizeScale = static_cast<UInt16>(*mPostProcessMtl->GetFloat("_SSRResolutionScale"));
    auto ssrTexSizeW = sizeScale * gameViewWidth;
    auto ssrTexSizeH = sizeScale * gameViewHeight;
    auto* marchTestView = CreateTextureView2D(
        "SSR March Texture",
        ssrTexSizeW,
        ssrTexSizeH,
        GraphicsFormat::R16G16B16A16_SFloat,
        NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    Float4 projectionParams = Float4(1.0f, nearPlane, farPlane, 1.0f / farPlane);
    Float4 marchTestTexelSize = Float4(1.0f/ static_cast<float>(ssrTexSizeW), 1.0f / static_cast<float>(ssrTexSizeH), static_cast<float>(ssrTexSizeW), static_cast<float>(ssrTexSizeH));
    Float4 mainTexelSize = Float4(1.0f / static_cast<float>(gameViewWidth), 1.0f / static_cast<float>(gameViewHeight), static_cast<float>(gameViewWidth), static_cast<float>(gameViewHeight));
    float aspectRatio = static_cast<float>(gameViewWidth) / static_cast<float>(gameViewHeight);
    float noiseTexSize = 512.0;
    float noiseTiling = static_cast<float>(size) / noiseTexSize;
    Float3 params = Float3(aspectRatio, noiseTiling, static_cast<float>(lodCount));
    auto pool = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    RenderContext ctx(pool);
    ctx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
    ctx.SetProperty("_InverseViewMatrix", invViewMatrix);
    ctx.SetProperty("_ProjectionMatrix", projMatrix);
    ctx.SetProperty("_ViewMatrix", viewMatrix);
    ctx.SetProperty("_ProjectionParams", projectionParams);
    ctx.SetProperty("_MarchTest_TexelSize", marchTestTexelSize);
    ctx.SetProperty("_MainTex_TexelSize", mainTexelSize);
    ctx.SetProperty("_Params", params);
    ctx.SetProperty("_CameraPosWS", cameraPos);

    ctx.SetProperty("_MainMap", sceneView);
    ctx.SetProperty("_NormalDepthMap", normalDepthView);
    std::vector<REDTextureView*> marchTestViews = { marchTestView };
    PostProcess(mRED, marchTestViews, mPostProcessMtl, "ssr_march", std::move(ctx));

    auto resolveTexture = mRED->AllocateTexture("SSR Resolve Texture",
        NGITextureDesc
        {
            GraphicsFormat::R8G8B8A8_UNorm,
            NGITextureType::Texture2D,
            lodCount,
            1,
            ssrTexSizeW,
            ssrTexSizeH,
            1,
            1,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource
        });

    REDTextureView* tempResolveView[8];
    tempResolveView[0] = mRED->AllocateTextureView(resolveTexture,
                                                  NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                                     GraphicsFormat::R8G8B8A8_UNorm,
                                                                     NGITextureType::Texture2D,
                                                                     {
                                                                         NGITextureAspect::Color,
                                                                         0,
                                                                         1,
                                                                         0,
                                                                         1,
                                                                     }});
    RenderContext resolveCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    resolveCtx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
    resolveCtx.SetProperty("_InverseViewMatrix", invViewMatrix);
    resolveCtx.SetProperty("_ProjectionMatrix", projMatrix);
    resolveCtx.SetProperty("_ViewMatrix", viewMatrix);
    resolveCtx.SetProperty("_ProjectionParams", projectionParams);
    resolveCtx.SetProperty("_MarchTest_TexelSize", marchTestTexelSize);
    resolveCtx.SetProperty("_MainTex_TexelSize", mainTexelSize);
    resolveCtx.SetProperty("_Params", params);
    resolveCtx.SetProperty("_CameraPosWS", cameraPos);
    resolveCtx.SetProperty("_Epsilon", 0.001f);
    resolveCtx.SetProperty("_MainMap", sceneView);
    resolveCtx.SetProperty("_NormalDepthMap", normalDepthView);
    resolveCtx.SetProperty("_MarchTestMap", marchTestView);
    std::vector<REDTextureView*> firstResolveViews = { tempResolveView[0] };
    PostProcess(mRED, firstResolveViews, mPostProcessMtl, "ssr_resolve", std::move(resolveCtx));

    for (UInt16 count = 0; count < lodCount - 1; count++)
    {
        tempResolveView[count + 1] = mRED->AllocateTextureView(resolveTexture,
                                                              NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                                                 GraphicsFormat::R8G8B8A8_UNorm,
                                                                                 NGITextureType::Texture2D,
                                                                                 {
                                                                                     NGITextureAspect::Color,
                                                                                     static_cast<UInt16>(count + 1),
                                                                                     1,
                                                                                     0,
                                                                                     1,
                                                                                 }});
        RenderContext mipmapCtx(pool);
        mipmapCtx.SetProperty("_ResolveMap", tempResolveView[count]);
        mipmapCtx.SetProperty("_SpecularRMap", specularRoughnessView);
        mipmapCtx.SetProperty("_MarchTest_TexelSize", marchTestTexelSize);
        mipmapCtx.SetProperty("_CurrentMipLevel", static_cast<float>(count));
        std::vector<REDTextureView*> tempResolveViews = { tempResolveView[count + 1] };
        PostProcess(mRED, tempResolveViews, mPostProcessMtl, "ssr_mipmap", std::move(mipmapCtx));
    }

    REDTextureView* resolveView = mRED->AllocateTextureView(resolveTexture,
                                                           NGITextureViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                                              GraphicsFormat::R8G8B8A8_UNorm,
                                                                              NGITextureType::Texture2D,
                                                                              {
                                                                                  NGITextureAspect::Color,
                                                                                  0,
                                                                                  lodCount,
                                                                                  0,
                                                                                  1,
                                                                              }});

    RenderContext combineCtx(pool);
    auto& refleProbes = GetRefleProbeList();
    if (refleProbes.size() != 0) {
        auto [rpH, transformH] = mWorld->GetComponent<ReflectionProbeCameraComponentR, TransformComponentR>(refleProbes.front());
        auto* rpSystem = TYPE_CAST(ReflectionProbeSystemR*, mWorld->GetRenderSystem<ReflectionProbeSystemR>());
        auto refleTexture = rpSystem->GetReflectionProbeCameraReflectionCubeMap(rpH.Read());
        combineCtx.SetProperty("_ReflectionCubemap", refleTexture->GetNGITextureView());
    }
    else
    {
        combineCtx.SetProperty("_ReflectionCubemap", rendererSystem->GetRenderPrimitives()->mDefaultBlackTexture2DView.get());
    }

    combineCtx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
    combineCtx.SetProperty("_InverseViewMatrix", invViewMatrix);
    combineCtx.SetProperty("_ProjectionMatrix", projMatrix);
    combineCtx.SetProperty("_ProjectionParams", projectionParams);
    combineCtx.SetProperty("_MarchTest_TexelSize", marchTestTexelSize);
    combineCtx.SetProperty("_MainTex_TexelSize", mainTexelSize);
    combineCtx.SetProperty("_Params", params);
    combineCtx.SetProperty("_CameraPosWS", cameraPos);
    combineCtx.SetProperty("_MainMap", sceneView);
    combineCtx.SetProperty("_NormalDepthMap", normalDepthView);
    combineCtx.SetProperty("_SpecularRMap", specularRoughnessView);
    combineCtx.SetProperty("_MarchTestMap", marchTestView);
    combineCtx.SetProperty("_ResolveMap", resolveView);
    std::vector<REDTextureView*> combineViews = { ssrView };
    PostProcess(mRED, combineViews, mPostProcessMtl, "ssr_combine", std::move(combineCtx));
}

void cross::BuiltInRenderPipeline::AssembleStochasticSSR(RenderWorld* world, REDTextureView* sceneView, REDTextureView* inNDView, REDTextureView* specularRView,
                                                                    REDTextureView* hizView, REDTextureView* ssrView)
{
    const UInt16 kSampleCount = static_cast<UInt16>(64);

    auto GenerateRandomOffset = [&]()
    {
        Float2 offset = Float2(Random::GetHaltonValue(mSSRsampleIndex & 1023, 2), Random::GetHaltonValue(mSSRsampleIndex & 1023, 3));
        if (mSSRsampleIndex++ >= kSampleCount)
            mSSRsampleIndex = static_cast<UInt16>(0);
        return offset;
    };

    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;
    const RenderCamera* renderCamera = GetRenderCamera();
    Float4x4 projMatrix = renderCamera->GetProjMatrix();
    Float4x4 invProjMatrix = renderCamera->GetInvertProjMatrix();
    if (GetSetting()->TAA)
    {
        projMatrix = GetJitterData()->jitterProjMat;
        invProjMatrix = projMatrix.Inverted();
    }
    auto& invViewMatrix = renderCamera->GetInvertViewMatrix();
    Float4x4 invViewProjMatrix = invProjMatrix * invViewMatrix;
    float nearPlane = renderCamera->GetNearPlane();
    float farPlane = renderCamera->GetFarPlane();
    Float3 cameraPos = Float3(invViewMatrix.m30, invViewMatrix.m31, invViewMatrix.m32);

    UInt16 lodCount = static_cast<UInt16>(std::log(gameViewWidth < gameViewHeight ? gameViewWidth : gameViewHeight) / std::log(2));
    lodCount = lodCount - 3 < 8 ? lodCount - 3 : 8;

    auto ssrTexSizeW = gameViewWidth; 
    auto ssrTexSizeH = gameViewHeight;

    Float4x4 warpToScreenSpaceMatrix = Float4x4::Identity();
    warpToScreenSpaceMatrix.m00 = static_cast<float>(ssrTexSizeW) * 0.5f;
    warpToScreenSpaceMatrix.m30 = static_cast<float>(ssrTexSizeW) * 0.5f;
    warpToScreenSpaceMatrix.m11 = static_cast<float>(ssrTexSizeH) * 0.5f;
    warpToScreenSpaceMatrix.m31 = static_cast<float>(ssrTexSizeH) * 0.5f;
    Float4x4 projectToPixelMatrix = projMatrix * warpToScreenSpaceMatrix;

    Float4 main_TexelSize = Float4(1.0f / static_cast<float>(gameViewWidth), 1.0f / static_cast<float>(gameViewHeight), static_cast<float>(gameViewWidth), static_cast<float>(gameViewHeight));
    Float4 marchTest_TexelSize = Float4(1.0f / static_cast<float>(ssrTexSizeW), 1.0f / static_cast<float>(ssrTexSizeH), static_cast<float>(ssrTexSizeW), static_cast<float>(ssrTexSizeH));
    Float2 randomSampler = GenerateRandomOffset();//Float2(0.5,0.5);
    Float4 noiseJitter = Float4(static_cast<float>(ssrTexSizeW) / 512.f, static_cast<float>(ssrTexSizeH) / 512.f, randomSampler.x, randomSampler.y);
    Float4 projectionParams = Float4(1.0f, nearPlane, farPlane, 1.0f / farPlane);
    Float3 hizLodLevelParams = Float3(static_cast<float>(lodCount - 1), static_cast<float>(0), static_cast<float>(0));

    //Trace pass
    auto* rayHitPdfView = CreateTextureView2D(
        "RayHitPDF Texture",
        ssrTexSizeW,
        ssrTexSizeH,
        GraphicsFormat::R16G16B16A16_SFloat,
        NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    auto* maskDepthHituvView = CreateTextureView2D(
        "MaskDepthHitUV Texture",
        ssrTexSizeW,
        ssrTexSizeH,
        GraphicsFormat::R16G16B16A16_SFloat,
        NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource);

    bool isLinearTrace = *mPostProcessMtl->GetBool("_IsLinearTrace");// false;
    bool isSingleSPP = *mPostProcessMtl->GetBool("_IsSingleSPP");
    if (isLinearTrace) {
        RenderContext linearTraceSinctx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        linearTraceSinctx.SetProperty("_InverseViewMatrix", invViewMatrix);
        linearTraceSinctx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
        linearTraceSinctx.SetProperty("_InverseViewProjectionMatrix", invViewProjMatrix);
        linearTraceSinctx.SetProperty("_MarchTest_TexelSize", marchTest_TexelSize);
        linearTraceSinctx.SetProperty("_NoiseJitter", noiseJitter);
        linearTraceSinctx.SetProperty("_ProjectionParams", projectionParams);
        linearTraceSinctx.SetProperty("_CameraPosWS", cameraPos);
        linearTraceSinctx.SetProperty("_SceneColorMap", sceneView);
        linearTraceSinctx.SetProperty("_SpecularRMap", specularRView);
        linearTraceSinctx.SetProperty("_NormalDepthMap", inNDView);
        std::vector<REDTextureView*> linearTraceViews = { rayHitPdfView, maskDepthHituvView };
        if (isSingleSPP) {
            PostProcess(mRED, linearTraceViews, mPostProcessMtl, "stochastic_ssr_linear2Dtrace_singleSPP", std::move(linearTraceSinctx));
        }
        else {
            PostProcess(mRED, linearTraceViews, mPostProcessMtl, "stochastic_ssr_linear2Dtrace_multiSPP", std::move(linearTraceSinctx));
        }
    }
    else
    {
        RenderContext hizTraceSinctx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        hizTraceSinctx.SetProperty("_InverseViewMatrix", invViewMatrix);
        hizTraceSinctx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
        hizTraceSinctx.SetProperty("_InverseViewProjectionMatrix", invViewProjMatrix);
        hizTraceSinctx.SetProperty("_ProjectionMatrix", projMatrix);
        hizTraceSinctx.SetProperty("_MarchTest_TexelSize", marchTest_TexelSize);
        hizTraceSinctx.SetProperty("_NoiseJitter", noiseJitter);
        hizTraceSinctx.SetProperty("_ProjectionParams", projectionParams);
        hizTraceSinctx.SetProperty("_CameraPosWS", cameraPos);
        hizTraceSinctx.SetProperty("_HizLodLevelParams", hizLodLevelParams);
        hizTraceSinctx.SetProperty("_SceneColorMap", sceneView);
        hizTraceSinctx.SetProperty("_HierarchicalDepthMap", hizView);
        hizTraceSinctx.SetProperty("_SpecularRMap", specularRView);
        hizTraceSinctx.SetProperty("_NormalDepthMap", inNDView);
        std::vector<REDTextureView*> hizTraceViews = { rayHitPdfView, maskDepthHituvView };
        if (isSingleSPP) {
            PostProcess(mRED, hizTraceViews, mPostProcessMtl, "stochastic_ssr_hiZtrace_singleSPP", std::move(hizTraceSinctx));
        }
        else{
            PostProcess(mRED, hizTraceViews, mPostProcessMtl, "stochastic_ssr_hiZtrace_multiSPP", std::move(hizTraceSinctx));//stochastic_ssr_linear2Dtrace_singleSPP
        }
    }

    //Resolve Pass
    auto resolveTexSizeW = gameViewWidth;
    auto resolveTexSizeH = gameViewHeight;
    auto resolveTexture = mRED->AllocateTexture("SSR Resolve Texture",
        NGITextureDesc
        {
            GraphicsFormat::R8G8B8A8_UNorm,
            NGITextureType::Texture2D,
            lodCount,
            1,
            resolveTexSizeW,
            resolveTexSizeH,
            1,
            1,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource
        });
    REDTextureView* tempResolveView[8];
    tempResolveView[0] = mRED->AllocateTextureView(resolveTexture,
        NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                           GraphicsFormat::R8G8B8A8_UNorm,
                           NGITextureType::Texture2D,
                           {
                               NGITextureAspect::Color,
                               0,
                               1,
                               0,
                               1,
                           } });
    Float4 resolve_TexelSize = Float4(1.0f / static_cast<float>(resolveTexSizeW), 1.0f / static_cast<float>(resolveTexSizeH), static_cast<float>(resolveTexSizeW), static_cast<float>(resolveTexSizeH));
    RenderContext resolveSinctx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    resolveSinctx.SetProperty("_InverseViewMatrix", invViewMatrix);
    resolveSinctx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
    resolveSinctx.SetProperty("_InverseViewProjectionMatrix", invViewProjMatrix);
    resolveSinctx.SetProperty("_MarchTest_TexelSize", marchTest_TexelSize);
    resolveSinctx.SetProperty("_Resolve_TexelSize", resolve_TexelSize);
    resolveSinctx.SetProperty("_NoiseJitter", noiseJitter);
    resolveSinctx.SetProperty("_ProjectionParams", projectionParams);
    resolveSinctx.SetProperty("_CameraPosWS", cameraPos);
    resolveSinctx.SetProperty("_SpecularRMap", specularRView);
    resolveSinctx.SetProperty("_NormalDepthMap", inNDView);
    resolveSinctx.SetProperty("_SceneColorMap", sceneView);
    resolveSinctx.SetProperty("_RayHitPdfMap", rayHitPdfView);
    resolveSinctx.SetProperty("_MaskDepthHituvMap", maskDepthHituvView);
    std::vector<REDTextureView*>  resolveViews = { tempResolveView[0] };
    if (isSingleSPP)
    {
        PostProcess(mRED, resolveViews, mPostProcessMtl, "stochastic_ssr_resolve_singleSPP", std::move(resolveSinctx));
    }
    else
    {
        PostProcess(mRED, resolveViews, mPostProcessMtl, "stochastic_ssr_resolve_multiSPP", std::move(resolveSinctx));
    }

    //MIPMAP
    for (UInt16 count = 0; count < 4; count++)
    {
        tempResolveView[count + 1] = mRED->AllocateTextureView(resolveTexture,
            NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                               GraphicsFormat::R8G8B8A8_UNorm,
                               NGITextureType::Texture2D,
                               {
                                   NGITextureAspect::Color,
                                   static_cast<UInt16>(count + 1),
                                   1,
                                   0,
                                   1,
                               } });
        RenderContext mipmapCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        mipmapCtx.SetProperty("_ResolveMap", tempResolveView[count]);
        mipmapCtx.SetProperty("_SpecularRMap", specularRView);
        mipmapCtx.SetProperty("_MarchTest_TexelSize", resolve_TexelSize);
        mipmapCtx.SetProperty("_CurrentMipLevel", static_cast<float>(count));
        std::vector<REDTextureView*> tempResolveViews = { tempResolveView[count + 1] };
        PostProcess(mRED, tempResolveViews, mPostProcessMtl, "ssr_mipmap", std::move(mipmapCtx));
    }

    REDTextureView* resolveView = mRED->AllocateTextureView(resolveTexture,
        NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                           GraphicsFormat::R8G8B8A8_UNorm,
                           NGITextureType::Texture2D,
                           {
                               NGITextureAspect::Color,
                               0,
                               lodCount,
                               0,
                               1,
                           } });

    //Combine Pass
    RenderContext combinectx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    combinectx.SetProperty("_InverseViewMatrix", invViewMatrix);
    combinectx.SetProperty("_InverseProjectionMatrix", invProjMatrix);
    combinectx.SetProperty("_InverseViewProjectionMatrix", invViewProjMatrix);
    combinectx.SetProperty("_MarchTest_TexelSize", marchTest_TexelSize);
    combinectx.SetProperty("_Main_TexelSize", main_TexelSize);
    combinectx.SetProperty("_NoiseJitter", noiseJitter);
    combinectx.SetProperty("_ProjectionParams", projectionParams);
    combinectx.SetProperty("_HizLodLevelParams", hizLodLevelParams);
    combinectx.SetProperty("_CameraPosWS", cameraPos);
    combinectx.SetProperty("_SpecularRMap", specularRView);
    combinectx.SetProperty("_NormalDepthMap", inNDView);
    combinectx.SetProperty("_SceneColorMap", sceneView);
    combinectx.SetProperty("_ReflectionColorMap", resolveView);
    std::vector<REDTextureView*> combineViews = { ssrView };
    PostProcess(mRED, combineViews, mPostProcessMtl, "stochastic_ssr_combine", std::move(combinectx));
}

void cross::BuiltInRenderPipeline::AssembleHiZ(RenderWorld* world, REDTextureView* depthStencilView, REDTextureView*& hiZView)
{
    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;

    auto* depthView = mRED->AllocateTextureView(depthStencilView->mTexture,
        NGITextureViewDesc{ NGITextureUsage::ShaderResource,
                           depthStencilView->mDesc.Format,
                           NGITextureType::Texture2D,
                           NGITextureSubRange{
                               NGITextureAspect::Depth,
                               0,
                               1,
                               0,
                               1,
                           } });


    UInt16 lodCount = static_cast<UInt16>(std::log(gameViewWidth < gameViewHeight ? gameViewWidth : gameViewHeight) / std::log(2));
    lodCount = lodCount < 8 ? lodCount : 8;
    auto hizTexture = mRED->AllocateTexture("Hierarchical Depth Texture",
        NGITextureDesc
        {
            GraphicsFormat::R16_UNorm,
            NGITextureType::Texture2D,
            lodCount,
            1,
            gameViewWidth,
            gameViewHeight,
            1,
            1,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource
        });

    hiZView = mRED->AllocateTextureView(hizTexture,
        NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                           GraphicsFormat::R16_UNorm,
                           NGITextureType::Texture2D,
                           {
                               NGITextureAspect::Color,
                               0,
                               lodCount,
                               0,
                               1,
                           } });
    REDTextureView* tempDepthView[8];
    tempDepthView[0] = mRED->AllocateTextureView(hizTexture,
        NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                           GraphicsFormat::R16_UNorm,
                           NGITextureType::Texture2D,
                           {
                               NGITextureAspect::Color,
                               0,
                               1,
                               0,
                               1,
                           } });

    RenderContext copyZCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    copyZCtx.SetProperty("_DepthMap", depthView);
    std::vector<REDTextureView*> copyZViews = { tempDepthView[0] };
    PostProcess(mRED, copyZViews, mPostProcessMtl, "copy_depth", std::move(copyZCtx));

    for (UInt16 index = 0; index < lodCount - 1; index++)
    {
        tempDepthView[index+1] = mRED->AllocateTextureView(hizTexture,
            NGITextureViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                               GraphicsFormat::R16_UNorm,
                               NGITextureType::Texture2D,
                               {
                                   NGITextureAspect::Color,
                                   static_cast<UInt16>(index + 1),
                                   1,
                                   0,
                                   1,
                               } });
        RenderContext hizCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        hizCtx.SetProperty("_DepthMap", tempDepthView[index]);
        //hizCtx.SetProperty("_HiZ_PrevDepthLevel", static_cast<float>(index));
        std::vector<REDTextureView*> hiZViews = { tempDepthView[index + 1] };
        PostProcess(mRED, hiZViews, mPostProcessMtl, "hierarchical_depth", std::move(hizCtx));
    }
}

void cross::BuiltInRenderPipeline::AssembleBloom_KinoMethod(RenderWorld* world, REDTextureView* sceneView, REDTextureView* bloomedView)
{
    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;

    auto CreateTextureView2D_Shortcut = [this](std::string_view name, UInt32 width, UInt32 height) {
        return CreateTextureView2D(name, width, height, GraphicsFormat::R11G11B10_UFloatPack32, NGITextureUsage::RenderTarget | NGITextureUsage::SubpassInput | NGITextureUsage::ShaderResource);
    };

    auto texWidth = gameViewWidth;
    auto texHeight = gameViewHeight;

    Float4 bloomThreshold;
    {
        const float k_Softness = 0.5f;
        float lthresh = 0.f;
        float knee = lthresh * k_Softness + 1e-5f;
        bloomThreshold = Float4(lthresh, lthresh - knee, knee * 2.f, 0.25f / knee);
    }

    auto* bloomPrefilterView = CreateTextureView2D_Shortcut("BloomPrefilter Texture", texWidth, texHeight);
    {
        Float2 tmpSize(1.f / texWidth, 1.f / texHeight);
        RenderContext bloomPrefilterCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        bloomPrefilterCtx.SetProperty("toBloom_texture", sceneView);
        bloomPrefilterCtx.SetProperty("TexSize", tmpSize);
        bloomPrefilterCtx.SetProperty("ThresholdPara", bloomThreshold);
        std::vector<REDTextureView*> bloomPrefilterViews = { bloomPrefilterView };
        PostProcess(mRED, bloomPrefilterViews, mPostProcessMtl, "bloom_prefilter", std::move(bloomPrefilterCtx));
    }

    std::vector<REDTextureView*> bloomDownSampledViewList(0);
    std::vector<std::pair<UInt32, UInt32>> texWH(0);
    REDTextureView* nowView = bloomPrefilterView;
    UInt16 downCnt = 0;
    constexpr UInt16 MaxDownSampleCnt = 3;

    while (texWidth > 32 && texHeight > 32 && downCnt < MaxDownSampleCnt)
    {
        Float2 HighResTexSize = Float2(1.f / texWidth, 1.f / texHeight);
        texWidth = (texWidth + 1) / 2;
        texHeight = (texHeight + 1) / 2;
        Float2 LowResTexSize = Float2(1.f / texWidth, 1.f / texHeight);
        texWH.push_back({texWidth, texHeight});
        std::string viewName = "BloomDownSample Texture 0";
        viewName.back() = static_cast<char>('0' + downCnt);
        auto* bloomDownSampledView = CreateTextureView2D_Shortcut(viewName, texWidth, texHeight);

        RenderContext bloomDownSampleCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
        bloomDownSampleCtx.SetProperty("toDownSample_texture", nowView);
        bloomDownSampleCtx.SetProperty("TexSize", HighResTexSize);
        bloomDownSampleCtx.SetProperty("PassIdx", static_cast<float>(downCnt));
        std::vector<REDTextureView*> bloomDownSampleViews = { bloomDownSampledView };
        PostProcess(mRED, bloomDownSampleViews, mPostProcessMtl, "bloom_down_sample", std::move(bloomDownSampleCtx));

        nowView = bloomDownSampledView;
        bloomDownSampledViewList.push_back(nowView);

        downCnt++;
    }

    for (size_t i = bloomDownSampledViewList.size() - 1; i >= 1; i--)
    {
        std::string viewName = "BloomUpSample Texture 0";
        viewName.back() = static_cast<char>('0' + i);
        auto* tmpView = CreateTextureView2D_Shortcut(viewName, texWH[i - 1].first, texWH[i - 1].second);
        Float2 LowResTexSize = Float2(1.f / texWH[i].first, 1.f / texWH[i].second);
        RenderContext bloomUpSampleCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());

        bloomUpSampleCtx.SetProperty("lowRes_texture", nowView);
        bloomUpSampleCtx.SetProperty("highRes_texture", bloomDownSampledViewList[i - 1]);
        bloomUpSampleCtx.SetProperty("TexSizeLow", LowResTexSize);
        std::vector<REDTextureView*> bloomUpSampleViews = { tmpView };
        PostProcess(mRED, bloomUpSampleViews, mPostProcessMtl, "bloom_up_sample", std::move(bloomUpSampleCtx));
        nowView = tmpView;
    }

    Float2 LowResTexSize = Float2(1.f / texWH[0].first, 1.f / texWH[0].second);
    RenderContext bloomMergeCtx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    bloomMergeCtx.SetProperty("bloomBlur_texture", nowView);
    bloomMergeCtx.SetProperty("toBloom_texture", sceneView);
    bloomMergeCtx.SetProperty("TexSizeLow", LowResTexSize);
    std::vector<REDTextureView*> bloomMergeViews = { bloomedView };
    PostProcess(mRED, bloomMergeViews, mPostProcessMtl, "bloom_merge", std::move(bloomMergeCtx));
}

void cross::BuiltInRenderPipeline::AssembleTAA_Deprecated(RenderWorld* world, REDTextureView* inputRenderView, REDTextureView* depthStencilView, REDTextureView*& taaView)
{
    auto gameViewWidth = mTargetView->mTexture->mDesc.Width;
    auto gameViewHeight = mTargetView->mTexture->mDesc.Height;

    NGITextureViewDesc taaTexViewDesc{ NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                      GraphicsFormat::R16G16B16A16_SFloat,
                                      NGITextureType::Texture2D,
                                      {
                                          NGITextureAspect::Color,
                                          0,
                                          1,
                                          0,
                                          1,
                                      } };

    // prepare history buffer input
    REDTextureView* accTexView;
    if (mRED->Validate(mTAAAccTex))
    {
        // history buffer was valid(lifetime was extended)
        if (mTAAAccTex->mDesc.Width != gameViewWidth || mTAAAccTex->mDesc.Height != gameViewHeight)
        {
            // size not match, use color input as input history buffer
            accTexView = inputRenderView;
        }
        else
        {
            // size match
            accTexView = mRED->AllocateTextureView(mTAAAccTex, taaTexViewDesc);
        }
    }
    else
    {
        // history buffer was not valid(the frame when TAA was enabled), use color input as input history buffer
        accTexView = inputRenderView;
    }

    // prepare history buffer output
    {
        NGITextureDesc desc{
            GraphicsFormat::R16G16B16A16_SFloat,
            NGITextureType::Texture2D,
            1,
            1,
            gameViewWidth,
            gameViewHeight,
            1,
            1,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
        };
        mTAAAccTex = mRED->AllocateTexture("TAA History Buffer", desc);
        // extend lifetime to next frame
        mTAAAccTex->ExtendLifetime();
        taaView = mRED->AllocateTextureView(mTAAAccTex, taaTexViewDesc);
    }

    auto* depthView = mRED->AllocateTextureView(depthStencilView->mTexture,
        NGITextureViewDesc{ NGITextureUsage::ShaderResource,
                           mDepthStencilFormat,
                           NGITextureType::Texture2D,
                           {
                               NGITextureAspect::Depth,
                               0,
                               1,
                               0,
                               1,
                           } });

    auto reprojectionMat = GetJitterData()->GetReprojectionMatrixNoJitter();
    RenderContext ctx(EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator());
    ctx.SetProperty("_MainMap", inputRenderView);
    ctx.SetProperty("_DepthMap", depthView);
    ctx.SetProperty("_AccumMap", accTexView);
    ctx.SetProperty("_ReprojectionMat", reprojectionMat);
    ctx.SetProperty("_CurrentFrameWeight", GetSetting()->TAACurrentFrameWeight);
    std::vector<REDTextureView*> taaViews{ taaView };
    PostProcess(mRED, taaViews, mPostProcessMtl, "taa_render", std::move(ctx));
}
