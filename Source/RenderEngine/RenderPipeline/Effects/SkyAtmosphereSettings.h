#pragma once
#include "PassBase.h"
#include "Resource/Material.h"

namespace cross {

class CEMeta(Editor, Reflect) RENDER_ENGINE_API SkyAtmosphereAdvancedVars : public PassSetting
{
public:
    CEProperty(<PERSON><PERSON><PERSON>, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkySampleCountMin = 2.0f;
    CEProperty(Serialize, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkySampleCountMax = 32.0f;
    CEProperty(Serialize, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkyDistanceToSampleCountMax = 150.0f;

    CEProperty(Serialize, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkyViewLUTSampleCountMin = 4.0f;
    CEProperty(Serialize, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkyViewLUTSampleCountMax = 32.0f;
    CEProperty(Serialize, Editor, Script, Reflect) CECSAttribute(PropertyInfo(PropertyType = "Auto", bHide = true)) float SkyViewLUTDistanceToSampleCountMax = 150.0f;

    CE_Virtual_Serialize_Deserialize;
};
 inline bool operator==(const SkyAtmosphereAdvancedVars& lhs, const SkyAtmosphereAdvancedVars& rhs)
{
    return lhs.SkySampleCountMin == rhs.SkySampleCountMin && lhs.SkySampleCountMax == rhs.SkySampleCountMax && lhs.SkyDistanceToSampleCountMax == rhs.SkyDistanceToSampleCountMax &&
           lhs.SkyViewLUTSampleCountMin == rhs.SkyViewLUTSampleCountMin && lhs.SkyViewLUTSampleCountMax == rhs.SkyViewLUTSampleCountMax && lhs.SkyViewLUTDistanceToSampleCountMax == rhs.SkyViewLUTDistanceToSampleCountMax;
}

}   // namespace cross