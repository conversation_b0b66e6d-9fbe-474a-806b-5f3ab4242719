#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include <queue>
#include <vector>
namespace cross {
class ParticleInstanceCountManager
{
public:
    ParticleInstanceCountManager(UInt32 emitterIndex);
    ~ParticleInstanceCountManager();
    void ClearInstanceCounterBuffer();

    REDBufferView* GetInstanceCounterBufferView()
    {
        Assert(mInstanceCounterBufferView);
        return mInstanceCounterBufferView.get();
    }

    REDBufferView* GetIndirectArgBufferViewUAV()
    {
        Assert(mIndirectArgBufferViewUAV);
        return mIndirectArgBufferViewUAV.get();
    }

    REDBufferView* GetIndirectArgBufferViewSRV()
    {
        return mIndirectArgBufferViewSRV.get();
    }

    REDBufferRef GetIndirectArgBuffer()
    {
        return mIndirectArgBuffer.get();
    }

    REDResidentBuffer* GetInstanceCounterBuffer() const { return mInstanceCounterBuffer.get(); }

    void CopyInstanceCount();
    SInt32 GetInstanceCount();
    UInt32 GetEmitterIndex() const { return mEmitterIndex; }

    REDBufferRef GetGlobalInstanceCounterBuffer() const { return mGlobalInstanceCounterBuffer; }
    void SetGlobalInstanceCounterBuffer(REDBufferRef buffer) { mGlobalInstanceCounterBuffer = buffer; }
    SInt32 GetGlobalInstanceCounterBufferOffset() const { return mGlobalInstanceCounterBufferOffset; }
    void SetGlobalInstanceCounterBufferOffset(SInt32 offset) { mGlobalInstanceCounterBufferOffset = offset; }
    REDBufferView* GetGlobalInstanceCounterBufferView() const { return mGlobalInstanceCounterBufferView; }
    void SetGlobalInstanceCounterBufferView(REDBufferView* view) { mGlobalInstanceCounterBufferView = view; }

    void CopyToGlobalInstanceCounterBuffer();

 public:
    static const UInt32 sStaticInvalidValue = 0xFFFFFFFF;

private:
    RenderingExecutionDescriptor* mRED = nullptr;
    REDUniquePtr<REDResidentBuffer> mInstanceCounterBuffer = nullptr;
    REDUniquePtr<REDResidentBuffer> mIndirectArgBuffer = nullptr;
    REDUniquePtr<REDResidentBufferView> mInstanceCounterBufferView = nullptr;
    REDUniquePtr<REDResidentBufferView> mIndirectArgBufferViewUAV = nullptr;
    REDUniquePtr<REDResidentBufferView> mIndirectArgBufferViewSRV = nullptr;
    const SizeType mInstanceCounterNum = 4;
    std::queue<std::pair<int, NGIStagingBuffer*>> mPendingCopyBackTasks;
    std::vector<NGIStagingBuffer*> mStagingBufferPool;
    UInt32 mEmitterIndex;

    REDBufferRef mGlobalInstanceCounterBuffer;
    REDBufferView* mGlobalInstanceCounterBufferView = nullptr;
    SInt32 mGlobalInstanceCounterBufferOffset = -1;


};
}
