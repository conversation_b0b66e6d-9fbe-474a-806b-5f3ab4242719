#include "NativeGraphicsInterface/NGI.h"
#include "CECommon/Common/GlobalSystemDesc.h"

#include "VirtualTextureFeedback.h"
#include "VirtualTextureSystemR.h"
#include "VirtualTextureMath.h"
#include "UniquePageList.h"
#include "UniqueRequestList.h"
#include "TexturePagePool.h"
#include "VirtualTextureSpace.h"
#include "AllocatedVirtualTexture.h"
#include "RenderEngine/ComputeShaderR.h"
#include "NGIManager.h"
namespace cross {
class VTFeedbackAnalysisTask
{
public:
    static void DoTask(VirtualTextureSystemR::VTFeedbackAnalysisParameters& inParams)
    {
        SCOPED_CPU_TIMING(GroupRendering, "VTFeedbackAnalysisTask");
        //inParams.uniquePageList->Initialize(true);
        inParams.system->FeedbackAnalysisTask(inParams);
    }
};

class VTGatherRequestsTask
{
public:
    static void DoTask(VirtualTextureSystemR::VTGatherRequestsParameters & parameters)
    {
        SCOPED_CPU_TIMING(GroupRendering, "VTGatherRequestsTask");
        //parameters.requestList->Initialize();
        parameters.system->GatherRequestsTask(parameters);
    }
};

static float ComputeMipLevel(const IAllocatedVirtualTexture* allocatedVT, const UInt2& inScreenSpaceSize)
{
    const UInt32 textureWidth = allocatedVT->GetWidthInPixels();
    const UInt32 textureHeight = allocatedVT->GetHeightInPixels();
    const Float2 dfdx(static_cast<float>(textureWidth / inScreenSpaceSize.x), 0.0f);
    const Float2 dfdy(0.0f, static_cast<float>(textureHeight / inScreenSpaceSize.y));
    const float ppx = dfdx.x * dfdx.x + dfdx.y * dfdx.y;
    const float ppy = dfdy.x * dfdy.x + dfdy.y * dfdy.y;
    return 0.5f * static_cast<float>(Log2(std::max(ppx, ppy)));
}

static FORCEINLINE UInt32 EncodePage(UInt32 id, UInt32 vLevel, UInt32 vTileX, UInt32 vTileY)
{
    const UInt32 vLevelPlus1 = vLevel + 1u;

    UInt32 page;
    page = vTileX << 0;
    page |= vTileY << 12;
    page |= vLevelPlus1 << 24;
    page |= id << 28;
    return page;
}

VirtualTextureSystemR::VirtualTextureSystemR() 
    : mFrame(1u)   // Need to start on Frame 1, otherwise the first call to update will fail to allocate any pages
    , bFlushCaches(false)
{
}

VirtualTextureSystemR::~VirtualTextureSystemR() 
{
}

const GlobalSystemDesc& VirtualTextureSystemR::GetDesc()
{
    static const GlobalSystemDesc* sDesc{nullptr};
    if (!sDesc)
    {
        auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
        sDesc = descSystem->CreateOrGetGlobalSystemDesc("VirtualTextureSystemR", false);
    }
    return *sDesc;
}

RENDER_ENGINE_API VirtualTextureSystemR* VirtualTextureSystemR::CreateInstance()
{
    return new VirtualTextureSystemR();
}

void VirtualTextureSystemR::Release() 
{
    delete this;
}

void VirtualTextureSystemR::OnFirstUpdate(FrameParam* frameParam) 
{
}

void VirtualTextureSystemR::OnBeginFrame(FrameParam* frameParam) 
{
    auto* frameAllocator = frameParam->GetFrameAllocator();

    mPendingDeleteBuffers = frameAllocator->CreateFrameContainer<FrameVector<NGIStagingBuffer*>>(FRAME_STAGE_GAME_RENDER, 100);
}
void VirtualTextureSystemR::OnEndFrame(FrameParam* frameParam) 
{
    for (UInt32 i = 0; i < mPendingDeleteBuffers->GetSize(); i++)
    {
        auto staging = mPendingDeleteBuffers->At(i);
        delete staging;
    }
    mPendingDeleteBuffers = nullptr;
}
void VirtualTextureSystemR::NotifyShutdownEngine() 
{
    if (mMergedUniquePageList)
    {
        delete mMergedUniquePageList;
        mMergedUniquePageList = nullptr;
    }

    if (mMergedRequestList)
    {
        mMergedRequestList->Release();
        mMergedRequestList = nullptr;
    }
    delete mMergedRequestList;

    for (int i = 0; i < MaxSpaces; i++)
    {
        mSpaces[i].reset();
    }
    
    for (int i = 0; i < MaxNumTasks; i++)
    {
        if (feedbackAnalysisParameters[i].uniquePageList)
        {
            feedbackAnalysisParameters[i].uniquePageList = nullptr;
        }

        if (gatherRequestsParameters[i].requestList)
        {
            gatherRequestsParameters[i].requestList->Release();
            delete gatherRequestsParameters[i].requestList;
            gatherRequestsParameters[i].requestList = nullptr;
            gatherRequestsParameters[i].pageUpdateBuffers.clear();
        }
    }
    DestroyPendingVirtualTextures(true);
}
void VirtualTextureSystemR::FlushVirtualTextureCache()
{
    bFlushCaches = true;
}

void VirtualTextureSystemR::AllocateResources()
{
    for (UInt8 ID = 0; ID < MaxSpaces; ID ++)
    {
        if (mSpaces[ID] != nullptr)
        {
            mSpaces[ID]->AllocateTextures();
        }
    }
}

void VirtualTextureSystemR::Update(RenderingExecutionDescriptor* RED, RenderWorld* World, ComputeShaderR* VTCS, UInt32 currentframe, VTFeedbackManager* feedbackManager)
{
    if (bFlushCaches) 
    {
        std::unique_lock lock(mPhysicalSpaceMutex);
        for (UInt32 i = 0; i < mPhysicalSpaces.size(); ++i)
        {
            VTPhysicalSpace* physicalSpace = mPhysicalSpaces[i];
            if (physicalSpace)
            {
                // Flush unlocked pages
                physicalSpace->GetPagePool().EvictAllPages(this);
            }
        }
        // doesn't need clear pagetabe when evict;
        // we will generate erase pageupdate to write pagetable to zero
       // for (UInt8 ID = 0; ID < MaxSpaces; ID++)
       // {
       //     if (mSpaces[ID] != nullptr)
       //     {
       //         mSpaces[ID]->SetPagetableCleanedState(false);
       //     }
       //  }
        bFlushCaches = false;
    }

    DestroyPendingVirtualTextures(false);

    // Early out when no allocated VTs
    if (mAllocatedVTs.size() == 0)
    {
        mMappedTilesToProduce.clear();
        return;
    }

    // PageList
    if (mMergedUniquePageList == nullptr)
    {
        mMergedUniquePageList = new VTUniquePageList();
    }
    mMergedUniquePageList->Initialize(true);
    {
        SCOPED_CPU_TIMING(GroupRendering, "VTGatherFeedBack");
        // Fetch feedback for analysis
        VTFeedbackManager::MapResult feedbackResult;
        {
            feedbackResult = feedbackManager->Map(currentframe);
            if (feedbackResult.data.size() == 0 && mTilesToLock.size() == 0)
                return;
        }

        // Create tasks to read the feedback data
        
        const UInt32 maxNumFeedbackTasks = 8;   // [1, MaxNumTasks]
        const UInt32 feedbackSizePerTask = VTMath::DivideAndRoundUp(static_cast<UInt32>(feedbackResult.data.size()), maxNumFeedbackTasks);

        UInt32 numFeedbackTasks = 0;
        UInt32 currentOffset = 0;
        while (currentOffset < feedbackResult.data.size())
        {
            const UInt32 taskIndex = numFeedbackTasks++;
            VTFeedbackAnalysisParameters& params = feedbackAnalysisParameters[taskIndex];
            params.system = this;
            if (taskIndex == 0u)
            {
                params.uniquePageList = mMergedUniquePageList;
            }
            else
            {
                if (params.uniquePageList == nullptr)
                {
                    params.uniquePageList = new VTUniquePageList();
                }
                params.uniquePageList->Initialize(true);
            }
            params.feedbackBuffer = feedbackResult.data.data() + currentOffset;

            const UInt32 Size = std::min(feedbackSizePerTask, static_cast<UInt32>(feedbackResult.data.size()) - currentOffset);
            params.feedbackSize = Size;
            currentOffset += Size;
        }

        // do the tasks
        //const bool bParallelTasks = true;
        //const SInt32 localFeedbackTaskCount = bParallelTasks ? 1 : numFeedbackTasks;

        if (numFeedbackTasks > 0)
        {
            threading::ParallelFor(static_cast<SInt32>(numFeedbackTasks), [&](SInt32 taskIndex) {
                    VTFeedbackAnalysisTask::DoTask(feedbackAnalysisParameters[taskIndex]);
                });
        }

        // task num > 1 merge pagelist
        if (numFeedbackTasks > 1u)
        {
            for (UInt32 taskIndex = 1u; taskIndex < numFeedbackTasks; ++taskIndex)
            {
                mMergedUniquePageList->MergePages(feedbackAnalysisParameters[taskIndex].uniquePageList);
                //delete feedbackAnalysisParameters[taskIndex].uniquePageList;
                //feedbackAnalysisParameters[taskIndex].uniquePageList = nullptr;
            }
        }

        feedbackManager->Unmap(feedbackResult.mapHandle);
    }

    if (mMergedRequestList == nullptr)
    {
        mMergedRequestList = new VTUniqueRequestList();
    }
   
    mMergedRequestList->Initialize();
    // Collect tiles to lock
    {
        SCOPED_CPU_TIMING(GroupRendering, "CollectTilestoLock");
        std::vector<VTLocalTile> remainingTilesToLock;
        for (const auto& tile : mTilesToLock) 
        {
            const VTProducerHandle producerHandle = tile.GetProducerHandle();
            const VirtualTextureProducer* producer = mProducers.FindProducer(producerHandle);

            Assert(mTileLocks.IsLocked(tile));
            if (producer) 
            {
                // get physics address, set unfree in pagepool
                UInt8 producerLayerMaskToLoad = 0u;
                for (UInt32 producerLayerIndex = 0u; producerLayerIndex < producer->GetNumTextureLayers(); producerLayerIndex++) 
                {
                    UInt32 groupIndex = producer->GetPhysicalGroupIndexForTextureLayer(producerLayerIndex);
                    VTPhysicalSpace* physicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(groupIndex);
                    
                    VTPagePool& pagePool = physicalSpace->GetPagePool();
                    const UInt32 pAddress = pagePool.FindPageAddress(producerHandle, static_cast<UInt8>(groupIndex), tile.local_vAddress, static_cast<UInt8>(tile.local_vLevel));

                    if (pAddress == ~0u)
                    {
                        producerLayerMaskToLoad |= (1u << producerLayerIndex);      // maybe overflow the request list, try lock tile next frame
                    }
                    else
                    {
                        pagePool.Lock(static_cast<UInt16>(pAddress));    // freeHeap remove
                    }
                }

                if (producerLayerMaskToLoad != 0u)
                {
                    const UInt16 loadRequestIndex = mMergedRequestList->LockLoadRequest(VTLocalTile(tile.GetProducerHandle(), tile.local_vAddress, tile.local_vLevel), producerLayerMaskToLoad);
                    if (loadRequestIndex == 0xffff)
                    {
                        // Overflowed the request list...try to lock the tile again next frame
                        remainingTilesToLock.push_back(tile);
                    }
                }
            }
        }
        mTilesToLock = std::move(remainingTilesToLock);
    }

    // Gather Request
    std::vector<UInt32> packedTiles;
    if (mRequestedPackedTiles.size() > 0) {
        packedTiles = std::move(mRequestedPackedTiles);
        mRequestedPackedTiles.clear();
    }

    if (packedTiles.size() > 0)
    {
        // Collect explicitly requested tiles
        // These tiles are generated on the current frame, so they are collected/processed in a separate list
        VTUniquePageList* requestedPageList = new VTUniquePageList();
        requestedPageList->Initialize();
        for (UInt32 Tile : packedTiles)
        {
            requestedPageList->Add(Tile, 0xffff);
        }
        GatherRequests(mFrame);

        delete requestedPageList;
    }

    // Pages from feedback buffer were generated several frames ago, so they may no longer be valid for newly allocated VTs
    static UInt32 pendingFrameDelay = 3u;
    if (mFrame >= pendingFrameDelay)
    {
        GatherRequests(mFrame - pendingFrameDelay);
    }

    // Sort Request
    {
        // Limit the number of uploads (account for MappedTilesToProduce this frame)
        // Are all pages equal? Should there be different limits on different types of pages?
        const SInt32 maxNumUploads = 32;        // Max number of page uploads per frame when in editor
        const SInt32 maxRequestUploads = std::max(static_cast<SInt32>(maxNumUploads - mMappedTilesToProduce.size()), 1);

        mMergedRequestList->SortRequests(mProducers, maxRequestUploads);
    }

    // Submit the requests to produce pages that are already mapped, prefetch operate
    SubmitPreMappedRequests();
    // Submit the merged requests, main submit
    SubmitRequests(RED, World, VTCS, mMergedRequestList, true);
    // Release
    ReleaseResource();
}

void VirtualTextureSystemR::FeedbackAnalysisTask(VTFeedbackAnalysisParameters& parameters) 
{
    VTUniquePageList* requestedPageList = parameters.uniquePageList;
    const UInt32* buffer = parameters.feedbackBuffer;
    const UInt32 bufferSize = parameters.feedbackSize;

    UInt32 lastPixel = 0xffffffff;
    UInt32 lastCount = 0;

    for (UInt32 index = 0; index < bufferSize; index++)
    {
        const UInt32 pixel = buffer[index];
        if (pixel == lastPixel)
        {
            lastCount++;
            continue;
        }

        if (lastPixel != 0xffffffff)
        {
            requestedPageList->Add(lastPixel, lastCount);
        }

        lastPixel = pixel;
        lastCount = 1;
    }

    if (lastPixel != 0xffffffff)
    {
        requestedPageList->Add(lastPixel, lastCount);
    }
}

void VirtualTextureSystemR::GatherRequestsTask(VTGatherRequestsParameters& parameters)
{
    const VTUniquePageList* uniquePageList = parameters.uniquePageList;
    std::vector<VTPageUpdateBuffer> & pageUpdateBuffers = parameters.pageUpdateBuffers;
    VTUniqueRequestList* requestList = parameters.requestList;
    const UInt32 pageUpdateFlushCount = parameters.pageUpdateFlushCount;
    UInt32 pageEndIndex = parameters.pageStartIndex + parameters.numPages;

    UInt32 numRequestsPages = 0u;
    UInt32 numResidentPages = 0u;
    UInt32 numNonResidentPages = 0u;
    UInt32 numPrefetchPages = 0u;

    //const bool bForceContinuousUpdate = false;

    SCOPED_CPU_TIMING(GroupRendering, "GatherRequestsTask_");
    for (UInt32 i = parameters.pageStartIndex; i < pageEndIndex; ++i)
    {
        const UInt32 pageEncoded = uniquePageList->GetPage(i);
        const UInt16 pageCount = uniquePageList->GetCount(i);

        // Decode page
        const UInt8 id = static_cast<UInt8>((pageEncoded >> 28));
        const VTSpace* space = GetSpace(id);
        if (space == nullptr)
        {
            continue;
        }

        const UInt32 vLevelPlusOne = ((pageEncoded >> 24) & 0x0f);
        const UInt8 vLevel = static_cast<UInt8>(std::max(vLevelPlusOne, 1u) - 1);

        // vPageX/Y passed from shader are relative to the given vLevel, we shift them up so be relative to level0
        const UInt32 vPageX = (pageEncoded & 0xfff) << vLevel;
        const UInt32 vPageY = ((pageEncoded >> 12) & 0xfff) << vLevel;

        const UInt32 vAddress = VTMath::MortonCode2(vPageX) | (VTMath::MortonCode2(vPageY) << 1);
        //printf("vPageX = %d  vPageY = %d vAddress = %d\n", vPageX, vPageY, vAddress);

        UInt8 pageTableLayersToLoad[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {0};
        UInt32 numPageTableLayersToLoad = 0u;
        {
            const VTPage virtualPage(vLevel, vAddress);
            const UInt16 virtualPageHash = static_cast<UInt16>(VTMath::MurmurFinalize32(virtualPage.packed));
            for (UInt8 pageTableLayerIndex = 0u; pageTableLayerIndex < space->GetNumPageTableLayers(); ++pageTableLayerIndex)
            {
                const VTPageMap& pageMap = space->GetPageMapForPageTableLayer(pageTableLayerIndex);

                ++numRequestsPages;
                const PhysicalSpaceIDAndAddress physicalSpaceIDAndAddress = pageMap.FindPagePhysicalSpaceIDAndAddress(virtualPage, virtualPageHash);
                if (physicalSpaceIDAndAddress.packed != ~0u)
                {
                    // Page is already resident, just need to update LRU free list
                    AddPageUpdate(pageUpdateBuffers, pageUpdateFlushCount, physicalSpaceIDAndAddress.physicalSpaceID, physicalSpaceIDAndAddress.pAddress);
                    ++numResidentPages;
                }
                else
                {
                    // Page not resident, store for later processing
                    pageTableLayersToLoad[numPageTableLayersToLoad++] = pageTableLayerIndex;
                }
            }
        }

        if (numPageTableLayersToLoad == 0u)
        {
            // All pages are resident and properly mapped, we're done
            // This is the fast path, as most frames should generally have the majority of tiles already mapped
            continue;
        }
        // Need to resolve allocatedVT in order to determine which pages to load
        const AllocatedVirtualTexture* allocatedVT = space->GetAllocator().Find(vAddress);
        if (!allocatedVT)
        {
            printf("Space %i, vAddr %i@%i (%d, %d) is not allocated to any AllocatedVT but was still requested.\n", id, vAddress, vLevel, vPageX, vPageY);
            continue;
        }

        if (allocatedVT->GetFrameAllocated() > parameters.frameRequested)
        {
            // If the VT was allocated after the frame that generated this feedback, it's no longer valid
            continue;
        }

        // Build producer local layer masks from physical layers that we need to load
        UInt8 producerGroupMaskToLoad[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {0u};
        UInt8 producerTextureLayerMaskToLoad[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {0u};
        const UInt32 numUniqueProducers = allocatedVT->GetNumUniqueProducers();
        for (UInt32 loadPageTableLayerIndex = 0u; loadPageTableLayerIndex < numPageTableLayersToLoad; ++loadPageTableLayerIndex)
        {
            const UInt32 pageTableLayerIndex = pageTableLayersToLoad[loadPageTableLayerIndex];
            const UInt32 producerIndex = allocatedVT->GetProducerIndexForPageTableLayer(pageTableLayerIndex);
            Assert(producerIndex < numUniqueProducers);

            const UInt32 producerTextureLayerMask = allocatedVT->GetProducerTextureLayerMaskForPageTableLayer(pageTableLayerIndex);
            producerTextureLayerMaskToLoad[producerIndex] |= producerTextureLayerMask;

            const UInt32 producerPhysicalGroupIndex = allocatedVT->GetProducerPhysicalGroupIndexForPageTableLayer(pageTableLayerIndex);
            producerGroupMaskToLoad[producerIndex] |= 1 << producerPhysicalGroupIndex;
        }

        const UInt32 vDimensions = space->GetDimensions();
        const UInt32 allocatedPageX = allocatedVT->GetVirtualPageX();
        const UInt32 allocatedPageY = allocatedVT->GetVirtualPageY();

        Assert(vAddress >= allocatedVT->GetVirtualAddress());
        Assert(vPageX >= allocatedPageX);
        Assert(vPageY >= allocatedPageY);

        const UInt8 maxLevel = static_cast<UInt8>(allocatedVT->GetMaxLevel());
        for (UInt32 producerIndex = 0u; producerIndex < numUniqueProducers; ++producerIndex)
        {
            UInt8 groupMaskToLoad = producerGroupMaskToLoad[producerIndex];
            if (groupMaskToLoad == 0u)
            {
                continue;
            }

            const VTProducerHandle producerHandle = allocatedVT->GetUniqueProducerHandle(producerIndex);
            const VirtualTextureProducer* producer = mProducers.FindProducer(producerHandle);
            if (!producer)
            {
                continue;
            }

            const UInt8 producerMipBias = allocatedVT->GetUniqueProducerMipBias(producerIndex);
            const UInt8 producerMaxLevel = static_cast<UInt8>(producer->GetMaxLevel());

            // here vLevel is clamped against producerMipBias, as producerMipBias represents the most detailed level of this producer, relative to the allocated VT
            // used to rescale vAddress to the correct tile within the given mip level
            UInt8 mapping_vLevel = std::max(vLevel, producerMipBias);

            // local_vLevel is the level within the producer that we want to allocate/map
            // here we subtract producerMipBias, which effectively matches more detailed mips of lower resolution producers with less detailed mips of higher resolution producers
            UInt8 local_vLevel = mapping_vLevel - producerMipBias;

            const UInt32 local_vPageX = (vPageX - allocatedPageX) >> mapping_vLevel;
            const UInt32 local_vPageY = (vPageY - allocatedPageY) >> mapping_vLevel;
            UInt32 local_vAddress = VTMath::MortonCode2(local_vPageX) | (VTMath::MortonCode2(local_vPageY) << 1);

            UInt8 producerPhysicalGroupMaskToPrefetchForLevel[16] = {0u};
            UInt8 maxPrefetchLocal_vLevel = local_vLevel;

            // Iterate local layers that we found unmapped
            for (UInt8 producerGroupIndex = 0u; producerGroupIndex < static_cast<UInt8>(producer->GetNumPhysicalGroups()); ++producerGroupIndex)
            {
                if ((groupMaskToLoad & (1u << producerGroupIndex)) == 0u)
                {
                    continue;
                }

                const VTPhysicalSpace* physicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(producerGroupIndex);
                const VTPagePool& pagePool = physicalSpace->GetPagePool();

                // Find the highest resolution tile that's currently loaded
                const UInt32 allocated_pAddress = pagePool.FindNearestPageAddress(producerHandle, producerGroupIndex, local_vAddress, local_vLevel, producerMaxLevel);

                bool bRequestedPageWasResident = false;
                UInt8 allocatedLocal_vLevel = maxLevel;
                if (allocated_pAddress != ~0u)
                {
                    allocatedLocal_vLevel = pagePool.GetLocalLevelForAddress(static_cast<UInt16>(allocated_pAddress));
                    Assert(allocatedLocal_vLevel >= local_vLevel);

                    const UInt8 allocatedMapping_vLevel = allocatedLocal_vLevel + producerMipBias;
                    UInt8 allocated_vLevel = std::min(allocatedMapping_vLevel, maxLevel);
                    if (allocatedLocal_vLevel == local_vLevel)
                    {
                        // page at the requested level was already resident, no longer need to load
                        bRequestedPageWasResident = true;
                        // We can map the already resident page at the original requested vLevel
                        // This may be different from allocated_vLevel when various biases are involved
                        // Without this, we'll never see anything mapped to the original requested level
                        allocated_vLevel = vLevel;
                        groupMaskToLoad &= ~(1u << producerGroupIndex);
                        ++numResidentPages;
                    }

                    Assert(allocated_vLevel <= maxLevel);
                    const UInt32 allocated_vAddress = vAddress & (0xffffffff << (allocated_vLevel * vDimensions));

                    AddPageUpdate(pageUpdateBuffers, pageUpdateFlushCount, physicalSpace->GetID(), static_cast<UInt16>(allocated_pAddress));

                    const PhysicalSpaceIDAndAddress physicalSpaceIDAndAddress(physicalSpace->GetID(), static_cast<UInt16>(allocated_pAddress));
                    UInt32 numMappedPages = 0u;
                    for (UInt32 loadLayerIndex = 0u; loadLayerIndex < numPageTableLayersToLoad; ++loadLayerIndex)
                    {
                        const UInt8 pageTableLayerIndex = pageTableLayersToLoad[loadLayerIndex];
                        if (allocatedVT->GetProducerPhysicalGroupIndexForPageTableLayer(pageTableLayerIndex) == producerGroupIndex && allocatedVT->GetProducerIndexForPageTableLayer(pageTableLayerIndex) == producerIndex)
                        {
                            // if we found a lower resolution tile than was requested, it may have already been mapped, check for that first
                            const VTPageMap& pageMap = space->GetPageMapForPageTableLayer(pageTableLayerIndex);
                            const PhysicalSpaceIDAndAddress prevPhysicalSpaceIDAndAddress = pageMap.FindPagePhysicalSpaceIDAndAddress(allocated_vLevel, allocated_vAddress);

                            if (prevPhysicalSpaceIDAndAddress.packed == ~0u)
                            {
                                // map the page now if it wasn't already mapped
                                requestList->AddDirectMappingRequest(space->GetID(), physicalSpace->GetID(), pageTableLayerIndex, maxLevel, allocated_vAddress, allocated_vLevel, allocatedMapping_vLevel, static_cast<UInt16>(allocated_pAddress));
                            }
                            ++numMappedPages;
                        }
                    }
                    Assert(numMappedPages > 0u);
                }

                if (!bRequestedPageWasResident)
                {
                    // page not resident...see if we want to prefetch a page with resolution incrementally larger than what's currently resident
                    // this means we'll ultimately load more data, but these lower resolution pages should load much faster than the requested high resolution page
                    // this should make popping less noticeable
                    UInt8 prefetchLocal_vLevel = allocatedLocal_vLevel - std::min<UInt8>(1, allocatedLocal_vLevel);
                    prefetchLocal_vLevel = std::min<UInt8>(prefetchLocal_vLevel, maxLevel - producerMipBias);
                    if (prefetchLocal_vLevel > local_vLevel)
                    {
                        producerPhysicalGroupMaskToPrefetchForLevel[prefetchLocal_vLevel] |= (1u << producerGroupIndex);
                        maxPrefetchLocal_vLevel = std::max(maxPrefetchLocal_vLevel, prefetchLocal_vLevel);
                        ++numPrefetchPages;
                    }
                    ++numNonResidentPages;
                }
            }

            // Check to see if we have any levels to prefetch
            for (UInt8 prefetchLocal_vLevel = local_vLevel + 1u; prefetchLocal_vLevel <= maxPrefetchLocal_vLevel; ++prefetchLocal_vLevel)
            {
                UInt8 producerPhysicalGroupMaskToPrefetch = producerPhysicalGroupMaskToPrefetchForLevel[prefetchLocal_vLevel];
                if (producerPhysicalGroupMaskToPrefetch != 0u)
                {
                    const UInt32 prefetchLocal_vAddress = local_vAddress >> ((prefetchLocal_vLevel - local_vLevel) * vDimensions);

                    // If we want to prefetch any layers for a given level, need to ensure that we request all the layers that aren't currently loaded
                    // This is required since the VT producer interface needs to be able to write data for all layers if desired, so we need to make sure that all layers are allocated
                    for (UInt8 producerPhysicalGroupIndex = 0u; producerPhysicalGroupIndex < producer->GetNumPhysicalGroups(); ++producerPhysicalGroupIndex)
                    {
                        if ((producerPhysicalGroupMaskToPrefetch & (1u << producerPhysicalGroupIndex)) == 0u)
                        {
                            const VTPhysicalSpace* physicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
                            const VTPagePool& PagePool = physicalSpace->GetPagePool();
                            const UInt16 pAddress = static_cast<UInt16>(PagePool.FindPageAddress(producerHandle, producerPhysicalGroupIndex, prefetchLocal_vAddress, prefetchLocal_vLevel));
                            if (pAddress == 65535)
                            {
                                producerPhysicalGroupMaskToPrefetch |= (1u << producerPhysicalGroupIndex);
                                ++numPrefetchPages;
                            }
                            else
                            {
                                // Need to mark the page as recently used, otherwise it may be evicted later this frame
                                AddPageUpdate(pageUpdateBuffers, pageUpdateFlushCount, physicalSpace->GetID(), pAddress);
                            }
                        }
                    }

                    const UInt16 loadRequestIndex = requestList->AddLoadRequest(VTLocalTile(producerHandle, prefetchLocal_vAddress, prefetchLocal_vLevel), producerPhysicalGroupMaskToPrefetch, pageCount);
                    if (loadRequestIndex != 0xffff)
                    {
                        const UInt8 prefetchMapping_vLevel = prefetchLocal_vLevel + producerMipBias;

                        const UInt32 prefetch_vAddress = vAddress & (0xffffffff << (prefetchMapping_vLevel * vDimensions));
                        for (UInt32 loadLayerIndex = 0u; loadLayerIndex < numPageTableLayersToLoad; ++loadLayerIndex)
                        {
                            const UInt8 layerIndex = pageTableLayersToLoad[loadLayerIndex];
                            if (allocatedVT->GetProducerIndexForPageTableLayer(layerIndex) == producerIndex)
                            {
                                const UInt8 producerPhysicalGroupIndex = allocatedVT->GetProducerPhysicalGroupIndexForPageTableLayer(layerIndex);
                                if (producerPhysicalGroupMaskToPrefetch & (1u << producerPhysicalGroupIndex))
                                {
                                    requestList->AddMappingRequest(loadRequestIndex, producerPhysicalGroupIndex, id, layerIndex, maxLevel, prefetch_vAddress, prefetchMapping_vLevel, prefetchMapping_vLevel);
                                }
                            }
                        }
                    }
                }
            }

            if (groupMaskToLoad != 0u)
            {
                const UInt16 loadRequestIndex = requestList->AddLoadRequest(VTLocalTile(producerHandle, local_vAddress, local_vLevel), groupMaskToLoad, pageCount);
                if (loadRequestIndex != 0xffff)
                {
                    for (UInt32 loadLayerIndex = 0u; loadLayerIndex < numPageTableLayersToLoad; ++loadLayerIndex)
                    {
                        const UInt8 layerIndex = pageTableLayersToLoad[loadLayerIndex];
                        if (allocatedVT->GetProducerIndexForPageTableLayer(layerIndex) == producerIndex)
                        {
                            const UInt8 producerPhysicalGroupIndex = allocatedVT->GetProducerPhysicalGroupIndexForPageTableLayer(layerIndex);
                            if (groupMaskToLoad & (1u << producerPhysicalGroupIndex))
                            {
                                requestList->AddMappingRequest(loadRequestIndex, producerPhysicalGroupIndex, id, layerIndex, maxLevel, vAddress, vLevel, mapping_vLevel);
                            }
                        }
                    }
                }
            }
        }
    }

    //std::vector<VTPageUpdateBuffer>& pageUpdateBuffers = gatherRequestsParameters[taskIndex].pageUpdateBuffers;
    {
        std::shared_lock lock(mPhysicalSpaceMutex);
        for (UInt16 physicalSpaceID = 0u; physicalSpaceID < static_cast<UInt16>(mPhysicalSpaces.size()); ++physicalSpaceID)
        {
            SCOPED_CPU_TIMING(GroupRendering, "UpdateUsage");
            if (mPhysicalSpaces[physicalSpaceID] == nullptr)
            {
                continue;
            }

            VTPhysicalSpace* physicalSpace = GetPhysicalSpace(physicalSpaceID);
            VTPageUpdateBuffer& buffer = pageUpdateBuffers[physicalSpaceID];

            if (buffer.numPages > 0u)
            {
                buffer.numPageUpdates += buffer.numPages;
                VTPagePool& pagePool = physicalSpace->GetPagePool();
                auto scope_lock = pagePool.Lock();
                for (UInt32 i = 0u; i < buffer.numPages; ++i)
                {
                    pagePool.UpdateUsage(mFrame, buffer.physicalAddresses[i]);
                }
            }
        }
    }
}

IAllocatedVirtualTexture* VirtualTextureSystemR::AllocateVirtualTexture(const FAllocatedVTDescription& desc)
{
    auto it = mAllocatedVTs.find(desc);
    if (it != mAllocatedVTs.end())
    {
        it->second->AddRef();
        return it->second;
    }

    UInt32 blockWidthInTiles = 0u;
    UInt32 blockHeightInTiles = 0u;
    UInt32 minWidthInBlocks = ~0u;
    UInt32 minHeightInBlocks = ~0u;
    UInt32 arrayX = 1u;
    UInt32 arrayY = 1u;


    VirtualTextureProducer* producerForLayer[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {nullptr};
    bool bAnyLayerProducerWantsPersistentHighestMip = false;

    for (UInt32 layerIndex = 0u; layerIndex < desc.numTextureLayers; ++layerIndex)
    {
        VirtualTextureProducer* producer = mProducers.FindProducer(desc.producerHandle[layerIndex]);
        producerForLayer[layerIndex] = producer;
        if (producer)
        {
            const VTProducerDescription& producerDesc = producer->GetDescription();
            blockWidthInTiles = std::max(blockWidthInTiles, producerDesc.blockWidthInTiles);
            blockHeightInTiles = std::max(blockHeightInTiles, producerDesc.blockHeightInTiles);
            minWidthInBlocks = std::min<UInt32>(minWidthInBlocks, producerDesc.widthInBlocks);
            minHeightInBlocks = std::min<UInt32>(minHeightInBlocks, producerDesc.heightInBlocks);
            arrayX = std::max(arrayX, producerDesc.arrayX);
            arrayY = std::max(arrayY, producerDesc.arrayY);
            bAnyLayerProducerWantsPersistentHighestMip |= producer->GetDescription().bPersistentHighestMip;
        }
    }
    // Find a block width that is evenly divided by all layers (least common multiple)
    // Start with min size, then increment by min size until a valid size is found
    UInt32 widthInBlocks = minWidthInBlocks;
    {
        bool bFoundValidWidthInBlocks = false;
        while (!bFoundValidWidthInBlocks)
        {
            bFoundValidWidthInBlocks = true;
            for (UInt32 layerIndex = 0u; layerIndex < desc.numTextureLayers; ++layerIndex)
            {
                const VirtualTextureProducer* producer = producerForLayer[layerIndex];
                if (producer)
                {
                    if ((widthInBlocks % producer->GetDescription().widthInBlocks) != 0u)
                    {
                        widthInBlocks += minWidthInBlocks;
                        Assert(widthInBlocks > minWidthInBlocks);   // check for overflow
                        bFoundValidWidthInBlocks = false;
                        break;
                    }
                }
            }
        }
    }

    // Same thing for height
    UInt32 heightInBlocks = minHeightInBlocks;
    {
        bool bFoundValidHeightInBlocks = false;
        while (!bFoundValidHeightInBlocks)
        {
            bFoundValidHeightInBlocks = true;
            for (UInt32 layerIndex = 0u; layerIndex < desc.numTextureLayers; ++layerIndex)
            {
                const VirtualTextureProducer* producer = producerForLayer[layerIndex];
                if (producer)
                {
                    if ((heightInBlocks % producer->GetDescription().heightInBlocks) != 0u)
                    {
                        heightInBlocks += minHeightInBlocks;
                        Assert(heightInBlocks > minHeightInBlocks); // check for overflow
                        bFoundValidHeightInBlocks = false;
                        break;
                    }
                }
            }
        }
    } 

    AllocatedVirtualTexture* allocatedVT = new AllocatedVirtualTexture(this, mFrame, desc, producerForLayer, blockWidthInTiles, blockHeightInTiles, widthInBlocks, heightInBlocks, arrayX, arrayY);
    allocatedVT->AddRef();
    mAllocatedVTs.emplace(desc, allocatedVT);
    if (bAnyLayerProducerWantsPersistentHighestMip)
    {
        mAllocatedVTsToMap.push_back(allocatedVT);
    }
    return allocatedVT;
}

VTProducerHandle VirtualTextureSystemR::RegisterProducer(const VTProducerDescription& inDesc, IVirtualTexture* inProducer)
{
    return mProducers.RegisterProducer(this, inDesc, inProducer);
}

void VirtualTextureSystemR::ReleaseProducer(const UInt32& handle) 
{
    mProducers.ReleaseProducer(this, VTProducerHandle(handle));
}

void VirtualTextureSystemR::DestoryVirtualTexture(IAllocatedVirtualTexture* allocatedVT)
{
    const UInt32 numRefs = allocatedVT->ReleaseRef();
    Assert(numRefs >= 0);
    if (numRefs == 0) 
    {
        allocatedVT->frameDeleted = mFrame;
        mPendingDeleteAllocatedVTs.push_back(allocatedVT);
    }
}

void VirtualTextureSystemR::DestroyPendingVirtualTextures(bool bForceDestoryAll)
{
    std::vector<IAllocatedVirtualTexture*> allocatedVTsToDelete;
    if (bForceDestoryAll)
    {
        allocatedVTsToDelete = std::move(mPendingDeleteAllocatedVTs);
        mPendingDeleteAllocatedVTs.clear();
    }
    else
    {
        //const UInt32 maxDeletePerFrame = 60;
        const UInt32 currentFrame = mFrame;
        SInt32 index = static_cast<SInt32>(mPendingDeleteAllocatedVTs.size() - 1);
        while (index >= 0)
        {
            IAllocatedVirtualTexture* allocatedVT = mPendingDeleteAllocatedVTs[index];
            auto desc = allocatedVT->GetDescription();
            const bool bForceDelete = desc.bPrivateSpace;
            const bool bCanDeleteTime = currentFrame >= allocatedVT->frameDeleted;
            if (bForceDelete || bCanDeleteTime)
            {
                if (allocatedVT->GetRefs() == 0)    // switch world, next world may use this allocated vt, but last world added to pending delete
                    allocatedVTsToDelete.push_back(allocatedVT);
                mPendingDeleteAllocatedVTs.erase(mPendingDeleteAllocatedVTs.begin() + index);
            }
            index--;
        }
    }

    // delete
    for (auto* allocatedVT : allocatedVTsToDelete)
    {
        auto it = mAllocatedVTs.find(allocatedVT->GetDescription());
        if (it != mAllocatedVTs.end()) 
        {
            LOG_INFO("Delete allocated vt:{}\n", it->second->mName);
            it->second->Destroy(this);
            mAllocatedVTs.erase(it);
        }
    }
    ReleasePendingPhysicalSpaces();
}


void VirtualTextureSystemR::ReleasePendingPhysicalSpaces() 
{
    std::unique_lock lock(mPhysicalSpaceMutex);
    for (SInt32 id = 0; id < mPhysicalSpaces.size(); ++id)
    {
        // Physical space is released when ref count hits 0
        // Might need to have some mechanism to hold an extra reference if we know we will be recycling very soon (such when doing level reload)
        VTPhysicalSpace* physicalSpace = mPhysicalSpaces[id];
        if (physicalSpace && physicalSpace->GetRefCount() == 0u && mFrame > static_cast<UInt32>(physicalSpace->GetFrameDeleted()))
        {
            physicalSpace->ReleaseNGI();
            delete physicalSpace;
            mPhysicalSpaces[id] = nullptr;
        }
    }
}

void VirtualTextureSystemR::ReleaseResource() 
{
    // Relase page list and request list 
    //mMergedRequestList->Release();
    //delete mMergedRequestList;
    //mMergedRequestList = nullptr;

    // Relase feedback analysis tasks
    //for (UInt32 i = 0; i < MaxNumTasks; i++)
    //{
    //    VTFeedbackAnalysisTask* task = mFeedbackAnalysisTasks[i];
    //    delete task;
    //    mFeedbackAnalysisTasks[i] = nullptr;
    //}

    mProducers.ReleaseOverBudget();
}

void VirtualTextureSystemR::GatherRequests(UInt32 frameRequested) 
{
    SCOPED_CPU_TIMING(GroupRendering, "GatherRequests");
    const UInt32 pageUpdateFlushCount = 8;

   
    UInt32 numGatherTasks = 0u;
    {
        const UInt32 numPagesPerTask = 128;
        const UInt32 numPages = mMergedUniquePageList->GetNum();
        UInt32 startPageIndex = 0u;
        Assert(numPages < numPagesPerTask * MaxNumTasks);
        while (startPageIndex < numPages)
        {
            const UInt32 numPagesForTask = std::min(numPagesPerTask, numPages - startPageIndex);
            if (numPagesForTask > 0u)
            {
                const UInt32 taskIndex = numGatherTasks++;
                VTGatherRequestsParameters& params = gatherRequestsParameters[taskIndex];
                params.system = this;
                params.frameRequested = frameRequested;
                params.uniquePageList = mMergedUniquePageList;
                params.pageUpdateFlushCount = pageUpdateFlushCount;
                const UInt32 bufferSize = static_cast<UInt32>(mPhysicalSpaces.size());
                params.pageUpdateBuffers.clear();
                params.pageUpdateBuffers.resize(bufferSize);
                if (taskIndex == 0u)
                {
                    params.requestList = mMergedRequestList;
                }
                else
                {
                    if (params.requestList == nullptr)
                    {
                        params.requestList = new VTUniqueRequestList();
                    }
                    params.requestList->Initialize();
                }
                params.pageStartIndex = startPageIndex;
                params.numPages = numPagesForTask;
                startPageIndex += numPagesForTask;
            }
        }
    }

    // Kick all of the tasks
    if (numGatherTasks > 0u)
    {
        //for (UInt32 taskIndex = 0u; taskIndex < numGatherTasks; ++taskIndex)
        //{
        //    //VTGatherRequestsTask* requestsTask = new VTGatherRequestsTask(gatherRequestsParameters[taskIndex]);
        //    //SCOPED_CPU_TIMING(GroupRendering, "VTRequestTask");
        //    //requestsTask->DoTask();
        //    //delete requestsTask;
        //    VTGatherRequestsTask::DoTask(gatherRequestsParameters[taskIndex]);
        //}

        threading::ParallelFor(static_cast<SInt32>(numGatherTasks), [&](SInt32 taskIndex) 
        //for (int taskIndex = 0; taskIndex < static_cast<SInt32>(numGatherTasks); taskIndex++)
        {
            QUICK_SCOPED_CPU_TIMING("GatherRequestTask");
            VTGatherRequestsTask::DoTask(gatherRequestsParameters[taskIndex]);
        });


        for (UInt32 taskIndex = 1u; taskIndex < numGatherTasks; ++taskIndex)
        {
            mMergedRequestList->MergeRequests(gatherRequestsParameters[taskIndex].requestList);
        }
    }
}

void VirtualTextureSystemR::AddPageUpdate(std::vector<VTPageUpdateBuffer>& buffers, UInt32 flushCount, UInt16 physicalSpaceID, UInt16 pAddress) 
{
    VTPageUpdateBuffer& buffer = buffers[physicalSpaceID];
    if (pAddress == buffer.prevPhysicalAddress)
    {
        return;
    }
    buffer.prevPhysicalAddress = pAddress;

    if (buffer.numPages >= flushCount)
    {
        // Once we've passed a certain threshold of pending pages to update, try to take the lock then apply the updates
        VTPhysicalSpace* physicalSpace = GetPhysicalSpace(physicalSpaceID);
        VTPagePool& pagePool = physicalSpace->GetPagePool();
        
        {
            auto scope_lock = pagePool.Lock();
            const UInt32 currentFrame = mFrame;
            pagePool.UpdateUsage(currentFrame, pAddress);   // Update current request now, if we manage to get the lock
            for (UInt32 i = 0u; i < buffer.numPages; ++i)
            {
                pagePool.UpdateUsage(currentFrame, buffer.physicalAddresses[i]);
            }
            buffer.numPageUpdates += (buffer.numPages + 1u);
            buffer.numPages = 0u;
        }
    }

    // Only need to buffer if we didn't lock (otherwise this has already been updated
    {
        Assert(buffer.numPages < VTPageUpdateBuffer::PageCapacity);
        buffer.physicalAddresses[buffer.numPages++] = pAddress;
    }
}

void VirtualTextureSystemR::SubmitRequestsFromLocalTileList(std::vector<VTLocalTile>& outDeferredTiles, const std::set<VTLocalTile>& localTileList, EVTProducePageFlags flags) 
{
    for (const VTLocalTile& tile : localTileList)
    {
        const VTProducerHandle producerHandle = tile.GetProducerHandle();
        VirtualTextureProducer const* producer = mProducers.FindProducer(producerHandle);
        if (producer == nullptr)
        {
            // If we didn't process the tile last frame and deferred processing and if the producer got removed in the meantime we end up here.
            // Just throw away the request.
            continue;
        }

        // Fill targets for each layer
        // Each producer can have multiple physical layers
        // If the phys layer is mapped then we get the textures it owns and map them into the producer local slots and set the flags
        UInt8 layerMask = 0;
        VTProduceTargetLayer produceTarget[VIRTUALTEXTURE_SPACE_MAXLAYERS];
        for (UInt32 producerPhysicalGroupIndex = 0u; producerPhysicalGroupIndex < producer->GetNumPhysicalGroups(); ++producerPhysicalGroupIndex)
        {
            VTPhysicalSpace* physicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
            VTPagePool& pagePool = physicalSpace->GetPagePool();
            const UInt32 pAddress = pagePool.FindPageAddress(producerHandle, static_cast<UInt8>(producerPhysicalGroupIndex), tile.local_vAddress, tile.local_vLevel);
            if (pAddress != ~0u)
            {
                UInt32 physicalLocalTextureIndex = 0;
                for (UInt32 producerLayerIndex = 0u; producerLayerIndex < producer->GetNumTextureLayers(); ++producerLayerIndex)
                {
                    if (producer->GetPhysicalGroupIndexForTextureLayer(producerLayerIndex) == producerPhysicalGroupIndex)
                    {
                        produceTarget[producerLayerIndex].texture = physicalSpace->GetPhysicalTexture(physicalLocalTextureIndex);
                        produceTarget[producerLayerIndex].textureSRV = physicalSpace->GetPhysicalTextureSRV(physicalLocalTextureIndex);
                        produceTarget[producerLayerIndex].pPageLocation = physicalSpace->GetPhysicalLocation(static_cast<UInt16>(pAddress));
                        layerMask |= 1 << producerLayerIndex;
                        physicalLocalTextureIndex++;
                    }
                }
            }
        }

        if (layerMask == 0)
        {
            // If we don't have anything mapped then we can ignore (since we only want to refresh existing mapped data)
            continue;
        }

        VTRequestPageResult requestPageResult = producer->GetVirtualTexture()->RequestPageData(producerHandle, layerMask, tile.local_vLevel, tile.local_vAddress, EVTRequestPagePriority::High);

        if (requestPageResult.status != EVTRequestPageStatus::Available)
        {
            // Keep the request for the next frame?
            outDeferredTiles.push_back(tile);
            continue;
        }
        // Produce Page Data
        producer->GetVirtualTexture()->ProducePageData(EVTProducePageFlags::SkipPageBorders, 
            producerHandle, layerMask, tile.local_vLevel, tile.local_vAddress, requestPageResult.handle, produceTarget);
    }
}

void VirtualTextureSystemR::SubmitPreMappedRequests() 
{
    {
        SubmitRequestsFromLocalTileList(mTransientCollectedPages, mMappedTilesToProduce, EVTProducePageFlags::None);
        mMappedTilesToProduce.clear();
        mMappedTilesToProduce.insert(mTransientCollectedPages.begin(), mTransientCollectedPages.end());
        mTransientCollectedPages.clear();
    }

    {
        SubmitRequestsFromLocalTileList(mTransientCollectedPages, mContinuousUpdateTilesToProduce, EVTProducePageFlags::ContinuousUpdate);
        mContinuousUpdateTilesToProduce.clear();
        mTransientCollectedPages.clear();
    }
}

void VirtualTextureSystemR::SubmitRequests(RenderingExecutionDescriptor* RED, RenderWorld* world, ComputeShaderR* VTCS, VTUniqueRequestList* requestList, bool bAsync)
{
    std::vector<UInt32> requestPhysicalAddress(requestList->GetNumLoadRequests() * VIRTUALTEXTURE_SPACE_MAXLAYERS);
    std::memset(requestPhysicalAddress.data(), ~0u, requestPhysicalAddress.size() * sizeof(UInt32));
    {
        std::vector<FProducePageDataPrepareTask> prepareTasks;
        prepareTasks.clear();
        prepareTasks.reserve(64);

        const bool bSyncProduceLockedTiles = true;
        bool bWaitForProducers = false;

        const UInt32 maxPagesProduced = 300;        // Max number of pages that can be produced per frame
        const UInt32 pageFreeThreshold = 60;       // Number of frames since the last time a VT page was used, before it's considered free
        UInt32 numPagesProduced = 0u;
        UInt32 numPageAllocateFails = 0u;

        for (UInt32 requestIndex = 0u; requestIndex < requestList->GetNumLoadRequests(); ++requestIndex)
        {
            SCOPED_CPU_TIMING(GroupRendering, "VTLoadRequest");
            bool bLockTile = requestList->IsLocked(requestIndex);
            //bLockTile = true;   // every frame refresh lock tile
            bool bForceProduceTile = !bAsync || (bLockTile && bSyncProduceLockedTiles);
            const VTLocalTile tileToLoad = requestList->GetLoadRequest(requestIndex);
            const VTProducerHandle producerHandle = tileToLoad.GetProducerHandle();
            const VirtualTextureProducer& producer = mProducers.GetProducer(producerHandle);

            const UInt32 producerPhysicalGroupMask = requestList->GetGroupMask(requestIndex);
            UInt32 producerTextureLayerMask = 0;
            for (UInt32 producerLayerIndex = 0; producerLayerIndex < producer.GetNumTextureLayers(); ++producerLayerIndex)
            {
                if (producerPhysicalGroupMask & (1 << producer.GetPhysicalGroupIndexForTextureLayer(producerLayerIndex)))
                {
                    producerTextureLayerMask |= (1 << producerLayerIndex);
                }
            }

            const EVTRequestPagePriority priority = bLockTile ? EVTRequestPagePriority::High : EVTRequestPagePriority::Normal;
            //VTRequestPageResult requestPageResult = producer.GetVirtualTexture()->RequestPageData(producerHandle, static_cast<UInt8>(producerTextureLayerMask), static_cast<UInt8>(tileToLoad.local_vLevel), tileToLoad.local_vAddress, priority);
            SCOPED_CPU_TIMING(GroupRendering, "VTRequestProducer");
            VTRequestPageResult requestPageResult = mProducers.Request(producerHandle, static_cast<UInt8>(producerTextureLayerMask), static_cast<UInt8>(tileToLoad.local_vLevel), tileToLoad.local_vAddress, priority);
            //bForceProduceTile = false;
            if (requestPageResult.status == EVTRequestPageStatus::Pending && bForceProduceTile)
            {
                // If we're forcing production of this tile, we're OK producing data now (and possibly waiting) as long as data is pending
                requestPageResult.status = EVTRequestPageStatus::Available;
                bWaitForProducers = true;
            }

            if (requestPageResult.status == EVTRequestPageStatus::Available && !bForceProduceTile && numPagesProduced >= maxPagesProduced)
            {
                // Don't produce non-locked pages yet, if we're over our limit
                requestPageResult.status = EVTRequestPageStatus::Pending;
            }

            bool bTileLoaded = false;
            bool bTileInvalid = false;
            if (requestPageResult.status == EVTRequestPageStatus::Invalid)
            {
                bTileInvalid = true;
            }
            else if (requestPageResult.status == EVTRequestPageStatus::Available)
            {
                SCOPED_CPU_TIMING(GroupRendering, "VTallocateapageLoadRequest");
                VTProduceTargetLayer produceTarget[VIRTUALTEXTURE_SPACE_MAXLAYERS];
                UInt32 allocate_pAddress[VIRTUALTEXTURE_SPACE_MAXLAYERS];
                std::memset(allocate_pAddress, 255, sizeof(UInt32) * VIRTUALTEXTURE_SPACE_MAXLAYERS);
                
                // try to allocate a page for each layer we need to load
                bool bProduceTargetValid = true;
                for (UInt32 producerPhysicalGroupIndex = 0u; producerPhysicalGroupIndex < producer.GetNumPhysicalGroups(); ++producerPhysicalGroupIndex)
                {
                    // If mask isn't set, we must already have a physical tile allocated for this layer, don't need to allocate another one
                    if (producerPhysicalGroupMask & (1u << producerPhysicalGroupIndex))
                    {
                        VTPhysicalSpace* physicalSpace = producer.GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
                        VTPagePool& pagePool = physicalSpace->GetPagePool();
                        if (pagePool.AnyFreeAvailable(mFrame, bLockTile? 0 :pageFreeThreshold))
                        {
                            const UInt32 pAddress = pagePool.Alloc(this, mFrame, producerHandle, static_cast<UInt8>(producerPhysicalGroupIndex), tileToLoad.local_vAddress, static_cast<UInt8>(tileToLoad.local_vLevel), bLockTile);
                            Assert(pAddress != ~0u);

                            UInt32 physicalLocalTextureIndex = 0;
                            for (UInt32 producerLayerIndex = 0u; producerLayerIndex < producer.GetNumTextureLayers(); ++producerLayerIndex)
                            {
                                if (producer.GetPhysicalGroupIndexForTextureLayer(producerLayerIndex) == producerPhysicalGroupIndex)
                                {
                                    produceTarget[producerLayerIndex].texture = physicalSpace->GetPhysicalTexture(physicalLocalTextureIndex);
                                    produceTarget[producerLayerIndex].textureSRV = physicalSpace->GetPhysicalTextureSRV(physicalLocalTextureIndex);
                                    produceTarget[producerLayerIndex].pPageLocation = physicalSpace->GetPhysicalLocation(static_cast<UInt16>(pAddress));

                                    physicalLocalTextureIndex++;

                                    allocate_pAddress[producerPhysicalGroupIndex] = pAddress;
                                }
                            }

                            ++numPagesProduced;
                        }
                        else
                        {
                            bProduceTargetValid = false;
                            numPageAllocateFails++;
                            break;
                        }
                    }
                }
                if (bProduceTargetValid)
                {
                    // Successfully allocated required pages, now we can make the request
                    for (UInt32 producerPhysicalGroupIndex = 0u; producerPhysicalGroupIndex < producer.GetNumPhysicalGroups(); ++producerPhysicalGroupIndex)
                    {
                        if (producerPhysicalGroupMask & (1u << producerPhysicalGroupIndex))
                        {
                            // Associate the addresses we allocated with this request, so they can be mapped if required
                            const UInt32 pAddress = allocate_pAddress[producerPhysicalGroupIndex];
                            Assert(pAddress != ~0u);
                            requestPhysicalAddress[requestIndex * VIRTUALTEXTURE_SPACE_MAXLAYERS + producerPhysicalGroupIndex] = pAddress;
                        }
                        else
                        {
                            // Fill in pAddress for layers that are already resident
                            const VTPhysicalSpace* physicalSpace = producer.GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
                            const VTPagePool& pagePool = physicalSpace->GetPagePool();
                            const UInt32 pAddress = pagePool.FindPageAddress(producerHandle, static_cast<UInt8>(producerPhysicalGroupIndex), tileToLoad.local_vAddress, static_cast<UInt8>(tileToLoad.local_vLevel));

                            UInt32 physicalLocalTextureIndex = 0;
                            for (UInt32 producerLayerIndex = 0u; producerLayerIndex < producer.GetNumTextureLayers(); ++producerLayerIndex)
                            {
                                if (producer.GetPhysicalGroupIndexForTextureLayer(producerLayerIndex) == producerPhysicalGroupIndex)
                                {
                                    produceTarget[producerLayerIndex].texture = physicalSpace->GetPhysicalTexture(physicalLocalTextureIndex);
                                    produceTarget[producerLayerIndex].textureSRV = physicalSpace->GetPhysicalTextureSRV(physicalLocalTextureIndex);
                                    produceTarget[producerLayerIndex].pPageLocation = physicalSpace->GetPhysicalLocation(static_cast<UInt16>(pAddress));
                                    physicalLocalTextureIndex++;
                                }
                            }
                        }
                    }

                    {
                        SCOPED_CPU_TIMING(GroupRendering, "VTallocateapageMemcpy");
                        FProducePageDataPrepareTask task;
                        task.virtualTexture = producer.GetVirtualTexture();
                        task.flags = EVTProducePageFlags::None;
                        task.producerHandle = producerHandle;
                        task.layerMask = static_cast<UInt8>(producerTextureLayerMask);
                        task.vLevel = tileToLoad.local_vLevel;
                        task.vAddress = tileToLoad.local_vAddress;
                        task.requestHandle = requestPageResult.handle;
                        std::memcpy(task.produceTarget, produceTarget, sizeof(produceTarget));
                        prepareTasks.push_back(task);
                        //if (task.virtualTexture->IsTopLevel(tileToLoad.local_vLevel))
                        //    mTopLevelTileTask.push_back(task);
                    }

                    bTileLoaded = true;
                }
                else
                {
                    // Failed to allocate required physical pages for the tile, free any pages we did manage to allocate
                    for (UInt32 producerPhysicalGroupIndex = 0u; producerPhysicalGroupIndex < producer.GetNumPhysicalGroups(); ++producerPhysicalGroupIndex)
                    {
                        const UInt32 pAddress = allocate_pAddress[producerPhysicalGroupIndex];
                        if (pAddress != ~0u)
                        {
                            VTPhysicalSpace* physicalSpace = producer.GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
                            VTPagePool& pagePool = physicalSpace->GetPagePool();
                            pagePool.Free(this, static_cast<UInt16>(pAddress));
                        }
                    }
                }
            }

            if (bLockTile && !bTileLoaded && !bTileInvalid)
            {
                // Want to lock this tile, but didn't manage to load it this frame, add it back to the list to try the lock again next frame
                mTilesToLock.push_back(tileToLoad);
            }
        }

        for (FProducePageDataPrepareTask& task : prepareTasks)
        {
            SCOPED_CPU_TIMING(GroupRendering, "VTProducePageData");
            task.virtualTexture->ProducePageData(task.flags, task.producerHandle, task.layerMask, task.vLevel, task.vAddress, task.requestHandle, task.produceTarget);
        }
       /* threading::ParallelFor(static_cast<int>(prepareTasks.size()), [&](SInt32 index) {
            SCOPED_CPU_TIMING(GroupRendering, "VTProducePageData");
            auto& task = prepareTasks[index];
            task.virtualTexture->ProducePageData(task.flags, task.producerHandle, task.layerMask, task.vLevel, task.vAddress, task.requestHandle, task.produceTarget);
            });*/


        if (numPageAllocateFails != 0)
        {
            //LOG_INFO("Total Loading {} of {} failed", numPageAllocateFails, requestList->GetNumLoadRequests());
        }
    }

    {
        // ProcessRequests_Map
        for (UInt32 requestIndex = 0u; requestIndex < requestList->GetNumDirectMappingRequests(); ++requestIndex)
        {
            SCOPED_CPU_TIMING(GroupRendering, "VTProcessRequestsMap");
            const VTDirectMappingRequest mappingRequest = requestList->GetDirectMappingRequest(requestIndex);
            VTSpace* space = GetSpace(mappingRequest.spaceID);
            VTPhysicalSpace* physicalSpace = GetPhysicalSpace(mappingRequest.physicalSpaceID);

            physicalSpace->GetPagePool().MapPage(space, physicalSpace, mappingRequest.pageTableLayerIndex, mappingRequest.MaxLevel, mappingRequest.vLevel, mappingRequest.vAddress, mappingRequest.local_vLevel, mappingRequest.pAddress);
        }

        // Update page mappings for any requested page that completed allocation this frame
        for (UInt32 requestIndex = 0u; requestIndex < requestList->GetNumMappingRequests(); ++requestIndex)
        {
            SCOPED_CPU_TIMING(GroupRendering, "GetNumMappingRequestsMapping");
            const VTMappingRequest mappingRequest = requestList->GetMappingRequest(requestIndex);
            const UInt32 pAddress = requestPhysicalAddress[mappingRequest.loadRequestIndex * VIRTUALTEXTURE_SPACE_MAXLAYERS + mappingRequest.producerPhysicalGroupIndex];
            if (pAddress != ~0u)
            {
                const VTLocalTile& tileToLoad = requestList->GetLoadRequest(mappingRequest.loadRequestIndex);
                const VTProducerHandle producerHandle = tileToLoad.GetProducerHandle();
                VirtualTextureProducer& producer = mProducers.GetProducer(producerHandle);
                VTPhysicalSpace* physicalSpace = producer.GetPhysicalSpaceForPhysicalGroup(mappingRequest.producerPhysicalGroupIndex);
                VTSpace* space = GetSpace(mappingRequest.spaceID);
                Assert(requestList->GetGroupMask(mappingRequest.loadRequestIndex) & (1u << mappingRequest.producerPhysicalGroupIndex));

                physicalSpace->GetPagePool().MapPage(space, physicalSpace, mappingRequest.pageTableLayerIndex, mappingRequest.maxLevel, mappingRequest.vLevel, mappingRequest.vAddress, mappingRequest.local_vLevel, static_cast<UInt16>(pAddress));
            }
        }
    }

    // Map any resident tiles to newly allocated VTs
    if (mAllocatedVTsToMap.size() > 0)
    {
        UInt32 Index = 0u;
        while (Index < (UInt32)mAllocatedVTsToMap.size())
        {
            const IAllocatedVirtualTexture* allocatedVT = mAllocatedVTsToMap[Index];
            if (allocatedVT->mNumRefs == 0 || allocatedVT->TryMapLockedTiles(this))
            {
                mAllocatedVTsToMap.erase(mAllocatedVTsToMap.begin() + Index);
            }
            else
            {
                Index++;
            }
        }

        mAllocatedVTsToMap.shrink_to_fit();
    }

    // Update page tables
    {
        SCOPED_CPU_TIMING(GroupRendering, "VTUpdatePageTable");
        for (UInt32 ID = 0; ID < MaxSpaces; ID++)
        {
            if (mSpaces[ID])
            {
                mSpaces[ID]->ApplyUpdates(RED, world, VTCS, this);
            }
        }
    }

    mFrame++;
}

void VirtualTextureSystemR::RequestTilesInternal(const IAllocatedVirtualTexture* allocatedVT, SInt32 inMipLevel) 
{
    const SInt32 mipWidthInTiles = std::max<SInt32>(allocatedVT->GetWidthInTiles() >> inMipLevel, 1);
    const SInt32 mipHeightInTiles = std::max<SInt32>(allocatedVT->GetHeightInTiles() >> inMipLevel, 1);
    const UInt32 vBaseTileX = allocatedVT->GetVirtualPageX() >> inMipLevel;
    const UInt32 vBaseTileY = allocatedVT->GetVirtualPageY() >> inMipLevel;

    for (UInt32 tilePositionY = 0; tilePositionY < static_cast<UInt32>(mipHeightInTiles); tilePositionY++)
    {
        const UInt32 vGlobalTileY = vBaseTileY + tilePositionY;
        for (SInt32 tilePositionX = 0; tilePositionX < mipWidthInTiles; tilePositionX++)
        {
            const UInt32 vGlobalTileX = vBaseTileX + tilePositionX;
            const UInt32 encodedTile = EncodePage(allocatedVT->GetSpaceID(), inMipLevel, vGlobalTileX, vGlobalTileY);
            mRequestedPackedTiles.push_back(encodedTile);
        }
    }
}

void VirtualTextureSystemR::RequestTilesInternal(const IAllocatedVirtualTexture* allocatedVT, const UInt2& inScreenSpaceSize, SInt32 inMipLevel)
{
    if (inMipLevel < 0)
    {
        const UInt32 vMaxLevel = allocatedVT->GetMaxLevel();
        const float vLevel = ComputeMipLevel(allocatedVT, inScreenSpaceSize);
        const SInt32 vMipLevelDown = std::clamp(static_cast<SInt32>(vLevel), 0, static_cast<SInt32>(vMaxLevel));

        RequestTilesInternal(allocatedVT, vMipLevelDown);
        if (vMipLevelDown + 1u <= vMaxLevel)
        {
            // Need to fetch 2 levels to support trilinear filtering
            RequestTilesInternal(allocatedVT, vMipLevelDown + 1u);
        }
    }
    else
    {
        RequestTilesInternal(allocatedVT, inMipLevel);
    }
}

VTPhysicalSpace* VirtualTextureSystemR::AcquirePhysicalSpace(const VTPhysicalSpaceDescription& inDesc)
{
    std::unique_lock lock(mPhysicalSpaceMutex);
    for (SInt32 i = 0; i < mPhysicalSpaces.size(); ++i)
    {
        VTPhysicalSpace* physicalSpace = mPhysicalSpaces[i];
        if (physicalSpace && physicalSpace->GetDescription() == inDesc)
        {
            return physicalSpace;
        }
    }
    SizeType ID = mPhysicalSpaces.size();
    Assert(ID <= 0x0fff);

    for (SizeType i = 0; i < mPhysicalSpaces.size(); ++i)
    {
        if (!mPhysicalSpaces[i])
        {
            ID = i;
            break;
        }
    }

    if (ID == mPhysicalSpaces.size())
    {
        VTPhysicalSpace* ptr = nullptr;
        mPhysicalSpaces.push_back(ptr);
    }

    VTPhysicalSpace* physicalSpace = new VTPhysicalSpace(inDesc, static_cast<UInt16>(ID));
    mPhysicalSpaces[ID] = physicalSpace;
    return physicalSpace;
}

VTSpace* VirtualTextureSystemR::AcquireSpace(VTSpaceDescription& inSpaceDesc, UInt8 forceSpaceID, AllocatedVirtualTexture* allocatedVT)
{
    if (!inSpaceDesc.bPrivateSpace || forceSpaceID != 0xff)
    {
        for (UInt32 SpaceIndex = 0u; SpaceIndex < MaxSpaces; ++SpaceIndex)
        {
            if (SpaceIndex == forceSpaceID || forceSpaceID == 0xff)
            {
                VTSpace* Space = mSpaces[SpaceIndex].get();
                if (Space && Space->GetDescription() == inSpaceDesc)
                {
                    const UInt32 vAddress = Space->AllocateVirtualTexture(allocatedVT);
                    if (vAddress != ~0u)
                    {
                        allocatedVT->AssignVirtualAddress(vAddress);
                        Space->AddRef();
                        return Space;
                    }
                    else
                    {
                        printf("Failed Allocation space\n");
                    }
                }
            }
        }
    }

    for (UInt8 spaceIndex = 0u; spaceIndex < MaxSpaces; ++spaceIndex)
    {
        if (!mSpaces[spaceIndex])
        {
            VTSpace* space = (new VTSpace(spaceIndex, inSpaceDesc));
            mSpaces[spaceIndex].reset(space);

            space->InitRenderResource();

            const UInt32 vAddress = space->AllocateVirtualTexture(allocatedVT);
            allocatedVT->AssignVirtualAddress(vAddress);
            space->AddRef();
            return space;
        }
    }
    return nullptr;
}
VirtualTextureProducer* VirtualTextureSystemR::FindProducer(const VTProducerHandle& handle)
{
    return mProducers.FindProducer(handle);
}
void VirtualTextureSystemR::ReleaseSpace(VTSpace* space)
{
    const UInt32 numRefs = space->Release();
    if (numRefs == 0)
    {
        space->ReleaseRenderResource();
        mSpaces[space->GetID()].reset();
    }
}

void VirtualTextureSystemR::ReleasePhysicalSpace(UInt16 ID) 
{
    std::unique_lock lock(mPhysicalSpaceMutex);
    for (SInt32 i = 0; i < mPhysicalSpaces.size(); ++i)
    {
        VTPhysicalSpace* physicalSpace = mPhysicalSpaces[i];
        if (physicalSpace->GetID() == ID)
        {
            mPhysicalSpaces.erase(std::next(mPhysicalSpaces.begin(), i));
        }
    }
}

void VirtualTextureSystemR::LockTile(const VTLocalTile& tile) 
{
    if (mTileLocks.Lock(tile))
    {
        mTilesToLock.push_back(tile);
    }
}

void VirtualTextureSystemR::UnlockTile(const VTLocalTile& tile, const VirtualTextureProducer* producer)
{
    if (mTileLocks.Unlock(tile))
    {
        if (mTilesToLock.size() == 0)
        {
            for (UInt8 ProducerPhysicalGroupIndex = 0u; ProducerPhysicalGroupIndex < producer->GetNumPhysicalGroups(); ++ProducerPhysicalGroupIndex)
            {
                VTPhysicalSpace* PhysicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(ProducerPhysicalGroupIndex);
                VTPagePool& PagePool = PhysicalSpace->GetPagePool();
                const UInt32 pAddress = PagePool.FindPageAddress(tile.GetProducerHandle(), ProducerPhysicalGroupIndex, tile.local_vAddress, static_cast<UInt8>(tile.local_vLevel));
                if (pAddress != ~0u)
                {
                    PagePool.Unlock(mFrame, static_cast<UInt16>(pAddress));
                }
            }
        }
    }
}
void VirtualTextureSystemR::RequestTiles(const UInt2& inScreenSpaceSize, SInt32 inMipLevel) 
{
    for (const auto& pair : mAllocatedVTs)
    {
        RequestTilesInternal(pair.second, inScreenSpaceSize, inMipLevel);
    }
}
void VirtualTextureSystemR::End(NGIStagingBuffer* stagingBuffer, VTFeedbackBufferDesc desc, UInt32 frameIndex, VTFeedbackManager* feedbackManager)
{
    feedbackManager->SaveStagingBuffer(stagingBuffer, desc, frameIndex);

    if (mAllocatedVTs.size() == 0)
    {
        mMappedTilesToProduce.clear();
    }
}
}   // namespace cross
