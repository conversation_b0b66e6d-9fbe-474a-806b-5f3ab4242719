#pragma once
#include "EnginePrefix.h"
#include "RenderEngine/RenderPipeline/Effects/PassBase.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"

namespace cross {
class InstanceCulling
{
public:
    // return the last culling pass, either instance culling or foliage culling
    static std::tuple<REDPass*, REDBufferView*> AssembleInstanceCullingPass(const GameContext& gameContext, REDDrawUnitList* drawUnitList, REDTextureView* prevDepthHiZView, const NameID& passName, const NameID& tagName, bool enableHiZ,
                                                                            bool useCurrFrameHiZ);

    static void AddReadBackDrawCulling(REDPass* pass, REDDrawUnitList* drawUnitList, UInt64 frameId, bool feedBackDebug);

    static void RequestForReadbackDraw(REDDrawUnitList* drawUnitList, UInt64 frameId);

private:
    static std::tuple<REDBufferRef, R<PERSON><PERSON>ufferView*, REDBufferView*> CreateUAVBuffer(RenderingExecutionDescriptor* RED, std::string_view name, UInt32 elementCount, UInt32 elementBytes, NGIBufferUsage additionalBufferUsage = NGIBufferUsage(0));

    static void ClearRWStructuredBuffer(RenderingExecutionDescriptor* RED, const FFSRenderPipelineSetting* renderPipelineSetting, REDBufferView* bufferView, UInt32 value);

    static REDPass* ClearRWStructuredBuffer(RenderingExecutionDescriptor* RED, const FFSRenderPipelineSetting* renderPipelineSetting, REDBufferView* bufferView, UInt32 minOffset, UInt32 maxOffset, UInt32 value);
};
}   // namespace cross