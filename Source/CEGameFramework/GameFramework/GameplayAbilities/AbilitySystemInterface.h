// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Utils/GASMacros.h"

namespace cegf {

class UAbilitySystemComponent;

class GAMEPLAYABILITIES_API IAbilitySystemInterface
{
    GENERATED_IINTERFACE_BODY()

    /** Returns the ability system component to use for this actor. It may live on another actor, such as a Pawn using the PlayerState's component */
    virtual UAbilitySystemComponent* GetAbilitySystemComponent() const = 0;
};
}   // namespace cegf