#pragma once
#include "reflection/objects/rtti_base.hpp"
/** 
 * Get default object of a class.
 * @see UClass::GetDefaultObject()
 */
#ifndef _MANAGED
template<class T>
requires(std::is_base_of_v<gbf::reflection::RttiBase, T>)
inline std::shared_ptr<T> GetDefault()
{
    const gbf::reflection::MetaClass* metaClass = gbf::reflection::query_meta_class<T>();
    std::shared_ptr<T> result;
    if (metaClass)
    {
        gbf::reflection::RttiBase* rtti_base = reinterpret_cast<gbf::reflection::RttiBase*>(metaClass->GetDefaultObject());
        result = std::dynamic_pointer_cast<T>(rtti_base->shared_from_this());
    }
    return result;
}

/**
 * Get default object of a class.
 * @see Class.h
 */
template<class T>
requires(std::is_base_of_v<gbf::reflection::RttiBase, T>)
inline std::shared_ptr<T> GetDefault(const gbf::reflection::MetaClass* metaClass)
{
    std::shared_ptr<T> result;
    if (metaClass)
    {
        gbf::reflection::RttiBase* rtti_base = reinterpret_cast<gbf::reflection::RttiBase*>(metaClass->GetDefaultObject());
        result = std::dynamic_pointer_cast<T>(rtti_base->shared_from_this());
    }
    return result;
}
#endif

template<typename T>
void MakeWeakPtr(gbf::reflection::RttiBase* InPtr, std::weak_ptr<T>& SourceObject)
{
    if (!InPtr)
    {
        return ;
    }
    if (auto shared_rtti = InPtr->shared_from_this(); shared_rtti)
    {
        auto shared_object = std::dynamic_pointer_cast<T>(shared_rtti);
        if (shared_object)
        {
            SourceObject = shared_object;
        }
    }
}

template<typename T>
std::weak_ptr<T> MakeWeakPtr(gbf::reflection::RttiBase* InPtr)
{
    if (!InPtr)
    {
        return {};
    }
    if (auto shared_rtti = InPtr->shared_from_this(); shared_rtti)
    {
        auto shared_object = std::dynamic_pointer_cast<T>(shared_rtti);
        if (shared_object)
        {
            return shared_object;
        }
    }
    return {};
}

template<typename T>
T *WeakPtrGet(const std::weak_ptr<T>& InPtr)
{
    if (auto shared = InPtr.lock(); shared)
    {
        return shared.get();
    }
    return nullptr;
}

template<typename T>
bool WeakPtrEqual(const std::weak_ptr<T>& lhs, const std::weak_ptr<T>& rhs)
{
    return WeakPtrGet(lhs) == WeakPtrGet(rhs);
}


/** Specifies why an actor is being deleted/removed from a level */
namespace EEndPlayReason {
enum Type : int
{
    /** When the Actor or Component is explicitly destroyed. */
    Destroyed,
    /** When the world is being unloaded for a level transition. */
    LevelTransition,
    /** When the world is being unloaded because PIE is ending. */
    EndPlayInEditor,
    /** When the level it is a member of is streamed out. */
    RemovedFromWorld,
    /** When the application is being exited. */
    Quit,
};
}
