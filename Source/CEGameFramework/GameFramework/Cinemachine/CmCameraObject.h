#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Cinemachine/CmCameraComponent.h"

namespace cegf {

class GAMEFRAMEWORK_API CEMeta(Reflect, WorkflowType, Cli, Puerts) CmCameraObject : public GameObject
{
public:
    CEGameplayInternal();
    StaticMetaClassName(CmCameraObject)

    CmCameraObject();

    virtual ~CmCameraObject();

    virtual void InitializeComponents() override;

    CmCameraComponent* GetCmCameraComponent() const { return mCmCameraComponent; }

protected:
    CmCameraComponent* mCmCameraComponent = nullptr;
};

using CmCameraObjectPtr = std::shared_ptr<CmCameraObject>;

}   // namespace cegf
