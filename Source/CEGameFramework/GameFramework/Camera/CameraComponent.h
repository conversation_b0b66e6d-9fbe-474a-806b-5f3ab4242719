#pragma once
#include "GameFramework/Components/Component.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "GameFramework/Camera/CameraTypes.h"
#include "Runtime/GameWorld/CameraSystemG.h"

namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(<PERSON>uerts, Cli, Reflect, WorkflowType) CameraComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(CameraComponent);
    CEMeta(Reflect)
    CameraComponent();

    virtual ~CameraComponent();

    virtual void Activate() override;

    virtual void Deactivate() override;

    CEFunction(ScriptCallable)
    cross::CameraProjectionMode GetProjectionMode() const;

    CEFunction(ScriptCallable)
    void SetProjectionMode(cross::CameraProjectionMode mode);

    CEFunction(ScriptCallable)
    void SetAspectRatio(float aspectRatio);

    CEFunction(ScriptCallable)
    float GetAspectRatio() const;

    CEFunction(ScriptCallable)
    bool GetCameraEnable() const;

    CEFunction(ScriptCallable)
    void SetCameraEnable(bool enable);

    CEFunction(ScriptCallable)
    void SetFocalLength(float focalLength);

    CEFunction(ScriptCallable)
    float GetFocalLength();

    CEFunction(ScriptCallable)
    bool SetCurrentFocusDistance(float val);

    CEFunction(ScriptCallable)
    float GetCurrentFocusDistance();

    CEFunction(ScriptCallable)
    bool SetCurrentAperture(float val);

    CEFunction(ScriptCallable)
    float GetCurrentAperture();

    //perspective mode settings 
    CEFunction(ScriptCallable)
    void SetFOV(float fieldOfView);

    CEFunction(ScriptCallable)
    float GetFOV() const;

    //orthogonal mode settings
    CEFunction(ScriptCallable)
    float GetOrthoWidth() const;

    CEFunction(ScriptCallable)
    bool SetOrthogonalWidth(float width);
    
    CEFunction(ScriptCallable)
    float GetOrthoHeight() const;

    CEFunction(ScriptCallable)
    float GetOrthoNearPlane() const;

    CEFunction(ScriptCallable)
    float GetOrthoFarPlane() const;

    CEFunction(ScriptCallable)
    void SetOrthogonal(float width, float height, float nearDistance, float farDistance);

    CEFunction(ScriptCallable)
    bool SetOrthogonalHeight(float height);

    CEFunction(ScriptCallable)
    bool SetOrthogonalNear(float nearDistance);

    CEFunction(ScriptCallable)
    bool SetOrthogonalFar(float farDistance);

    CEFunction(ScriptCallable)
    bool SetAsMainCamera();

    CEFunction(ScriptCallable)
    bool SetPerspectiveNear(float nearDistance);

    CEFunction(ScriptCallable)
    float GetPerspectiveNear() const;

    CEFunction(ScriptCallable)
    bool SetPerspectiveFar(float farDistance);

    CEFunction(ScriptCallable)
    float GetPerspectiveFar() const;

    CEFunction(ScriptCallable)
    bool SetMinFocalLength(float minFocalLength);

    CEFunction(ScriptCallable)
    float GetMinFocalLength() const;

    CEFunction(ScriptCallable)
    bool SetMaxFocalLength(float maxFocalLength);

    CEFunction(ScriptCallable)
    float GetMaxFocalLength() const;

    CEFunction(ScriptCallable)
    bool SetSensorWidth(float sensorWidth);

    CEFunction(ScriptCallable)
    float GetSensorWidth() const;

    CEFunction(ScriptCallable)
    bool SetSensorHeight(float sensorHeight);

    CEFunction(ScriptCallable)
    float GetSensorHeight() const;

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

    virtual void GetCameraView(float deltaTime, ViewTargetInfo& outTargetInfo);

    cross::Float4x4 GetProjectionMatrix() const;

protected:
};

using CameraComponentPtr = std::shared_ptr<CameraComponent>;

}
