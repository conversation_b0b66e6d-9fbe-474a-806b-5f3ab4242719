#include "GameFramework/GameObjects/ObjectRigRail.h"

namespace cegf
{

ObjectRigRail::ObjectRigRail()
{
    mTickFunction->bCanEverTick = true;
};

void ObjectRigRail::UpdateRailComponents()
{
    if (!GetCrossGameWorld())
    {
        return;
    }

    if (mRailSplineComponent && mRailObjectMount)
    {
        float splineLen = mRailSplineComponent->GetSplineLength();
        auto const mountPos = mRailSplineComponent->GetLocationAtDistanceAlongSpline(splineLen * mCurrentPositionOnRail);

        if (mLockOrientationToRail)
        {
            auto railRot = mRailSplineComponent->GetQuaternionAtDistanceAlongSpline(splineLen * mCurrentPositionOnRail);
            cross::TRS_A transform{};
            transform.mTranslation = static_cast<cross::Double3>(mountPos);
            transform.mRotation = static_cast<cross::Quaternion64>(railRot);

            mRailObjectMount->SetAdditionalTransform(transform);
        }
        else
        {
            mRailObjectMount->SetAdditionalLocation(mountPos);
            // std::cerr << "Current Pos: [" << mountPos.x << ", " << mountPos.y << ", " << mountPos.z << "].\n";
        }
    }
}

void ObjectRigRail::Tick(float deltaTime)
{
    GameObject::Tick(deltaTime);
    // std::cerr << "Current Time: " << mCurrentPositionOnRail << ". ";
    UpdateRailComponents();

    mCurrentPositionOnRail += 0.3f * deltaTime;
    if (mCurrentPositionOnRail > 1.0f)
        mCurrentPositionOnRail -= 1.0f;
}

void ObjectRigRail::InitializeComponents()
{
    mRailSplineComponent = AddComponent<SplineComponent>();
    mRailObjectMount = AddComponent<AdditionalTransformComponent>();
    GameObject::InitializeComponents();
}

void ObjectRigRail::Serialize(SerializeNode& node, SerializeContext& context) const
{
    GameObject::Serialize(node, context);

    node["mCurrentPositionOnRail"] = mCurrentPositionOnRail;
    node["mLockOrientationToRail"] = mLockOrientationToRail;
}

bool ObjectRigRail::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    bool ret = GameObject::Deserialize(in, context);

    mCurrentPositionOnRail = in["mCurrentPositionOnRail"].As<float>();
    mLockOrientationToRail = in["mLockOrientationToRail"].As<bool>();

    return ret;
}

void ObjectRigRail::PostEditChangeProperty(PropertyChangedEvent& PropertyChangedEvent)
{
    GameObject::PostEditChangeProperty(PropertyChangedEvent);
    UpdateRailComponents();
}

}
