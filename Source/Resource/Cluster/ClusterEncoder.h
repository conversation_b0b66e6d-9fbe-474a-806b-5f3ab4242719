#pragma once
#include "CrossBase/Platform/PlatformTypes.h"
#include "ClusterBuilder.h" 
#include <memory>

namespace cross::StellarMesh
{
    struct GPUClusteredMeshData
    {
        MeshStreamData mMeshData;
        std::vector<GPUCluster> mClusters;
    };

	class ClusterEncoder final
	{
	public:
        Resource_API ClusterEncoder(ClusteredMeshData clusteredMeshData);
        Resource_API GPUClusteredMeshData Encode();
        Resource_API ~ClusterEncoder();

	private:
        struct Impl;
        std::unique_ptr<Impl> pImpl;
	};
}