#pragma once
#include "Resource/Resource.h"
#include "MaterialDefines.h"

namespace cross::resource {
class Resource_API CEMeta(Cli, Script) MaterialFunction : public Resource, public MaterialEditorObject
{
public:
    CE_Serialize_Deserialize

    CEMeta(Cli)
    static MaterialFunction* MaterialFunction_CreateMaterialFunction();
    static int GetClassIDStatic()
    {
        return ClassID(MaterialFunction);
    }

    bool Serialize(SerializeNode&& s, const std::string& path) override;

    bool Deserialize(DeserializeNode const& s) override;

    void CopyFrom(const MaterialFunction& function);

    bool ResetResource() override;

    auto& GetMaterialFunctionDefines()
    {
        return mDefines;
    }

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);

private:
    CEMeta(Serialize)
    MaterialFunctionDefines mDefines{};
};

}   // namespace cross::resource
