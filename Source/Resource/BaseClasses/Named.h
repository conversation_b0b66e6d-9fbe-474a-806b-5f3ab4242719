#pragma once
#include "ResourceAsset_generated.h"
#include "RTTI/BaseObject.h"
#include "Resource/resourceforward.h"
namespace cross
{
    class Resource_API Named : public Object
    {
    public:
        Named();
        Named(const std::string& inName);
        Named(const std::string& inName, const std::string& inOriName);
        ~Named() = default;
    public:
        virtual const char* GetName() const { return mName.c_str(); }
        virtual void SetName(char const* name);
        const char* GetOriName() const { return mOriName.c_str(); }
        void Serialize(cross::SimpleSerializer& s) override;
        bool Deserialize(cross::SimpleSerializer const& s) override;
		bool Deserialize(cross::FBSerializer const&s) override;
        bool Deserialize(DeserializeNode const& s) override;
    public:
        std::string mName    = "";
        std::string mOriName = "";//for importer(origin mabye the jpg/png, the name is nda)
    };
}
