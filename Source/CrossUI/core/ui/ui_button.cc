#include "ui_button.h"

namespace oui {

bool ui_button(UIState& state, UIButton& button, ui_style style, ui_rect rect, int layer_index, u32 clip)
{
    primitive_layer* layer = &state.buffers[state.swap_index].layers[layer_index];
    int id = button.id;

    ui_style draw_style = style;
    bool result = false;
    bool hovering = state.IsHovering(rect);
    if (hovering && layer_index >= state.next_hover_layer_index) {
        state.next_hover = id;
        state.next_hover_layer_index = layer_index;
    }

    bool active = state.active == id;
    if (state.active == -1 && hovering && state.hover == id) {
        draw_style.color = style.hover;
    }

    if (state.hover == id && state.left_mouse_press) {
        state.SetActive(id);
    }

    if (active) {
        draw_style.color = style.active;
    }

    if (active&& state.left_mouse_release) {
        if (button.type == UIButtonType::DoubleClick) {
            result = state.IsHovering(rect) && state.double_click_id == id;
            state.double_click_id = id;
        }
        else {
            result = hovering;
        }
        state.ClearActive();
    }

    fill_round_rect_per_corner(layer, draw_style, rect, button.radius, clip);

    ui_label(state, button.label, rect, layer_index, clip);
    return result;
}

}