#pragma once

#include "../types.h"
#include "../state.h"
#include "../uimath.h"
#include "./ui_theme.h"
#include "./ui_layout.h"

#include <functional> 

namespace oui {

enum ClipResult {
    Discard = -1, 
    Keep = 0,
    Clip = 1
};

class UIElement {
public:
    int id;
    float scale = 1.0;
    ui_corner_radius radius;
    ui_offset padding{0};

    UIElement();
    virtual ~UIElement() = default;
};

#define UI_WINDOW_TITLE_HEIGHT 28.f

extern inline ClipResult ui_rect_clip(ui_rect r, ui_rect clip) {
    if (r.x + r.w < clip.x || r.x > clip.x + clip.w) return ClipResult::Discard;
    if (r.y + r.h < clip.y || r.y > clip.y + clip.h) return ClipResult::Discard;
    if (r.x >= clip.x && r.x + r.w <= clip.x + clip.w && r.y >= clip.y && r.y + r.h < clip.y + clip.h) return ClipResult::Keep;
    return ClipResult::Clip;
}

inline bool ui_rect_equals(ui_rect a, ui_rect b) { return a.x == b.x && a.y == b.y && a.w == b.w && a.h == b.h; }

inline bool has_key(std::set<Keycode>* set, Keycode code) { return set->find(code) != set->end(); }

}
