#include "PCH/CrossBasePCHPrivate.h"
#include "Debugging/Debug.h"

#if CROSSENGINE_LINUX
#include <sys/ptrace.h>
#include <sys/prctl.h>
#include <sys/wait.h>
#include <errno.h>

bool IsDebuggerPresent()
{
    int status = 0,
        pid = -1,
        result = 0;

#ifdef PR_SET_PTRACER
    // Guard ancient versions (&*^$%@*&#^%$ build agents)
    // Enable tracing by self and children
    if (0 != prctl(PR_SET_PTRACER, getpid(), 0, 0, 0))
    {
        #if CROSSENGINE_LINUX
        #else
        ErrorString(cross::Format("Unable to enable tracing: {}", strerror(errno)));
        #endif
        return false;
    }
#endif

    pid = fork();
    if (0 > pid)
    {
        #if CROSSENGINE_LINUX
        #else
        ErrorString("Error creating child process");
        #endif
        return false;
    }

    if (0 == pid)
    {
        // Child
        int parent = getppid();

        // Attempt to attach to parent
        if (ptrace(PTRACE_ATTACH, parent, nullptr, nullptr) == 0)
        {
            // Debugger is not attached; continue parent once it stops
            waitpid(parent, nullptr, WUNTRACED | WCONTINUED);
            ptrace(PTRACE_DETACH, getppid(), nullptr, nullptr);
            result = 0;
        }
        else
        {
            // Debugger is already tracing parent
            result = 1;
        }
        exit(result);
    }

    // Parent
    waitpid(pid, &status, 0);
    result = WEXITSTATUS(status);
#ifdef PR_SET_PTRACER
    // Clear tracing
    prctl(PR_SET_PTRACER, 0, 0, 0, 0);
#endif

    return (0 != result);
}
#endif

#if CROSSENGINE_OSX || CROSSENGINE_IOS
// taken from apple technical note QA1361
bool IsDebuggerPresent()
{
    static bool debuggerPresent = false;
    static bool inited = false;

    if (!inited)
    {
        kinfo_proc info;
        ::memset(&info, 0x00, sizeof(info));

        int mib[4] = { CTL_KERN, KERN_PROC, KERN_PROC_PID, ::getpid() };

        size_t size = sizeof(info);
        sysctl(mib, sizeof(mib) / sizeof(*mib), &info, &size, nullptr, 0);

        debuggerPresent = (info.kp_proc.p_flag & P_TRACED) != 0;
        inited = true;
    }

    return debuggerPresent;
}
#endif
