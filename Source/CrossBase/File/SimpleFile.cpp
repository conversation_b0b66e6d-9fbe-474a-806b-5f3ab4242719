#if 0//please use the filesystem to replace simplefile

#include "PCH/CrossBasePCHPrivate.h"
//#include "File/SimpleFile.h"
#include "File/SimpleFileImpl.h"

namespace cross
{
    SimpleFile::SimpleFile()
        : mPImpl(new SimpleFileImpl{})
    {}

    SimpleFile::~SimpleFile()
    {
        if (mPImpl)
            delete mPImpl;
    }

    bool SimpleFile::Open(std::string const& path, File::AccessMode accessMode)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Open(path, accessMode);
    }

    int SimpleFile::GetOpenMode()
    {
		DEBUG_ASSERT(mPImpl);
        return mPImpl->GetOpenMode();
    }

    void SimpleFile::Close()
    {
        DEBUG_ASSERT(mPImpl);
        mPImpl->Close();
    }

    void SimpleFile::Flush()
    {
        DEBUG_ASSERT(mPImpl);
        mPImpl->Flush();
    }

    UInt64 SimpleFile::Read(void * buffer, UInt64 size)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Read(buffer, size);
    }

    bool SimpleFile::Write(const void * buffer, UInt64 size)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Write(buffer, size);
    }

    UInt64 SimpleFile::Size() const
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Size();
    }

    bool SimpleFile::Resize(UInt64 size)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Resize(size);
    }

    bool SimpleFile::SeekRead(SInt64 offset, SeekFrom fromPos)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->SeekRead(offset, fromPos);
    }

    bool SimpleFile::SeekWrite(SInt64 offset, SeekFrom fromPos)
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->SeekWrite(offset, fromPos);
    }

    bool SimpleFile::Eof() const
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->Eof();
    }

    std::string const& SimpleFile::GetFileName() const
    {
        DEBUG_ASSERT(mPImpl);
        return mPImpl->GetFileName();
    }

	bool SimpleFile::ReadTextFile(std::string const& path, std::string& fileContent)
	{
		cross::SimpleFile file;
		std::string abs_path = PathHelper::GetAbsolutePath(path);
		if (!file.Open(abs_path, File::AccessMode::ReadMode))
		{
			LOG_ERROR("Load File {} failed", abs_path);
			return false;
		}
		UInt64 size = UInt64(file.Size());
        fileContent.resize(size);
		file.Read(fileContent.data(), size);
        return true;
	}

}

#endif
