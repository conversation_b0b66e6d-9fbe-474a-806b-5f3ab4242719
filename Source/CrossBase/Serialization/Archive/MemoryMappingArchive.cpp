#ifdef _WINDOWS
    #include "PCH/CrossBasePCHPrivate.h"
    #include "MemoryMappingArchive.h"
#else 
    #include "PCH/CrossBasePCHPrivate.h"
    #include "MemoryMappingArchive.h"
    #include <unistd.h>
#endif

namespace cross {
MemoryMappingArchive::MemoryMappingArchive(filesystem::MemoryMappingFilePtr mmfile)
    : mAnchor(0)
{
    mMappingFile = std::move(mmfile);
}
MemoryMappingArchive::~MemoryMappingArchive() 
{
    mMappingFile.reset();
}
UInt8 const* MemoryMappingArchive::Read(UInt64 size) const
{
    return nullptr;
}
UInt64 MemoryMappingArchive::Read(void* data, UInt64 size) const
{
    if (mAnchor + size > mMappingFile->GetSize())
    {
        return 0;
    }
    memcpy(data, mMappingFile->GetBuffer() + mAnchor, size);
    mAnchor += size;
    return size;
}
void MemoryMappingArchive::Seek(UInt64 pos) const
{
    if (pos > mMappingFile->Tell()) 
    {
        return;//Not valid here
    }
    mAnchor = pos;
}
void MemoryMappingArchive::Remap(UInt64 offset, UInt64 newSize) const
{
     // Need Remap
     UInt64 remapoffset = offset / MemoryMappingArchive::Getpagesize() * MemoryMappingArchive::Getpagesize();
     mMappingFile->Remap(remapoffset, newSize);
     if (mAnchor >= remapoffset)
         mAnchor -= remapoffset;
}
/// get OS page size (for remap)
UInt32 MemoryMappingArchive::Getpagesize()
{
#ifdef _WINDOWS
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    return sysInfo.dwAllocationGranularity;
#else
    return static_cast<UInt32>(sysconf(_SC_PAGESIZE));   //::getpagesize();
#endif
}
}

