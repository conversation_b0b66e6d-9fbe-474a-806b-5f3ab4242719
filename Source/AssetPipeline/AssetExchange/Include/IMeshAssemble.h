#pragma once

#include <cstdint>
#include <functional>
#include <array>
#include "AssetExchangeDefinitions.h"
#include "IMaterialAssemble.h"

namespace CEAssetExchange {

enum class VertexChannel
{
    Unkown = 0,

    Position0 = 32 | 0,
    Position1 = 32 | 1,
    Position2 = 32 | 2,
    Position3 = 32 | 3,
    PositionLast = Position3,

    Color0 = 64 | 0,
    Color1 = 64 | 1,
    Color2 = 64 | 2,
    Color3 = 64 | 3,
    ColorLast = Color3,

    Normal0 = 128 | 0,
    Normal1 = 128 | 1,
    Normal2 = 128 | 2,
    Normal3 = 128 | 3,
    NormalLast = Normal3,

    Tangent0 = 256 | 0,
    Tangent1 = 256 | 1,
    Tangent2 = 256 | 2,
    Tangent3 = 256 | 3,
    TangentLast = Tangent3,

    BiNormal0 = 512 | 0,
    BiNormal1 = 512 | 1,
    BiNormal2 = 512 | 2,
    BiNormal3 = 512 | 3,
    BiNormalLast = BiNormal3,

    BlendIndex0 = 1024 | 0,
    BlendIndex1 = 1024 | 1,
    BlendIndex2 = 1024 | 2,
    BlendIndex3 = 1024 | 3,
    BlendIndexLast = BlendIndex3,

    BlendWeight0 = 2048 | 0,
    BlendWeight1 = 2048 | 1,
    BlendWeight2 = 2048 | 2,
    BlendWeight3 = 2048 | 3,
    BlendWeightLast = BlendWeight3,

    TexCoord0 = 4096 | 0,
    TexCoord1 = 4096 | 1,
    TexCoord2 = 4096 | 2,
    TexCoord3 = 4096 | 3,
    TexCoord4 = 4096 | 4,
    TexCoord5 = 4096 | 5,
    TexCoord6 = 4096 | 6,
    TexCoord7 = 4096 | 7,
    TexCoord8 = 4096 | 8,
    TexCoord9 = 4096 | 9,
    TexCoord10 = 4096 | 10,
    TexCoord11 = 4096 | 11,
    TexCoord12 = 4096 | 12,
    TexCoord13 = 4096 | 13,
    TexCoord14 = 4096 | 14,
    TexCoord15 = 4096 | 15,
    TexCoordLast = TexCoord15,

    PSize0 = 8192 | 0,
    PSize1 = 8192 | 1,
    PSize2 = 8192 | 2,
    PSize3 = 8192 | 3,
    PSizeLast = PSize3,

    PositionT = 16384 | 0,
    PositionTLast = PositionT,

    TessFactor0 = 32768 | 0,
    TessFactor1 = 32768 | 1,
    TessFactor2 = 32768 | 2,
    TessFactor3 = 32768 | 3,
    TessFactorLast = TessFactor3,

    InstanceData = 65536,
    // InstanceData0 = 65536 | 0,
    // InstanceData1 = 65536 | 1,
    // InstanceData2 = 65536 | 2,
    // InstanceData3 = 65536 | 3,
    // InstanceData4 = 65536 | 4,
    // InstanceData5 = 65536 | 5,
    // InstanceData6 = 65536 | 6,
    // InstanceData7 = 65536 | 7,
    // InstanceDataLast = InstanceData7,

    QUATTAN0 = 1 << 17,
    QUATTAN1 = (1 << 17) | 1,
    QUATTAN2 = (1 << 17) | 2,
    QUATTAN3 = (1 << 17) | 3,
    QUATTANLast = QUATTAN3
};

enum class VertexBufferLayout
{
    PositionF3_NormalF3_TangentF3_BiTangentF3_ColorF4_UVF2 = 1,
    PF3_NF3_TF3_BiTF3_CF4_UVF2 = PositionF3_NormalF3_TangentF3_BiTangentF3_ColorF4_UVF2,
};

struct MeshSettings
{
    bool mUseFullPrecisionUV = false;
    bool mUseFullPrecisionTangent = false;
    // store normal and tangents use QTangents.
    bool mUseQTangents = false;
    bool mGenCollisionTree = true;
    bool mIsStreamFile = false;
};
static_assert(std::is_standard_layout<MeshSettings>::value, "Data structure must be standard layout");

struct MeshSection
{
    std::uint32_t mFirstIndex{0};
    std::uint32_t mNumTriangles{0};
    std::uint32_t mMinVertexIndex{0};
    std::uint32_t mMaxVertexIndex{0};

    const char* mName{nullptr};
    const char* mMaterial{nullptr};
    const IMaterialAssemble* mMaterial_A{nullptr};
};
static_assert(std::is_standard_layout<MeshSection>::value, "Data structure must be standard layout");

struct MeshLODSetting
{
    struct LevelSetting
    {
        float mFadeTransitionWidth{0.25f};
        float mScreenRelativeTransitionHeight{0.75f};
    };
    std::array<LevelSetting, 8> mLevelSettings{};

    float mCulledHeight{0.0f};
    bool mIsStreamable{false};
};

struct MeshLODs
{
    const MeshSection* mSections{nullptr};
    std::uintmax_t mSectionsNum{1};
    const std::uint32_t* mLODFirstIndices{nullptr};
    std::uintmax_t mLODFirstIndicesNum{1};
};

struct MeshVertexBuffer
{
    const char* mData{nullptr};
    std::uintmax_t mDataSize{0};
    VertexBufferLayout mLayout{VertexBufferLayout::PF3_NF3_TF3_BiTF3_CF4_UVF2};
    const VertexChannel* mIgnoreChannels{nullptr};
    std::uintmax_t mIgnoreChannelsNum{1};
    std::uint32_t mUVCount{2};
};

struct VertexChannelBuffer
{
    VertexChannel mChannel{VertexChannel::Position0};
    const char* mData{nullptr};
    std::uintmax_t mDataSize{0};
    std::uint32_t mStride{0};
};

struct MeshSplitBuffer
{
    const std::uint32_t* mIndex{nullptr};
    std::uintmax_t mIndexNum{0};
    std::uintmax_t mVertexNum{0};
    const VertexChannelBuffer* mChannelBuffer{nullptr};
    std::uintmax_t mChannelBufferNum{0};
};

struct MeshFlatBuffer
{
    const std::uint32_t* mIndex{nullptr};
    std::uintmax_t mIndexNum{0};
    MeshVertexBuffer* mFlatVertexBuffer{nullptr};
};


enum BoneTransRetgtMode : uint32_t
{
    Animation = 0,
    Skeleton,
    AnimationScaled
};

struct MeshBoneNode
{
    char Name[64];
    uint32_t BoneId;
    int32_t BoneType;
    uint32_t ParentId;
    uint32_t Retarget;
    float BindposeInv[16];
    float BindposeRef[16];
    float WorldMatrix[16];
};

struct BoxCollision
{
    float center[3];
    float rotation[4];
    float extent[3];
};
struct SphereCollision
{
    float center[3];
    float radius;
};
struct CapsuleCollision
{
    float center[3];
    float rotation[4];
    float radius;
    float halfHeight;
};
struct ConvexCollision
{
    float center[3];
    float rotation[4];
    const uint8_t* vertexData;
    std::uint32_t vertexSize;
    std::uint32_t vertexStride;
    const uint8_t* indexData;
    std::uint32_t indexSize;
    std::uint32_t indexStride;
};
struct TriMeshCollision
{
    const uint8_t* vertexData;
    std::uint32_t vertexSize;
    std::uint32_t vertexStride;
    const uint8_t* indexData;
    std::uint32_t indexSize;
    std::uint32_t indexStride;
};
// The abstract interface for mesh assemble
struct IMeshAssemble
{
    virtual void SetRawHeaderBuffers(const std::uint8_t* buffer, const std::uintmax_t size) = 0;

    virtual void SetRawFlatBuffers(const std::uint8_t* buffer, const std::uintmax_t size) = 0;

    virtual std::uint32_t GetMeshPartCount() = 0;

    virtual void GetMeshPartRange(std::uint32_t beginLOD, std::uint32_t endLOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart) = 0;

    virtual std::uint32_t GetLODCount() = 0;

    virtual void GetLODMeshPartRange(std::uint32_t LOD, std::uint32_t* beginMeshPart, std::uint32_t* endMeshPart) = 0;

    virtual std::uint32_t GetLODVertexCount(std::uint32_t LOD) = 0;

    virtual std::uint32_t GetLODIndexCount(std::uint32_t LOD) = 0;

    virtual std::uint32_t GetMeshPartVertexCount(std::uint32_t MeshPart) = 0;

    virtual std::uint32_t GetMeshPartIndexCount(std::uint32_t MeshPart) = 0;

    virtual void RemoveLODRange(std::uint32_t beginLOD, std::uint32_t endLOD) = 0;

    virtual void RemoveLOD(std::uint32_t LOD) = 0;

    virtual void UpdateMeshPart(std::uint32_t Index, const MeshSplitBuffer*) = 0;

    virtual void UpdateMeshPart(std::uint32_t Index, const MeshFlatBuffer*) = 0;

    virtual void SaveToFile() = 0;

    virtual void SetSettings(const MeshSettings*) = 0;

    virtual bool HasBeenDestroyed() = 0;

    virtual void EndAssemble() = 0;

    virtual bool HasEndAssemble() = 0;

    virtual void AddMeshLODs(const MeshLODs* LODs) = 0;

    virtual void AddMeshLODSetting(MeshLODSetting LODSetting) = 0;

    virtual void AddIndexStream(const std::uint32_t* data, const std::uintmax_t size) = 0;

    /// <summary>
    /// Add a flat vertex buffer into this mesh with certain layout
    /// </summary>
    virtual void AddVertexBuffer(const MeshVertexBuffer*) = 0;

    virtual void AddVertexPosition(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexTexCoord(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexColor(const VertexChannel, const std::uint32_t* data, const std::uintmax_t size) = 0;

    virtual void AddVertexNormal(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexTangent(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexBiNormal(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexBoneWeights(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddVertexBoneIds(const VertexChannel, const std::int16_t* data, const std::uintmax_t size) = 0;

    virtual void AddVertexQuatTangent(const VertexChannel, const float* data, const std::uintmax_t size) = 0;

    virtual void AddRefSkeleton(const MeshBoneNode* Nodes, const std::uintmax_t size, const char* name) = 0;
    // virtual void AddBlendShape(uint32_t meshPartIndex, const BlendShapeDeformer&) = 0;

    virtual void AddBoxCollision(const BoxCollision* data, const std::uintmax_t size) = 0;
    virtual void AddSphereCollision(const SphereCollision* data, const std::uintmax_t size) = 0;
    virtual void AddCapsuleCollision(const CapsuleCollision* data, const std::uintmax_t size) = 0;
    virtual void AddConvexCollision(const ConvexCollision* data) = 0;
    virtual void AddTriMeshCollision(const TriMeshCollision* data) = 0;
};

}   // namespace CEAssetExchange
