#include "SDKComponent/SDKCameraComponent.h"
#include "GameFramework/Camera/CameraComponent.h"
namespace cesdk::cegf {

static ::cegf::CameraComponent* GetCameraComponent(const SDKCameraComponent* component)
{
    if (!component || !component->componentInstance)
    {
        return nullptr;
    }

    return static_cast<::cegf::CameraComponent*>(component->componentInstance);
}

cesdk::cegf::CameraProjectionMode SDKCameraComponent::GetProjectionMode() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        switch (CameraComponent->GetProjectionMode())
        {
        case ::cross::CameraProjectionMode::Perspective:
            return cesdk::cegf::CameraProjectionMode::Perspective;
            break;
        case ::cross::CameraProjectionMode::Orthogonal:
            return cesdk::cegf::CameraProjectionMode::Orthogonal;
            break;
        default:
            return cesdk::cegf::CameraProjectionMode::Perspective;
            break;
        }
    }
    return cesdk::cegf::CameraProjectionMode::Perspective;
}
void SDKCameraComponent::SetProjectionMode(cesdk::cegf::CameraProjectionMode mode)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        switch (mode)
        {
        case cesdk::cegf::CameraProjectionMode::Perspective:
            CameraComponent->SetProjectionMode(::cross::CameraProjectionMode::Perspective);
            break;
        case cesdk::cegf::CameraProjectionMode::Orthogonal:
            CameraComponent->SetProjectionMode(::cross::CameraProjectionMode::Orthogonal);
            break;
        default:
            CameraComponent->SetProjectionMode(::cross::CameraProjectionMode::Perspective);
            break;
        }
    }
}
void SDKCameraComponent::SetAspectRatio(float aspectRatio)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->SetAspectRatio(aspectRatio);
    }
}
float SDKCameraComponent::GetAspectRatio() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->GetAspectRatio();
    }
    return 0.0f;
}

bool SDKCameraComponent::GetCameraEnable() const {
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetCameraEnable();
    }
    return false;
}

void SDKCameraComponent::SetCameraEnable(bool enable) {
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->SetCameraEnable(enable);
    }
}

void SDKCameraComponent::SetFocalLength(float focalLength)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->SetFocalLength(focalLength);
    }
}

float SDKCameraComponent::GetFocalLength()
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetFocalLength();
    }
    return 0.0f;
}

// perspective mode settings
void SDKCameraComponent::SetFOV(float fieldOfView)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->SetFOV(fieldOfView);
    }
}

float SDKCameraComponent::GetFOV() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetFOV();
    }
    return 0.0f;
}

// orthogonal mode settings
float SDKCameraComponent::GetOrthoWidth() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetOrthoWidth();
    }
    return 0.0f;
}

bool SDKCameraComponent::SetOrthogonalWidth(float width)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetOrthogonalWidth(width);
    }
    return false;
}

float SDKCameraComponent::GetOrthoHeight() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetOrthoHeight();
    }
    return 0.0f;
}

float SDKCameraComponent::GetOrthoNearPlane() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetOrthoNearPlane();
    }
    return 0.0f;
}

float SDKCameraComponent::GetOrthoFarPlane() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetOrthoFarPlane();
    }
    return 0.0f;
}

void SDKCameraComponent::SetOrthogonal(float width, float height, float nearDistance, float farDistance)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        CameraComponent->SetOrthogonal(width, height, nearDistance, farDistance);
    }
}

bool SDKCameraComponent::SetOrthogonalHeight(float height)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetOrthogonalHeight(height);
    }
    return false;
}

bool SDKCameraComponent::SetOrthogonalNear(float nearDistance)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetOrthogonalNear(nearDistance);
    }
    return false;
}

bool SDKCameraComponent::SetOrthogonalFar(float farDistance)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetOrthogonalFar(farDistance);
    }
    return false;
}

bool SDKCameraComponent::SetAsMainCamera()
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetAsMainCamera();
    }
    return false;
}

bool SDKCameraComponent::SetPerspectiveNear(float nearDistance) 
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetPerspectiveNear(nearDistance);
    }
    return false;
}

float SDKCameraComponent::GetPerspectiveNear() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetPerspectiveNear();
    }
    return 0.1f;
}

bool SDKCameraComponent::SetPerspectiveFar(float farDistance)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetPerspectiveFar(farDistance);
    }
    return false;
}

float SDKCameraComponent::GetPerspectiveFar() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetPerspectiveFar();
    }
    return 1000.0f;
}

bool SDKCameraComponent::SetMinFocalLength(float minFocalLength)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetMinFocalLength(minFocalLength);
    }
    return false;
}

float SDKCameraComponent::GetMinFocalLength() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetMinFocalLength();
    }
    return 10.0f;
}

bool SDKCameraComponent::SetMaxFocalLength(float maxFocalLength)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetMaxFocalLength(maxFocalLength);
    }
    return false;
}

float SDKCameraComponent::GetMaxFocalLength() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetMaxFocalLength();
    }
    return 300.0f;
}

bool SDKCameraComponent::SetCurrentFocusDistance(float val)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetCurrentFocusDistance(val);
    }
    return false;
}

float SDKCameraComponent::GetCurrentFocusDistance() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetCurrentFocusDistance();
    }
    return 10000.0f;
}

bool SDKCameraComponent::SetCurrentAperture(float val)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetCurrentAperture(val);
    }
    return false;
}

float SDKCameraComponent::GetCurrentAperture() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetCurrentAperture();
    }
    return 2.8f;
}

bool SDKCameraComponent::SetSensorWidth(float sensorWidth)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetSensorWidth(sensorWidth);
    }
    return false;
}

float SDKCameraComponent::GetSensorWidth() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetSensorWidth();
    }
    return 36.0f;
}

bool SDKCameraComponent::SetSensorHeight(float sensorHeight)
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->SetSensorHeight(sensorHeight);
    }
    return false;
}

float SDKCameraComponent::GetSensorHeight() const
{
    ::cegf::CameraComponent* CameraComponent = GetCameraComponent(this);
    if (CameraComponent)
    {
        return CameraComponent->GetSensorHeight();
    }
    return 24.0f;
}
}