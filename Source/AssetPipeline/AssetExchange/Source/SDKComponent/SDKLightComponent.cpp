#include "SDKComponent/SDKLightComponent.h"
#include "GameFramework/Components/LightComponent.h"

namespace cesdk::cegf {

// 获取内部的LightComponent指针
static ::cegf::LightComponent* GetLightComponent(const SDKLightComponent* component) 
{
    if (!component || !component->componentInstance)
    {
        return nullptr;
    }
    
    return static_cast<::cegf::LightComponent*>(component->componentInstance);
}

void SDKLightComponent::SetLightType(LightType lightType)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        switch (lightType)
        {
        case LightType::Directional:
            lightComponent->SetLightType(::cross::LightType::Directional);
            break;
        case LightType::Point:
            lightComponent->SetLightType(::cross::LightType::Point);
            break;
        case LightType::Spot:
            lightComponent->SetLightType(::cross::LightType::Spot);
            break;
        case LightType::Rect:
            lightComponent->SetLightType(::cross::LightType::Rect);
            break;
        default:
            lightComponent->SetLightType(::cross::LightType::Directional);
            break;
        }
    }
}

LightType SDKLightComponent::GetLightType() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::LightType lightType = lightComponent->GetLightType();
        switch (lightType) {
        case ::cross::LightType::Directional:
            return LightType::Directional;
            break;
        case ::cross::LightType::Point:
            return LightType::Point;
            break;
        case ::cross::LightType::Spot:
            return LightType::Spot;
            break;
        case ::cross::LightType::Rect:
            return LightType::Rect;
            break;
        default:
            return LightType::Directional;
            break;
        }
    }
    return LightType::Directional;
}

void SDKLightComponent::SetLightColor(const cross::Float3& color)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::Float3 internalColor;
        internalColor.x = color.x;
        internalColor.y = color.y;
        internalColor.z = color.z;
        lightComponent->SetLightColor(internalColor);
    }
}

cross::Float3 SDKLightComponent::GetLightColor() const
{
    cross::Float3 result = { 0.0f, 0.0f, 0.0f };
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::Float3 color = lightComponent->GetLightColor();
        result.x = color.x;
        result.y = color.y;
        result.z = color.z;
    }
    return result;
}

void SDKLightComponent::SetLightIntensity(float intensity)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightIntensity(intensity);
    }
}

float SDKLightComponent::GetLightIntensity() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightIntensity();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightPrtIntensity(float intensity)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightPrtIntensity(intensity);
    }
}

float SDKLightComponent::GetLightPrtIntensity() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightPrtIntensity();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightVolumetricFactor(float factor)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightVolumetricFactor(factor);
    }
}

float SDKLightComponent::GetLightVolumetricFactor() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightVolumetricFactor();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSourceAngleOrRadius(float angleOrRadius)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSourceAngleOrRadius(angleOrRadius);
    }
}

float SDKLightComponent::GetLightSourceAngleOrRadius() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSourceAngleOrRadius();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSoftSourceAngleOrRadius(float angleOrRadius)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSoftSourceAngleOrRadius(angleOrRadius);
    }
}

float SDKLightComponent::GetLightSfotSourceAngleOrRadius() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSoftSourceAngleOrRadius();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSourceLength(float val)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSourceLength(val);
    }
}

float SDKLightComponent::GetLightSourceLength() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSourceLength();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightRange(float range)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightRange(range);
    }
}

float SDKLightComponent::GetLightRange() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightRange();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSourceWidth(float width)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSourceWidth(width);
    }
}

float SDKLightComponent::GetLightSourceWidth() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSourceWidth();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSourceHeight(float height)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSourceHeight(height);
    }
}

float SDKLightComponent::GetLightSourceHeight() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSourceHeight();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightBarnDoorAngle(float angle)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightBarnDoorAngle(angle);
    }
}

float SDKLightComponent::GetLightBarnDoorAngle() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightBarnDoorAngle();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightBarnDoorLength(float length)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightBarnDoorLength(length);
    }
}

float SDKLightComponent::GetLightBarnDoorLength() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightBarnDoorLength();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightCastShadow(bool castShadow)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightCastShadow(castShadow);
    }
}

bool SDKLightComponent::GetLightCastShadow() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightCastShadow();
    }
    return false;
}

void SDKLightComponent::SetLightCastScreenSpaceShadow(bool castScreenSpaceShadow)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightCastScreenSpaceShadow(castScreenSpaceShadow);
    }
}

bool SDKLightComponent::GetLightCastScreenSpaceShadow() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightCastScreenSpaceShadow();
    }
    return false;
}

void SDKLightComponent::SetLightShadowStrength(float strength)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightShadowStrength(strength);
    }
}

float SDKLightComponent::GetLightShadowStrength() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightShadowStrength();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightMode(LightMode mode)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        switch (mode)
        {
        case LightMode::Realtime:
            lightComponent->SetLightMode(::cross::LightMode::Realtime);
            break;
        case LightMode::Mixed:
            lightComponent->SetLightMode(::cross::LightMode::Mixed);
            break;
        case LightMode::Baked:
            lightComponent->SetLightMode(::cross::LightMode::Baked);
            break;
        default:
            lightComponent->SetLightMode(::cross::LightMode::Realtime);
            break;
        }
    }
}

LightMode SDKLightComponent::GetLightMode() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::LightMode mode = lightComponent->GetLightMode();
        switch (mode)
        {

        case ::cross::LightMode::Realtime:
            return LightMode::Realtime;
            break;
        case ::cross::LightMode::Baked:
            return LightMode::Baked;
            break;
        case ::cross::LightMode::Mixed:
            return LightMode::Mixed;
            break;
        }
    }
    return LightMode::Realtime;
}

void SDKLightComponent::SetLightPriority(LightPriority priority)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        switch (priority)
        {
        case LightPriority::Major:
            lightComponent->SetLightPriority(::cross::LightPriority::Major);
            break;
        case LightPriority::Minor:
            lightComponent->SetLightPriority(::cross::LightPriority::Minor);
            break;
        default:
            lightComponent->SetLightPriority(::cross::LightPriority::Major);
            break;
        }
    }
}

LightPriority SDKLightComponent::GetLightPriority() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::LightPriority priority = lightComponent->GetLightPriority();
        switch (priority)
        {
        case ::cross::LightPriority::Major:
            return LightPriority::Major;
            break;
        case ::cross::LightPriority::Minor:
            return LightPriority::Minor;
            break;
        }
    }
    return LightPriority::Major;
}

void SDKLightComponent::SetLightShadowType(LightShadowType shadowType)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        switch (shadowType)
        {
        case LightShadowType::HardShadow:
            lightComponent->SetLightShadowType(::cross::LightShadowType::HardShadow);
            break;
        case LightShadowType::SoftShadowPCF_1X1:
            lightComponent->SetLightShadowType(::cross::LightShadowType::SoftShadowPCF_1X1);
            break;
        case LightShadowType::SoftShadowPCF_3X3:
            lightComponent->SetLightShadowType(::cross::LightShadowType::SoftShadowPCF_3X3);
            break;
        case LightShadowType::SoftShadowPCF_5X5:
            lightComponent->SetLightShadowType(::cross::LightShadowType::SoftShadowPCF_5X5);
            break;
        case LightShadowType::SoftShadowPCSS:
            lightComponent->SetLightShadowType(::cross::LightShadowType::SoftShadowPCSS);

            break;
        case LightShadowType::Count:
            break;
        }
    }
}

LightShadowType SDKLightComponent::GetLightShadowType() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::LightShadowType shadowType = lightComponent->GetLightShadowType();
        switch (shadowType) {
        case ::cross::LightShadowType::HardShadow:
            return LightShadowType::HardShadow;
            break;
        case ::cross::LightShadowType::SoftShadowPCF_1X1:
            return LightShadowType::SoftShadowPCF_1X1;
            break;
        case ::cross::LightShadowType::SoftShadowPCF_3X3:
            return LightShadowType::SoftShadowPCF_3X3;
            break;
        case ::cross::LightShadowType::SoftShadowPCF_5X5:
            return LightShadowType::SoftShadowPCF_5X5;
            break;
        case ::cross::LightShadowType::SoftShadowPCSS:
            return LightShadowType::SoftShadowPCSS;
            break;
        }
    }
    return LightShadowType::HardShadow;
}

void SDKLightComponent::SetLightShadowAmount(float amount)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightShadowAmount(amount);
    }
}

float SDKLightComponent::GetLightShadowAmount() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightShadowAmount();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightShadowBias(float bias)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightShadowBias(bias);
    }
}

float SDKLightComponent::GetLightShadowBias() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightShadowBias();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightShadowSlopeBias(float slopeBias)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightShadowSlopeBias(slopeBias);
    }
}

float SDKLightComponent::GetLightShadowSlopeBias() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightShadowSlopeBias();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightVarianceBiasVSM(float varianceBias)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightVarianceBiasVSM(varianceBias);
    }
}

float SDKLightComponent::GetLightVarianceBiasVSM() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightVarianceBiasVSM();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightLightLeakBiasVSM(float lightLeakBias)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightLightLeakBiasVSM(lightLeakBias);
    }
}

float SDKLightComponent::GetLightLightLeakBiasVSM() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightLightLeakBiasVSM();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightFilterSizePCF(float filterSize)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightFilterSizePCF(filterSize);
    }
}

float SDKLightComponent::GetLightFilterSizePCF() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightFilterSizePCF();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSoftnessPCSS(float softness)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSoftnessPCSS(softness);
    }
}

float SDKLightComponent::GetLightSoftnessPCSS() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSoftnessPCSS();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSampleCountPCSS(float sampleCount)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSampleCountPCSS(sampleCount);
    }
}

float SDKLightComponent::GetLightSampleCountPCSS() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSampleCountPCSS();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightInnerConeAngle(float angle)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightInnerConeAngle(angle);
    }
}

float SDKLightComponent::GetLightInnerConeAngleInDegree() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightInnerConeAngleInDegree();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightOuterConeAngle(float angle)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightOuterConeAngle(angle);
    }
}

float SDKLightComponent::GetLightOuterConeAngleInDegree() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightOuterConeAngleInDegree();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightConeFadeIntensity(float intensity)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightConeFadeIntensity(intensity);
    }
}

float SDKLightComponent::GetLightConeFadeIntensity() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightConeFadeIntensity();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightConeOverFlowLength(float length)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightConeOverFlowLength(length);
    }
}

float SDKLightComponent::GetLightConeOverFlowLength() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightConeOverFlowLength();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightSpotDistanceExp(float distanceExp)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightSpotDistanceExp(distanceExp);
    }
}

float SDKLightComponent::GetLightSpotDistanceExp() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightSpotDistanceExp();
    }
    return 0.0f;
}

void SDKLightComponent::SetLightRenderingLayerMask(uint32_t mask)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightRenderingLayerMask(mask);
    }
}

uint32_t SDKLightComponent::GetLightRenderingLayerMask() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightRenderingLayerMask();
    }
    return 0;
}

void SDKLightComponent::SetLightAtmosphereLightConfig(const AtmosphereLightConfig& config)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::AtmosphereLightConfig internalConfig;
        internalConfig.AtmosphereSunLight = config.AtmosphereSunLight;
        internalConfig.AtmosphereSunLightIndex = config.AtmosphereSunLightIndex;
        internalConfig.AtmosphereSunDiscColorScale = ::cross::Float3{ config.AtmosphereSunDiscColorScale.x, config.AtmosphereSunDiscColorScale.y, config.AtmosphereSunDiscColorScale.z };
        internalConfig.AtmosphereSunDiscIntensityScale = config.AtmosphereSunDiscIntensityScale;
        lightComponent->SetLightAtmosphereLightConfig(internalConfig);
    }
}

AtmosphereLightConfig SDKLightComponent::GetLightAtmosphereLightConfig() const
{
    AtmosphereLightConfig result{};
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        ::cross::AtmosphereLightConfig config = lightComponent->GetLightAtmosphereLightConfig();
        result.AtmosphereSunLight = config.AtmosphereSunLight;
        result.AtmosphereSunLightIndex = config.AtmosphereSunLightIndex;
        result.AtmosphereSunDiscColorScale = cesdk::cross::Float3{ config.AtmosphereSunDiscColorScale.x, config.AtmosphereSunDiscColorScale.y, config.AtmosphereSunDiscColorScale.z };
        result.AtmosphereSunDiscIntensityScale = config.AtmosphereSunDiscIntensityScale;
    }
    return result;
}

void SDKLightComponent::SetLightEnableTransmittance(bool enable)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightEnableTransmittance(enable);
    }
}

bool SDKLightComponent::GetLightEnableTransmittance() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightEnableTransmittance();
    }
    return false;
}

void SDKLightComponent::SetLightEnable(bool enable)
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        lightComponent->SetLightEnable(enable);
    }
}

bool SDKLightComponent::GetLightEnable() const
{
    ::cegf::LightComponent* lightComponent = GetLightComponent(this);
    if (lightComponent)
    {
        return lightComponent->GetLightEnable();
    }
    return false;
}

} // namespace cesdk::cegf
