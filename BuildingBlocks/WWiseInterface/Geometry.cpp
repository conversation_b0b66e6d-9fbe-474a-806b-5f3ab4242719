#include "Geometry.h"

#include "Runtime/Audio/AudioEngine.h"

namespace cross
{
    cross::AudioEngine::ID Hash(void const * const p)
    {
        return static_cast<cross::AudioEngine::ID>(std::hash<void const*>{}(p));
    }

//#pragma optimize( "", off )
    Float4 GetPlaneEquation(Float3 const & a, Float3 const & b, Float3 const & c)
    {
        Float3 const ab = b - a;
        Float3 const ac = c - a;
        Float3 const n = ab.Cross(ac);
        return Float4(n.x, n.y, n.z, -n.Dot(a));
    }

    float GetZFromPlane(Float2 const& p, Float4 const& plane)
    {
        return -(plane.x * p.x + plane.y * p.y + plane.w) / plane.z;
    }

    bool IsPointInTriangle(Float2 const & p, Float2 const & a, Float2 const & b, Float2 const & c)
    {
        Float2 const ab = b - a;
        Float2 const ac = c - a;
        Float2 const bc = c - b;
        Float2 const ap = p - a;
        Float2 const bp = p - b;

        if (ap.Cross(ab) * ap.Cross(ac) >= 0)
            return false;
        if (bp.Cross(ab) * bp.Cross(bc) <= 0) // should be (bp.Dot(ba) * bp.Dot(bc) >= 0)
            return false;
        return true;
    }
//#pragma optimize( "", on )
    //WARNING : not tested
    //int RayTriangle(Float3 const & origin, Float3 const & dir, Float3 const & a, Float3 const & b, Float3 const & c)
    //{
    //    Float3 const ab = b - a;
    //    Float3 const bc = c - b;
    //    Float3 const ca = a - c;
    //    Float3 const ao = origin - a;
    //    Float3 const bo = origin - b;
    //    Float3 const co = origin - c;

    //    Float3 const ncab = ca.Cross(ab);
    //    int ret = 0;
    //    if(Float3::Dot(ao, ncab) > 0)
    //    {
    //        if(Float3::Dot(ao, dir) < 0)
    //            ret = 1;
    //    }
    //    else
    //    {
    //        if(Float3::Dot(ao, dir) > 0)
    //            ret = -1;
    //    }
    //    if (0 == ret)
    //        return 0;

    //    // intersection is in the triangle?
    //    if (Float3::Dot(dir, ao.Cross(bo)) < 0)
    //        return 0;
    //    if (Float3::Dot(dir, bo.Cross(co)) < 0)
    //        return 0;
    //    if (Float3::Dot(dir, co.Cross(ao)) < 0)
    //        return 0;
    //    return ret;
    //}

    //WARNING : not tested
    //int SegmentTriangle(Float3 const & p, Float3 const & q, Float3 const & a, Float3 const & b, Float3 const & c)
    //{
    //    Float3 const ab = b - a;
    //    Float3 const bc = c - b;
    //    Float3 const ca = a - c;
    //    Float3 const pq = q - p;
    //    Float3 const ap = p - a;
    //    Float3 const aq = q - a;
    //    Float3 const bq = q - b;
    //    Float3 const cq = q - c;

    //    // intersects?
    //    int ret = 0;
    //    Float3 const ncab = ca.Cross(ab);
    //    if (Float3::Dot(aq, ncab) > 0)
    //        ++ret;
    //    else
    //        --ret;
    //    if (Float3::Dot(ap, ncab) < 0)
    //        ++ret;
    //    else
    //        --ret;
    //    if (0 == ret)
    //        return 0;

    //    // intersection is in the triangle?
    //    if (Float3::Dot(pq, aq.Cross(bq)) < 0)
    //        return 0;
    //    if (Float3::Dot(pq, bq.Cross(cq)) < 0)
    //        return 0;
    //    if (Float3::Dot(pq, cq.Cross(aq)) < 0)
    //        return 0;
    //    return ret;
    //}
}
