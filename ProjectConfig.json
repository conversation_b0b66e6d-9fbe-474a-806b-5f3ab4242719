{"Launch": "Contents/GlobalAssets/World/FfsGame_Runtime_Production.world", "ClientName": "TENCENT FLIGHT SIMULATOR GAME", "UseSeparatePresentThread": false, "UseFileSystem": true, "ClientScreenWidth": 3840, "ClientScreenHeight": 2160, "ClientScreenScale": 1.0, "MaxTickRates": 60, "EnableQTangents": false, "EnableFoliagePicking": true, "UseGPUSkin": true, "DpiAware": true, "Vsync": true, "ShowRegenerateFxMsgBoxBeforeOpenProject": false, "UnLockCursor": true, "MeshBuilder": {"VertexPerFrame": 1000000}, "TbusppSetting": {"enable": false, "CachedFrameCount": 5, "SimulatorFrameRate": 60, "ShowDebugInfo": false}, "HallModeActivate": false, "UseEngineStats": false, "FfsSetting": {"EnableGrassFoliage": true, "EnableImmediateAssetLoading": true, "PresentOffsetThreshold": 30, "ChaseFrameOffsetThreshold": 30, "LoadingMode": "prefab", "ConfigurationPath": "ConfigDefault.json", "EnableFlightPathLerp": true, "TodSunAngle": 0.018, "PreFrameEntityNum": 50, "VerifyScenesPath": "Contents/GlobalAssets/World/", "AircraftScriptPath": "Contents/GlobalAssets/Scripts/CsvFly.lua", "CameraSetting": {"enable": true, "FocalLength": 22, "SensorWidth": 42, "SensorHeight": 24, "FocusonRotationOffset": [0.0, 0.0, 0.0], "FocusonRotationMultiplier": [1.0, 0.5, 1.0], "ControllableCameras": [{"Mode": "FPP", "FocusonOffset": [0, 300, 1650]}, {"Mode": "TPP", "ViewTargetOffset": [0.0, 2000.0, -15000.0], "ViewTargetEuler": [0.0, -5.0, 0.0], "EnableCameraRotationLag": true, "CameraRotationLagSpeed": 0.5, "RotationScale": [0.0, 0.5, 0.5], "UseFixedUpdate": true, "AdditionalCalibrationOffset": 8000.0}, {"Mode": "LEFT_INSPECTION", "ViewTargetOffset": [-4000.0, 2000.0, 4000.0], "ViewTargetEuler": [-135.0, -20.0, 0.0], "EnableCameraRotationLag": true, "CameraRotationLagSpeed": 0.5, "RotationScale": [0.0, 0.5, 0.5], "UseFixedUpdate": true, "AdditionalCalibrationOffset": 8000.0}, {"Mode": "RIGHT_INSPECTION", "ViewTargetOffset": [4000.0, 2000.0, 4000.0], "ViewTargetEuler": [135.0, -20.0, 0.0], "EnableCameraRotationLag": true, "CameraRotationLagSpeed": 0.5, "RotationScale": [0.0, 0.5, 0.5], "UseFixedUpdate": true, "AdditionalCalibrationOffset": 8000.0}]}, "OnlySkinOnce": false, "PitchLimit": 20.0, "RollLimit": 40.0, "LandingAssistant": {"Enable": true, "MaxDistance": 1000.0, "MinDistance": 50.0, "Mode": 0, "ModeSetting": [{"EnableAutoPilot": true, "EnableAutoStable": false}, {"EnableAutoPilot": false, "EnableAutoStable": true, "TargetPitch": 1.0}]}}, "ClockerSetting": {"StatusServicePort": 18002, "UpdateStatusIntervalMs": 1000, "SendTimeoutMs": 1000, "RecvTimeoutMs": 2000, "ShowDebugInfo": false}, "FullScreen": false, "Renderdoc": false, "ShowDLSSDebugInfo": false, "EnableDLSS": true, "EnableDLSSG": false, "UseFFXFrameInterpolation": false, "TerrainStreamingDistance": 403473.0, "TerrainLoDPixelError": 8.0, "ContentVersion": "10_27_1500", "filesystems": {"res": {"discrete0": {"opener": "os", "root": "%DOC_DIR%"}, "discrete1": {"opener": "os", "root": "%WORK_DIR%"}, "discrete2": {"opener": "os", "root": "%ENGINE_RES_DIR%"}, "discrete3": {"opener": "os", "root": ""}, "zip": {"opener": "os", "root": "%WORK_DIR%"}}, "script": {}}, "FrustumCulling": {"BlockSize": 256}, "DecalSetting": {"enable": true}, "CullingDistanceScale": 1.0, "RenderPipelineSetting": {"GBufferDebugMode": 0, "VolumetricLight": false, "EnableDeferredLighting": true, "EnablePredepth": false, "PerPixelFogTransmittance": true, "EnableBlackScreen": false, "EnableRayTracing": true, "FeedbackDirectDrawOptimization": false, "FeedbackDirectShadowOptimization": false, "FeedBackDebug": false, "EnableTranslucentPassDepthWrite": false, "UseLightBoundCulling": false, "LinkMode": true, "ViewModeVisualizeType": 0, "mFoliageGpuDrivenSettings": {"MaxInstanceCount": 1000000, "MaxLightInstanceCount": 5000, "MaxDrawCount": 4000000, "UseCountBuffer": true, "LODScaleFactor": 1.3, "RandomCullingMin": 1.0, "RandomCullingMax": 500.0, "UseTileBasedDraw": true, "SimpleSpotLight": false, "TileBasedCullHeight": 22.0, "GridPixelSize": 64.0, "GridZSizeNum": 34.0, "MaxCullSizePerCell": 64.0, "GridSplitS": 1.6, "PointLightMesh": "Mesh/PointLightSphere.nda", "SpotLightMesh": "Mesh/SpotLightSphere.nda", "enable": true}, "mDepthPyramidSettings": {"HiZGeneratorShader": "Shader/Features/GPUDriven/DepthPyramid.compute.nda", "SPDHiZGeneratorShader": "Shader/Features/GPUDriven/DepthPyramidSPD.compute.nda", "UseConservativeDepth": false, "UseLargerOffset": true, "enable": false}, "mDrawRenderTextureToAtlasPassSettings": {}, "mApplyDBufferDataPassSettings": {}, "MPCDI_AlphaTexture": "Contents/GlobalAssets/Calibration/ColorCalibration/Default/mpcdi_alpha0.nda", "MPCDI_LutTexture": "Contents/GlobalAssets/Calibration/ColorCalibration/Default/mpcdi_lut0.nda", "MPCDI_WarpMesh": "Contents/GlobalAssets/Calibration/ShenzhenShow/mpcdi_mesh0.nda", "MPCDI_ScreenEffect_Mesh": "Contents/GlobalAssets/Calibration/E190_1/Geometry/mpcdi_screeneffect_mesh1.nda", "mBuildTileCullingSettings": {"GridPixelSize": 64.0, "GridZSizeNum": 32.0, "MaxCullSizePerCell": 32.0, "GridSplitS": 2.05, "BuildTileCullingComputeShader": "ComputeShader/GridCulling.compute.nda", "BuildTileLightCullingComputeShader": "ComputeShader/LightGridCulling.compute.nda", "enable": true}, "mVirtualShadowMapSettings": {"DebugMode": false, "DisableCache": false, "PhysicalPagesDimX": 256, "PhysicalPagesDimY": 16, "SMRTRayCount": 8, "SMRTSamplesPerRay": 4, "ContactShadowLength": 0.0, "ResolutionLodBiasDirectional": -3.0, "ClipmapFirstLevel": 2, "ClipmapLastLevel": 22, "MaskCoarsePagesDirectional": false, "ClipmapFirstCoarseLevel": 15, "ClipmapLastCoarseLevel": 18, "enable": true}, "mLocalLightShadowMapSettings": {"CacheMode": 3, "CacheLevel": 0, "BufferLimitFactor": 0, "LocalLightCacheSize": 4096, "MaxLightShadowMapSize": 512, "MinScreenRatio": 0.001, "MaxStaticRefreshNum": 0, "EnableDynamicShadow": false, "VisualizedLocalShadowCache": false, "enable": true}, "mContactShadowSettings": {"Intensity": 1.0, "Length": 0.1, "EnableDebugRay": false, "EnableDebugRayFreeze": false, "ContactShadowComputeShader": "Shader/Lighting/Shadow/ContactShadows.compute.nda", "enable": true}, "mMassiveLightsShadowSettings": {"Cluster": false, "Transmission": true, "CullBack": false, "Debug": false, "ClusterCount": 8, "ClusterOffsetX": 0.0, "ClusterOffsetZ": 0.0, "ClusterSize": 100.0, "ThresholdScale": 1.0, "StepUV": 0.001, "MinSteps": 2, "MaxSteps": 20, "StepMultiplier": 1.1, "ExcludedDistance": 2.5, "FadeInStartDistance": 150.0, "FadeInEndDistance": 200.0, "EnableDebugRay": false, "EnableDebugRayFreeze": false, "MassiveLightsShadowComputeShader": "Shader/Lighting/Shadow/MassiveLightsShadow/MassiveLightsShadow.compute.nda", "enable": false}, "mSubSampleShadingSettings": {"MSAASampleCount": 4, "EnableAGAA": true, "ComplexPixelThreshold": 0.001, "EnableAggregatedBaseColor": false, "DepthWeight": 0.15, "NormalWeight": 0.35, "RoughnessWeight": 0.5, "ShowComplex": false, "EmssiveConfig": {"EmissiveCustomResolve": true, "EmssiveSampleDiameter": 4, "ResolveMethod": 1}, "enable": true}, "mSkyAtmosphereAdvancedVars": {"SkySampleCountMin": 2.0, "SkySampleCountMax": 32.0, "SkyDistanceToSampleCountMax": 150.0, "SkyViewLUTSampleCountMin": 4.0, "SkyViewLUTSampleCountMax": 32.0, "SkyViewLUTDistanceToSampleCountMax": 150.0, "enable": true}, "mTileBasedDefferedLightingSettings": {"DeferredShadingMtl": "Material/TileBasedDeferred.nda", "StencilDeferredShadingMtl": "Material/StencilDeferred.nda", "StencilDeferredShadingAGAAMtl": "Material/StencilDeferred_AGAA.nda", "Visualize": 0, "enable": true}, "mIndirectLightingCompositeSettings": {"IndirectLightingCompositeMtl": "Material/IndirectLightingComposite.nda", "EnvBRDFTexture": "Texture/envBRDFlut.nda", "mAOSetting": {"mAOType": 1, "mGTAOSetting": {"GTAOComputeShader": "PipelineResource/FFSRP/Shader/Features/AO/GTAO.compute.nda", "EnableBentNormal": true, "EnableMultiBounce": true, "EnableSliceNoise": true, "EnableStepNoise": true, "RelativeSplitDepthDifference": -1.0, "EffectRadius": 200.0, "DenoisePassNum": 2.0, "DenoiseBlurBeta": 1.2, "UseDefaultConstants": false, "EffectFalloffRange": 1.0, "SampleDistributionPower": 1.0, "ThinOccluderCompensation": 0.0, "VisibilityValuePower": 1.0, "Intensity": 1.0, "DepthMIPSamplingOffset": 1.0, "enable": true}, "mRTAOSetting": {"RTAOComputeShader": "PipelineResource/FFSRP/Shader/RayTracing/RayTracingAmbientOcclusion.compute.nda", "enable": true}, "enable": true}, "mReflectionIndirectSetting": {"CaptureCubeArraySize": 128, "ReflectionType": 1, "mSSRSetting": {"Glossy": false, "MaxTraceDistance": 100000.0, "MaxScreenTraceFraction": 30.0, "SlopeCompareTolerance": 10, "SeperatedTranslucentPass": false, "UseFullDepth": true, "EnableDenoiser": 1, "StochasticRoughnessThreshold": 0.4, "NumRays": 4, "NumSteps": 8, "RayRoughnessThreshold": 0.1, "MinNumRays": 1, "MinNumSteps": 64, "StartMipLevel": 0, "FrameIndex": -1, "ViewRange": 1.0, "enable": true}, "mSSPRSetting": {"FlickDeal": false, "ReflectHeight": 0.0, "mTargetWidthRatio": 1.0, "mTargetHeightRatio": 1.0, "Fade2Edge": 1.0, "FadeVertical": 0.6, "SizeByteNum": 6.0, "SSPRComputeShader": "Shader/Features/PostProcess/SSPR/sspr.compute.nda", "enable": false}, "ReflectionIndirectMtl": "Material/ReflectionIndirect.nda", "GenerateMipmapMtl": "EngineResource/Material/ReflectionEnvironmentCubemap.nda", "ReflectionEnvironmentCS": "EngineResource/Shader/ReflectionEnvironment.compute.nda", "EnvBRDFTexture": "Texture/envBRDFlut.nda", "enable": true}, "mHybridGISetting": {"mHybridSurfelSetting": {"mSurfelVisualizeType": 0, "HYBRID_SURFEL_CAPACITY": 50000, "GHybridSurfelDownSampling": 1, "HYBRID_MIN_SURFEL_COVERAGE": 0.01, "SURFEL_RECYCLE_ENABLE": true, "HYBRID_SURFEL_CELL_LIMIT": 32, "Hybrid_SURFEL_EMMISIVE_LONGITUDE": 3.0, "HYBRID_SURFEL_RADIUS_BASELEVEL": 4, "HYBRID_SURFEL_RECYCLE_DISTANCE": 1.0, "HYBRID_SURFEL_RECYCLE_TIME": 60, "HYBRID_SURFEL_RECYCLE_CONVERAGE_BOUNCES": 5, "HYBRID_SURFEL_MAX_RADIUS": 30.0, "HYBRID_SURFEL_MOMENT_RESOLUTION": 4, "HYBRID_SURFEL_RAY_BUDGET": 50000, "HYBRID_SURFEL_RAY_BOOST_MAX": 16, "GHybridSurfelGridCellSize": 2.0, "InterestNormalWeight": 1.0, "InterestDepthWeight": 10.0, "InterestThreshold": 0.1, "enable": true}, "mHybridShortRangeAOSetting": {"mEnableHardwareRayTracing": false, "mNormalBias": 0.1, "mHWRTMaxTraceDistance": 1000.0, "mTraceDistanceScale": 1.0, "mEnableDistanceFalloff": true, "mEnableDebugView": true, "mDownsampleFactor": 1, "mNumPixelRays": 4, "mEnableTemporalAccumulation": true, "mEnableBentNormal": false, "mEnableHorizonSearch": true, "mEnableHorizonSearchHZB": true, "mEnableVisibilityMask": true, "mSliceCount": 2, "mStepPerSlice": 3, "mForegroundSampleRejectDistanceFraction": 0.3, "mForegroundSampleRejectPower": 1.0, "mHistoryDepthTestRelativeThickness": 0.0005, "mSlopeCompareToleranceScale": 0.5, "enable": true}, "mEnableVoxelGI": false, "mIndirectLightingIntensity": 1.0, "mIndirectSpecularLightingIntensity": 0.0, "mIndirectDiffuseLightingIntensity": 1.0, "mEmissiveGIIntensity": 1.0, "mSceneColorWithEmissiveExposureScale": 1.0, "mEnableGISpecularCombine": true, "mReflectionMaxRoughnessToTrace": 1.0, "mReflectionRoughnessFadeLength": 0.5, "mDownsampleFactor": 16, "mAdaptiveProbeMinDownsampleFactor": 4.0, "mProbeOctHalfResolution": false, "mVoxelRadiosityEnable": true, "mRadiosityRaysPerVoxel": 64, "mRadiosityDecayRate": 1.0, "mUseHiZRayTracing": false, "mNumRayCastSteps": 64, "mMaxScreenTraceDistance": 5000.0, "mMaxScreenTraceFraction": 0.3, "mHybridSceneFirstClipmapWorldExtent": 2500.0, "mMinVoxelTraceDistance": 100.0, "mMaxVoxelTraceDistance": 100000.0, "mVoxelTraceSurfaceBias": 0.2, "mEnableFirstVoxelUnHit": false, "mIsolatedPixelFlickingSuppression": true, "mDebugShowIsolatedPixel": false, "mIsolatedPixelThreshold": 2, "mFoliageTwosidedLightingMode": 0, "mFoliageSkyLightLerpDistance": 10000.0, "mLongDistanceUseSkyLight": false, "mLongDistanceUseSkyLightDistStart": 5000.0, "mLongDistanceUseSkyLightDistLerp": 20000.0, "mDiffuseBoost": 1.0, "mIrradianceFormatUseSH3": true, "mBentNormalAODirectSkyLightMethod": false, "mEnableCompositeTraces": true, "mEnableProbeTemporalFilter": true, "mProbeTemporalFilterWithHitDistance": false, "mEnableProbeSpatialFilter": true, "mSpatialFilterNumPasses": 3, "mProbeInterpolationWithNormal": false, "mEnableTemporalFilter": true, "mMaxFramesAccumulated": 14.0, "mNumHistoryAccumulateThres": 0.9, "mFastUpdateScale": 10.0, "mHistoryDistanceThreshold": 0.05, "mDisocclusionDistanceThreshold": 100000.0, "mFixFrameIndex": -1, "mUseTaaRT": false, "mEnablePreVoxelize": true, "mVoxelizeNewSize": true, "mVoxelizeNewSizeScale": 8.0, "mVoxelizeMeshLodIndex": 100, "mVoxelsForceFullVoxelize": false, "mVoxelsForceUpdateLighting": true, "mInjectLightingFromPrevFrame": false, "mEnableVoxelLightCulling": false, "mVoxelChunkSize": 8, "mUseLocalLightShadowCache": true, "mVoxelsKeepNotClear": false, "mClearTextureUseCS": true, "mEnableVoxelOpacityTex": true, "mEnableVoxelOpacityCompactTex": true, "mDebugShowVoxels": false, "mDebugShowVoxelType": 2.0, "mDebugShowClipmap": -1, "mDebugTraceSkyLightingOnly": false, "mDebugTraceSkyLightSH": false, "mDebugShowSH3": false, "mDebugScreenPosition": {"x": 0.5, "y": 0.5}, "mDebugScreenPosition2": {"x": 0.8, "y": 0.8}, "mLockShowSH3": false, "mDebugSHOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "mDebugSHOffset2": {"x": 0.0, "y": 0.0, "z": 0.0}, "mDebugSHScale": {"x": 10.0, "y": 10.0, "z": 10.0}, "mEnableDebugRay": false, "mEnableDebugRayFreeze": false, "mDebugRayPosition": {"x": 0.5, "y": 0.5}, "mDebugRayIndex": -1, "enable": true}, "mEnableAORescale": false, "enable": true}, "mCloudSetting": {"SpatialDown": 0, "TemporalDown": 1, "DebugOption": 0, "CloudTransOrder": 1, "SkyCloudMateial": "0d1792e56ddbb897974e3016ad6c841f", "BlueNoise": "9f300d2ab50a40af834acc18042d102e", "enable": true}, "mDepthOfFieldSetting": {"BlurRadius": 4.0, "FocalLength": 80.0, "Aperture": 1.2, "FocusDistance": 10.0, "DOFAutoFocus": false, "FixedFocusDistance": 10.0, "AutoInfluenceType": 1, "DepthOfFieldAutoFocusComputeShader": "Shader/Features/PostProcess/DepthOfFieldAutoFocus.compute.nda", "DepthOfFieldComputeShader": "Shader/Features/PostProcess/DepthOfField.compute.nda", "enable": false}, "mDLSSSetting": {"DLSSComputeShader": "PipelineResource/FFSRP/Shader/Features/DLSS/DLSS.compute.nda", "MergeDepthComputeShader": "PipelineResource/FFSRP/Shader/Features/DLSS/DLSS.compute.nda", "DLSS_Mode": 4, "bBoostMode": false, "sharpness": 1.0, "preExposure": 1.0, "exposureScale": 1.0, "colorBuffersHDR": true, "indicatorInvertAxisX": false, "indicatorInvertAxisY": false, "dlaaPreset": 0, "qualityPreset": 0, "balancedPreset": 0, "performancePreset": 0, "ultraPerformancePreset": 0, "useAutoExposure": false, "alphaUpscalingEnabled": false, "mSeparateTranslucencyBeforeDLSS": false, "enable": true}, "mDLSSGSetting": {"DLSSG_numFrames": 2, "enable": false}, "mFSR3Setting": {"FSR3ComputeShader": "PipelineResource/FFSRP/Shader/Features/FSR3/FSR3.compute.nda", "FSR_Mode": 1, "SuperResolutionOn": false, "AutoExposure": true, "PreExposure": 1.0, "FrameInterpolationOn": true, "DrawDebugView": false, "DrawDebugTearLines": false, "enable": false}, "mSeparateTranslucencyBlendSetting": {"enable": true}, "mPathTracingSettings": {"EnableTemporalAccumulation": true, "MaxBounce": 4, "SampleMode": 2, "MaxPathIntensity": 10.0, "MaxAccumulatedFrames": 65536, "enable": true}, "mTemporalAntiAliasingSetting": {"WithoutMotionVector": false, "EnableSharpenPass": true, "CurrentFrameWeight": 0.05, "enable": false}, "mFSR2Setting": {"ResetVersion": 0, "ScaleMode": 4, "CustomScaleValue": 1.7, "Sharpening": true, "SharpeningValue": 1.0, "HDR": true, "AutoExposure": true, "PreExposure": 5.0, "MotionVectors": true, "ThinFeature": true, "CameraJumpCutThreshold": 20.0, "DepthReconstruct": true, "DepthDilateUpsample": false, "DepthUseLastFrame": false, "RectifyHistory": true, "SeparateTranslucency": false, "AntiFlicker": {"EnableVelocityExtend": true, "ExtendMin": 0.5, "ExtendMax": 3.0, "VelocityExtendScale": 3.0, "EnableHistoryWeightScale": false, "WeightScale": 2.0, "DistancePower": 2.0, "ViewDepthThreshold": 10000.0}, "MiscFactors": {"DepthClipScale": 1.0, "LumaStabilityScale": 1.0, "DepthClipBaseScale": 4.0, "LumaDiffThreshold": 0.1, "DebugLowResolution": false, "DebugClearJitter": false, "DebugOutput": false}, "FSR2_Accumulate": "ComputeShader/FSR2.1/ffx_fsr2_accumulate_pass.compute.nda", "FSR2_AutogenReactive": "ComputeShader/FSR2.1/ffx_fsr2_autogen_reactive_pass.compute.nda", "FSR2_ComputeLuminancePyramid": "ComputeShader/FSR2.1/ffx_fsr2_compute_luminance_pyramid_pass.compute.nda", "FSR2_DepthClip": "ComputeShader/FSR2.1/ffx_fsr2_depth_clip_pass.compute.nda", "FSR2_Lock": "ComputeShader/FSR2.1/ffx_fsr2_lock_pass.compute.nda", "FSR2_PrepareInputColor": "ComputeShader/FSR2.1/ffx_fsr2_prepare_input_color_pass.compute.nda", "FSR2_RCAS": "ComputeShader/FSR2.1/ffx_fsr2_rcas_pass.compute.nda", "FSR2_ReconstructPreviousDepth": "ComputeShader/FSR2.1/ffx_fsr2_reconstruct_previous_depth_pass.compute.nda", "enable": false}, "mStellarMeshRenderPipelineSetting": {"mStellarMeshCullingPipelineSetting": {"enable": true}, "mStellarMeshRasterizePipelineSetting": {"enable": true}, "mStellarMeshExportDataSetting": {"enable": true}, "mStellarMeshShadingPipelineSetting": {"enable": true}, "mStellarMeshDebugSetting": {"mEnableDebug": false, "mVisualizeMode": 0}, "enable": true}, "CinematicScale": 1, "GlobalUnitScale": 100.0, "LocalLightInverseSquared": false, "UseRenderPipeline": "UseFFSRP", "VisualizeFoliageLOD": false, "EnableFoliageDrawing": true, "EnableVirtualTexture": true, "VTPoolSizeMax": 48.0, "BackBufferFormat": 1, "ViewMode": 0, "DefaultMaterial": "PipelineResource/FFSRP/Shader/Material/Lit/FxTemplate.fx.nda", "DefaultFX": "PipelineResource/FFSRP/Shader/Material/Lit/FxTemplate.fx.nda", "DisableLightCulling": true, "Shadow": true, "ShadowMapResolution": 4096, "PointShadowMapResolution": 128, "SpotShadowMapResolution": 128, "MaxShadowDistance": 1000000.0, "MaxShadowedLightCountPerCamera": 64, "ShadowParam": 1.0, "TransmissionDensityScale": 100.0, "CSMCascadeCount": 4, "CSMTransitionFactor": 0.5, "CSMCascade2Interval": 1, "CSMCascade3Interval": 2, "CSMCascade4Interval": 3, "BoundingSphereScale": 1.15, "CSMForceUpdateThresholdRelativeToRadius": 0.3, "CascadeDistributionExponent": 1.5, "CascadeBiasDistribution": 0.1, "EnableReprojectionShadow": false, "ReprojectionShadowFilterType": 2, "ReprojectionShadowMapResolution": 2048, "ReprojectionDepthBufferBias": 0.02, "EnableHoleFilling": true, "HoleFillingMaxStepCount": 8, "EnableHiZ": true, "EnableShadowVolumeCulling": false, "GPUDrivenLight": true, "UIMSAA": true, "GlobalMipBias": 1, "GlobalLODBias": 0, "MagnifierEnable": false, "MagnifierSize": 0.25, "MagnifierGain": 5.0, "MagnifierX": 0, "MagnifierY": 0, "PatchLength": 1.0}, "BuildingBlockDirectories": ["BuildingBlocks/DGWNetwork", "Source/FullFlightSimulator", "BuildingBlocks/DirectInputInterface", "BuildingBlocks/XInputInterface", "BuildingBlocks/WWiseInterface"], "WeatherPresetsDirectory": "Contents/GlobalAssets/Effect/Weather/Presets/", "MPCDI": {"enabled": false, "left": -45.0, "right": 45.0, "bottom": -8.116374292452722, "top": 8.116374292452722, "pitch": -0.48563396505931555, "yaw": 0.0, "roll": 0, "x": 0.0, "y": 0.10000000149011612, "z": 0.4000000059604645, "ColorTableSize": [3, 3, 3], "GridSize": [3, 2]}, "CrashMonitor": {"enable": true, "CheckIntervalSeconds": 10, "ExpireThreshold": 600}, "ResourceManager": {"GCInterval": 60, "mReleasePerFrame": 60, "mEnableParallelDelete": false}, "qualification": {"ResourcePath": "Contents/GlobalAssets/Qualification/"}, "PresentManager": {"Enable": false, "PresentMode": 1, "BufferCounts": 5, "StatsLog": true, "BlankOffset": 16000}, "TransientResources": {"enable": true, "TransientResourceCache": {"texture": {"garbageCollectLatency": 32, "capacity": 512}, "buffer": {"garbageCollectLatency": 64, "capacity": 1024}}, "bSupportsAllHeapFlags": false}, "TypeScriptDebug": {"enable": true, "debugPort": 9229, "bWaitDebugger": false, "InFlags": "--expose-gc", "ProfileGC": true}, "PixUISetting": {"ForcePaintMode": true, "WindowAsyncMode": false, "FullScreenUI": true, "ShowPixUILog": true, "PushClipFilterArea": 10000}, "Physics": {"RequireRWLock": true}, "Scalability": {"level": -1, "filename": "Scalability.json"}, "Streaming": {"EnableStreaming": true, "EnableStats": false}}