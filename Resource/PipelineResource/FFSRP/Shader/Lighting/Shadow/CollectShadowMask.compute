#pragma compute CS_RenderShadowMask

#define MAX_LIGHTS 16
#define BLOCK_SIZE 8

#include "ShaderLibrary/Common.hlsl"
#include "Material/Lit/LitCommonStruct.hlsl"

SHADER_CONST(bool, CE_USE_DOUBLE_TRANSFORM, false);
SHADER_CONST(bool, USE_REPROJECTION_SHADOW, false);

SamplerState ce_Sampler_Point;
SamplerState ce_Sampler_Clamp;
Texture2D<float> _DepthMap;
Texture2D<float4> _GBuffer1;
Texture2D<float4> _GBuffer2;
Texture2D<float4> _GBuffer3;
RWTexture2DArray<float4> _ShadowMasks;

struct LightData
{
    float4 lightDirPos;
    float4 lightTilePos;
    float4 lightAttenuation;
    float4 lightColor;
    float4 lightSpotDirection;
    int virtualShadowMapId;
    uint _pad[3];
};

StructuredBuffer<LightData> _LightDatas;

cbuffer _cbCommon
{
    matrix ce_InvViewProjMatrix;
    matrix ce_View;
    float _LightCount;
    float _TransmissionDensityScale;
    float4 _ShadowMaskTexelSize; // 1 / texWidth, 1 / texHeight, -1 / texWidth, -1 / texHeight
}

#include "../../Material/NormalBuffer.hlsl"
#include "ShadowInclude.hlsl"
#include "Shadow.hlsl"

float3 GetWorldPosition(float2 screenUV, float depth)
{
    float3 positionNDC = float3(screenUV.x * 2 - 1, (1 - screenUV.y) * 2 - 1, depth);
    float4 positionWS = mul(ce_InvViewProjMatrix, float4(positionNDC, 1));
    return positionWS.xyz / positionWS.w;
}

[numthreads(BLOCK_SIZE, BLOCK_SIZE, 1)]
void CS_RenderShadowMask(uint3 id : SV_DispatchThreadID)
{
    uint2 positionSS = id.xy;
    float2 screenUV = float2(positionSS.x + 0.5, positionSS.y + 0.5) * _ShadowMaskTexelSize.xy;
    float depth = _DepthMap.SampleLevel(ce_Sampler_Point, screenUV, 0.0).r;
    float3 positionWS = GetWorldPosition(screenUV, depth);

    float4 gBuffer1 = _GBuffer1.SampleLevel(ce_Sampler_Point, screenUV, 0.0);
	float3 normalData;
	DecodeFromNormalBuffer(gBuffer1, normalData);
    float3 normalWS = normalData;

    uint materialType = UnpackByte(_GBuffer2.SampleLevel(ce_Sampler_Point, screenUV, 0.0).w);

    bool isSubsurface = false;
    float opacity;

    PCFSamplerSettings shadowSetting = (PCFSamplerSettings)0;
    if (materialType == MaterialType_TwosidedFoliage)
    {
        isSubsurface = true;
        shadowSetting.isSubsurface = true;
        opacity = _GBuffer3.SampleLevel(ce_Sampler_Point, screenUV, 0.0).b;
        shadowSetting.densityMulContant = SubsurfaceDensityFromOpacity(opacity) * _TransmissionDensityScale;
    }
  
    for (uint i = 0; i < _LightCount; i++)
    {
        LightData lightData = _LightDatas[i];

        float4 lightDirPos = lightData.lightDirPos;
        float3 lightTilePos = lightData.lightTilePos.xyz;
        float3 lightSpotDir = lightData.lightSpotDirection.xyz;
        float4 lightAtten = lightData.lightAttenuation.xyzw;
        float lightSpotDirLen = length(lightSpotDir);

        float shadow = 1.0;
        float sssShadow = 1.0;
        if (lightDirPos.w == 0) 
        {
            // Directional Light
            const bool isUseReprojectionShadow = true;

            shadow = GetDirectionalShadowAttenuation(positionWS, positionSS, normalWS, lightDirPos.xyz, opacity, _TransmissionDensityScale, false, isUseReprojectionShadow, _ShadowDatas[i]);
            sssShadow = GetDirectionalShadowAttenuation(positionWS, positionSS, normalWS, lightDirPos.xyz, opacity, _TransmissionDensityScale, isSubsurface, isUseReprojectionShadow, _ShadowDatas[i]);
        }
//         else if (lightSpotDirLen < 0.01) 
//         {
//             // Point Light
//             float3 lightPos = lightDirPos.xyz;
// #ifdef CE_USE_DOUBLE_TRANSFORM
//             lightPos = GetLargeCoordinateReltvPosition(lightPos, lightTilePos, ce_CameraTilePosition);
// #endif
//             float3 L = lightPos - positionWS;
//             float len_p = length(L);
//             L = normalize(L);
//             float atten_p = lightAtten.w;
//             atten_p = saturate((atten_p - len_p * len_p) / atten_p);

//             if (atten_p > 0)
//             {
//                 shadow = GetPointShadowAttenuation(positionWS, lightPos, _ShadowDatas[i]);
//                 sssShadow = GetPointShadowAttenuation(positionWS, lightPos, _ShadowDatas[i], shadowSetting);
//             }
//         }
//         else 
//         {
//             // Spot Light
//             float3 lightPos = lightDirPos.xyz;
// #ifdef CE_USE_DOUBLE_TRANSFORM
//             lightPos = GetLargeCoordinateReltvPosition(lightPos, lightTilePos, ce_CameraTilePosition);
// #endif
//             float3 L = lightPos - positionWS;
//             float len_s = length(L);
//             L = normalize(L);
//             float d_theta = dot(-L, normalize(lightSpotDir.xyz));
//             float inLight = saturate((d_theta - lightAtten.x) / (lightAtten.y - lightAtten.x));
//             if (d_theta < lightAtten.x)
//                 inLight = 0.0;
//             float atten_s = lightAtten.w;
//             float atten = inLight * saturate((atten_s - len_s * len_s) / atten_s);

//             if (atten > 0)
//             {
//                 shadow = GetSpotShadowAttenuation(positionWS, lightPos, lightTilePos, _ShadowDatas[i]);
//                 sssShadow = GetSpotShadowAttenuation(positionWS, lightPos, lightTilePos, _ShadowDatas[i], shadowSetting);
//             }
//         }

        _ShadowMasks[uint3(id.xy, i)] = float4(shadow, sssShadow, ce_CameraPos[0].x * 0.001, 1);
    }
}