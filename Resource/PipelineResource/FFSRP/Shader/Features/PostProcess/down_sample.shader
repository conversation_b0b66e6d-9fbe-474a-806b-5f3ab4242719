Texture2D<float4> toDownSample_texture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer para: register(space0)
{
	float2 TexSize;
	int PassIdx;
}

float Brightness(float3 color)
{
	return color.x * 0.21 + color.y * 0.02 + color.z * 0.77;
    //return max(color.x, max(color.y, color.z));
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}
float4 Box4(float4 p0, float4 p1, float4 p2, float4 p3)
{
	return (p0 + p1 + p2 + p3) * 0.25f;
}
float4 PSMain(VS2PS input) : SV_TARGET
{
	float4 c0 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(-2, -2));
    float4 c1 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(0,-2));
    float4 c2 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(2, -2) );
    float4 c3 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0 , int2(-1, -1) );
    float4 c4 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(1, -1) );
    float4 c5 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(-2, 0) );
    float4 c6 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0);
    float4 c7 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(2, 0) );
    float4 c8 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(-1, 1) );
    float4 c9 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(1, 1) );
    float4 c10 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(-2, 2) );
    float4 c11 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(0, 2));
    float4 c12 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, input.UV, 0, int2(2, 2));
	if	(PassIdx == 0)
	{
		float w0 = 1.0 / (Brightness(c0) + 1.0);
		float w1 = 1.0 / (Brightness(c1) + 1.0);
		float w2 = 1.0 / (Brightness(c2) + 1.0);
		float w3 = 1.0 / (Brightness(c3) + 1.0);
		float w4 = 1.0 / (Brightness(c4) + 1.0);
		float w5 = 1.0 / (Brightness(c5) + 1.0);
		float w6 = 1.0 / (Brightness(c6) + 1.0);
		float w7 = 1.0 / (Brightness(c7) + 1.0);
		float w8 = 1.0 / (Brightness(c8) + 1.0);
		float w9 = 1.0 / (Brightness(c9) + 1.0);
		float w10 = 1.0 / (Brightness(c10) + 1.0);
		float w11 = 1.0 / (Brightness(c11) + 1.0);
		float w12 = 1.0 / (Brightness(c12) + 1.0);
		float sumW = w0 + w1 + w2 + w3 + w4 + w5 + w6 + w7 + w8 + w9 + w10 + w11 + w12;
		c0  = w0  * c0  / sumW;
		c1  = w1  * c1  / sumW;
		c2  = w2  * c2  / sumW;
		c3  = w3  * c3  / sumW;
		c4  = w4  * c4  / sumW;
		c5  = w5  * c5  / sumW;
		c6  = w6  * c6  / sumW;
		c7  = w7  * c7  / sumW;
		c8  = w8  * c8  / sumW;
		c9  = w9  * c9  / sumW;
		c10 = w10 * c10 / sumW;
		c11 = w11 * c11 / sumW;
		c12 = w12 * c12 / sumW;
	}
	
    return Box4(c0, c1, c5, c6) * 0.125f +
    Box4(c1, c2, c6, c7) * 0.125f +
    Box4(c5, c6, c10, c11) * 0.125f +
    Box4(c6, c7, c11, c12) * 0.125f +
    Box4(c3, c4, c8, c9) * 0.5f;
}