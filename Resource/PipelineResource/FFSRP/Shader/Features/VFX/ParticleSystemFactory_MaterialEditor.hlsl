#ifndef PARTICLE_SYSTEM_FACTORY_MATERIAL_EDITOR
#define PARTICLE_SYSTEM_FACTORY_MATERIAL_EDITOR

#define PARTICLE 1
#ifdef PARTICLE
    #undef INSTANCING
#endif

#define ENABLE_VIEW_MODE_VISUALIZE
#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/Lit/LitUEVariables.hlsl"

#include "NParticleDataAccess.hlsl"

#define ALIGN_MODE_UNALIGNED			0
#define ALIGN_MODE_VELOCITY				1
#define ALIGN_MODE_CUSTOM				2

#define SPRITE_FACING_MODE_CAMERA_PLANE 		0
#define SPRITE_FACING_MODE_CAMERA_POSITION 		1
#define SPRITE_FACING_MODE_CUSTOM 				2
#define SPRITE_FACING_MODE_CAMERA_BLEND			3

#define MESH_FACING_MODE_DEFAULT				0
#define MESH_FACING_MODE_VELOCITY				1
#define MESH_FACING_MODE_POSITION				2
#define MESH_FACING_MODE_PLANE					3

SHADER_CONST(bool, ENABLE_LARGE_COORDINATE, false);

float3 RotateMeshParticle(float3 euler, float3 pos)
{
	float sx, cx;
	sincos(euler.x, sx, cx);
	float sy, cy;
	sincos(euler.y, sy, cy);
	float sz, cz;
	sincos(euler.z, sz, cz);
	float3x3 rotMat;
	rotMat[0].xyz = float3(cz * cy, cz * sy * sx - sz * cx, cz * sy * cx + sz * sx);
	rotMat[1].xyz = float3(sz * cy, cz * cx + sz * sy * sx, sz * sy * cx - sx * cz);
	rotMat[2].xyz = float3(-sy, cy * sx, cy * cx);

	float3 output = mul(rotMat, pos);
	return output;
}

#define eps 0.001

#ifndef USED_WITH_NPARTICLE_MESH

float3 TransformParticle(float3 pos, float rotation,float3 axisX, float3 axisY, float3 axisZ, out float3 normal, out float3 tangent)
{
	float sinRotation;
	float cosRotation;
	sincos(rotation, sinRotation, cosRotation);
	float3 SR = axisX * sinRotation;
	float3 SU = axisY * sinRotation;
	float3 CR = axisX * cosRotation;
	float3 CU = axisY * cosRotation;
	axisX = SU + CR;
	axisY = CU - SR;

	float3x3 transform = float3x3(axisX, axisY, axisZ);
	tangent = axisX;
	normal = axisZ;

	return mul(pos, transform);
}

float3 FacingMode(PrimitiveSceneData primData, float3 pos, float3 center, float3 tilePos, float3 customFacingDirection, float rotation, out float3 normal, out float3 tangent)
{
	float3 axisZ = float3(ce_View._m20, ce_View._m21, ce_View._m22);
	float3 axisX = float3(ce_View._m00, ce_View._m01, ce_View._m02);
	float3 axisY = -float3(ce_View._m10, ce_View._m11, ce_View._m12);

	const float3 cameraVector = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePos) - center;
	if (primData.particle_RenderFacingMode != SPRITE_FACING_MODE_CAMERA_PLANE)
	{
		const float3 planeX = axisX;
		const float3 planeY = axisY;
		float3 facingDirection = lerp(cameraVector, customFacingDirection, primData.particle_RenderFacingMode == SPRITE_FACING_MODE_CUSTOM);
		if (length(facingDirection) < eps)
			facingDirection = float3(1, 0, 0);
		float3 up = float3(0, 1, 0);
		if (ENABLE_LARGE_COORDINATE)
			up = normalize(GetLargeCoordinateAbsolutePosition(ce_CameraPos, ce_CameraTilePosition));
		up = -up;
		axisZ = normalize(facingDirection);
		axisX = cross(up, axisZ);
		if (length(axisX) < eps)
		{
			axisX = GetPerpendicularVector(axisZ);
		}
		axisX = normalize(axisX);
		axisY = normalize(cross(axisZ, axisX));

		if (primData.particle_RenderFacingMode == SPRITE_FACING_MODE_CAMERA_BLEND)
		{
			const float cameraDistanceSq = dot(cameraVector, cameraVector);
			const float interp = saturate(cameraDistanceSq * primData.particle_RenderFacingBlend.y - primData.particle_RenderFacingBlend.z);
			axisX = lerp(axisX, planeX, interp);
			axisY = lerp(axisY, planeY, interp);
			//axisZ = normalize(cross(axisX, axisY));
		}
	}

	return TransformParticle(pos, rotation, axisX, axisY, axisZ, normal, tangent);
}

float3 Billboard(float3 input, matrix worldToObject, float3 center)
{
    float3 viewer = mul(worldToObject, float4(ce_CameraPos, 1.0)).xyz;

    float3 zPivot = normalize(viewer - center);
    float3 xPivot = cross(float3(0, 1, 0), zPivot);
	if (length(xPivot) < eps)
	{
		if (abs(zPivot.x) <= abs(zPivot.y) && abs(zPivot.x) <= abs(zPivot.z))
			xPivot = float3(0, -zPivot.z, zPivot.y);
		else if (abs(zPivot.y) <= abs(zPivot.x) && abs(zPivot.y) <= abs(zPivot.z))
			xPivot = float3(-zPivot.z, 0, zPivot.x);
		else
			xPivot = float3(-zPivot.y, zPivot.x, 0);
	}
	xPivot = normalize(xPivot);
    float3 yPivot = normalize(cross(zPivot, xPivot));

    // Use the three vectors to rotate the quad
    float3 centerOffs = input.xyz - center;
    float3 localPos = center + xPivot * centerOffs.x + yPivot * centerOffs.y + zPivot * centerOffs.z;

	return localPos;
}

float3 FaceRenderer(float3 input, float3 faceDirection, float3 center)
{
	float3 zPivot = normalize(faceDirection);
	float3 xPivot = normalize(cross(center, zPivot));
	float3 yPivot = normalize(cross(zPivot, xPivot));

	float3 centerOffs = input.xyz - center;
    float3 localPos = center + xPivot * centerOffs.x + yPivot * centerOffs.y + zPivot * centerOffs.z;
	return localPos;
}

float3 AlignRenderer(in PrimitiveSceneData primData, float3 input, float3 align, float3 center, float3 tilePos, float3 customFacingDirection, float rotation, out float3 normal, out float3 tangent)
{
	align = -align;
	const float3 cameraVector = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePos) - center;
	float3 facingDirection = lerp(cameraVector, customFacingDirection, primData.particle_RenderFacingMode == SPRITE_FACING_MODE_CUSTOM);
	if (length(align) < eps)
		align = float3(1, 0, 0);
	if (length(facingDirection) < eps)
		facingDirection = float3(0, 0, 1);
	align = normalize(align);
    float3 zPivot = normalize(facingDirection);
	float3 xPivot = cross(align, zPivot);
	if (length(xPivot) < eps)
	{
		xPivot = GetPerpendicularVector(align);
	}
	xPivot = normalize(xPivot);
	float3 yPivot = align;
	//zPivot = normalize(cross(xPivot, yPivot));
	yPivot = lerp(yPivot, cross(zPivot, xPivot), primData.particle_RenderFacingMode == SPRITE_FACING_MODE_CUSTOM);

	// float3 centerOffs = input.xyz;
    // float3 localPos = xPivot * centerOffs.x + yPivot * centerOffs.y;
	// normal = cross(xPivot, yPivot);
	// tangent = xPivot;
	// return localPos;

	return TransformParticle(input, rotation, xPivot, yPivot, cross(xPivot, yPivot), normal, tangent);
}

#if !defined(USED_WITH_NPARTICLE_SPRITE)
bool CullParticle(inout ObjectSceneData objData, inout PrimitiveSceneData primData)
{
	bool isCulled = false;

	// Cull by camera distance
	if (objData.particle_CullMask & (1 << 0))
	{
		float minDistance = primData.particle_CameraDistanceCullRange.x;
		float maxDistance = primData.particle_CameraDistanceCullRange.y;
		float3 particle_Position = mul(primData.ce_World, float4(objData.particle_Position, 1.0)).xyz;
		float3 tilePosition;
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
		tilePosition = objData.ce_TilePosition;
#else
		tilePosition = primData.ce_TilePosition;
#endif
		float3 camera_Position = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePosition);
		float camDistSq = distance(particle_Position, camera_Position);
		if (camDistSq < minDistance || camDistSq > maxDistance)
		{
			isCulled = true;
		}
	}

	return isCulled;
}
#endif

void ProcessSpriteParticle(in ObjectSceneData objData, in PrimitiveSceneData primData, inout VSInput vertex, in int objectIndex)
{

#if !defined(USED_WITH_NPARTICLE_SPRITE)
	if (CullParticle(objData, primData))
	{
		vertex.position.xyz = 0.0f;
		return;
	}
#endif

	float3 particle_Position;
	float2 particle_SizeScale;
	float2 particle_Pivot;
	float particle_Rotation;
	float3 particle_AnimatedVelocity;
	float3 particle_SpriteFacing;
	float3 particle_SpriteAlignment;

#if defined(USED_WITH_NPARTICLE_SPRITE)
	int instanceID = objectIndex - globalInstanceOffset;
	particle_Position = NParticleGetPosition(instanceID, float3(0, 0, 0));
	particle_SizeScale = NParticleGetSize(instanceID, float2(1, 1));
	particle_Rotation = NParticleGetRotation(instanceID, 0);
	particle_AnimatedVelocity = NParticleGetVelocity(instanceID, float3(1, 0, 0));
	particle_SpriteFacing = NParticleGetFacing(instanceID, float3(1, 0, 0));
	particle_SpriteAlignment = NParticleGetAlignment(instanceID, float3(1, 0, 0));
	particle_Pivot = NParticleGetPivot(instanceID, primData.particle_DefaultPivot);

	// TODO: Move vis tag culling to culling/sort
	if (NParticleGetVisTag(instanceID, 0) != 0)
	{
		vertex.position.xyz = 0.0f;
		return;
	}
#else
	particle_Position = objData.particle_Position;
	particle_SizeScale = objData.particle_SizeScale.xy;
	particle_Rotation = objData.particle_Rotation.z;
	particle_AnimatedVelocity = objData.particle_AnimatedVelocity;
	particle_SpriteFacing = primData.particle_RenderFacingDirection;
	particle_SpriteAlignment = primData.particle_RenderAlignDirection;
	particle_Pivot = primData.particle_DefaultPivot;
#endif

	particle_Position = mul(primData.ce_World, float4(particle_Position, 1)).xyz;
	particle_AnimatedVelocity = mul(primData.ce_World, float4(particle_AnimatedVelocity, 0)).xyz;
    // scale particle's position
	float4x4 scaleMat;
	scaleMat[0].xyzw = float4(particle_SizeScale.x, 0, 0, 0);
	scaleMat[1].xyzw = float4(0, particle_SizeScale.y, 0, 0);
	scaleMat[2].xyzw = float4(0, 0, 1, 0);
	scaleMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(scaleMat, vertex.position);
	float2 pivotOffset = (particle_Pivot - 0.5.xx) * abs(vertex.position.xy) * 2;
	vertex.position -= float4(pivotOffset, 0.0, 0.0);

	// float3 pivotOffset = float3(particle_DefaultPivot * scale.xy * 100, 0.0);
	// float3 anchorPoint = particle_Position + pivotOffset;

	// float4 rotatedPivot = mul(rotMat, float4(anchorPoint, 1.0));
	// float3 inverseOffset = anchorPoint - rotatedPivot.xyz;
	// vertex.position.xyz += (inverseOffset - pivotOffset);
	float3 normal, tangent;
	float3 tilePosition;
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
	tilePosition = objData.ce_TilePosition;
#else
	tilePosition = primData.ce_TilePosition;
#endif

	float3 customFacingDirection = float3(0, 0, 0), customAlignmentDirection = float3(0, 0, 0);
	customFacingDirection = lerp(customFacingDirection, particle_SpriteFacing, primData.particle_RenderFacingMode == SPRITE_FACING_MODE_CUSTOM);
	customAlignmentDirection = lerp(customAlignmentDirection, particle_SpriteAlignment, primData.particle_RenderAlignMode == ALIGN_MODE_CUSTOM);

	customFacingDirection = mul(primData.ce_World, float4(customFacingDirection, 0)).xyz;
	customAlignmentDirection = mul(primData.ce_World, float4(customAlignmentDirection, 0)).xyz;

    // apply particle's orientation depend on align mode or facing mode
	if (primData.particle_RenderAlignMode == ALIGN_MODE_UNALIGNED)
	{
		vertex.position.xyz = FacingMode(primData, vertex.position.xyz, particle_Position, tilePosition, customFacingDirection, particle_Rotation, normal, tangent);
	}
	else if (primData.particle_RenderAlignMode == ALIGN_MODE_VELOCITY)
	{
		vertex.position.xyz = AlignRenderer(primData, vertex.position.xyz, particle_AnimatedVelocity, particle_Position, tilePosition, customFacingDirection, particle_Rotation, normal, tangent);
	}
	else
	{
		vertex.position.xyz = AlignRenderer(primData, vertex.position.xyz, customAlignmentDirection, particle_Position, tilePosition, customFacingDirection, particle_Rotation, normal, tangent);
	}

	vertex.position.xyz += particle_Position;
    vertex.position.w = 1.0;

#ifdef VERTEX_NEED_NORMAL
	vertex.normal = float4(normal, 0);
#endif

#ifdef VERTEX_NEED_TANGENT
	vertex.tangent = float4(tangent, 0);
#endif

#ifdef VERTEX_NEED_UV
	#ifdef USED_WITH_NPARTICLE_SPRITE
	#else
		// particle's uv depend on SubUV
		float tileWidth = objData.particle_UVScale.z;
		float tileHeight = objData.particle_UVScale.w;
		vertex.uv = float2((1 - vertex.uv.x) * tileWidth + objData.particle_UVScale.x, vertex.uv.y * tileHeight + objData.particle_UVScale.y);
	#endif
#endif
}

#endif

#if !defined(USED_WITH_NPARTICLE_SPRITE)

float3 FacingMode(PrimitiveSceneData primData, float3 pos, float3 center, float3 velocity, float3 tilePos, inout float3 normal, inout float3 tangent)
{
	if (primData.particle_RenderFacingMode == MESH_FACING_MODE_DEFAULT)
		return pos;

	float3 axisY = float3(ce_View._m10, ce_View._m11, ce_View._m12);
	float3 axisX = -float3(ce_View._m20, ce_View._m21, ce_View._m22);
	float3 axisZ = float3(ce_View._m00, ce_View._m01, ce_View._m02);

	const float3 cameraPos = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePos);
	const float3 cameraVector = mul(primData.ce_InvWorld, float4(cameraPos, 1)).xyz - center;
	if (primData.particle_RenderFacingMode != MESH_FACING_MODE_PLANE)
	{
		const float3 planeX = axisX;
		const float3 planeY = axisY;
		float3 facingDirection = normalize(lerp(cameraVector, velocity, primData.particle_RenderFacingMode == MESH_FACING_MODE_VELOCITY));
		if (length(facingDirection) < eps)
			facingDirection = float3(1, 0, 0);
		
		float3 up = float3(0, 1, 0);
		if (ENABLE_LARGE_COORDINATE)
			up = normalize(GetLargeCoordinateAbsolutePosition(ce_CameraPos, ce_CameraTilePosition));

		axisX = normalize(facingDirection);
		axisZ = cross(axisX, up);

		if (length(axisZ) < eps)
		{
			axisZ = GetPerpendicularVector(axisX);
		}
		axisZ = normalize(axisZ);
		axisY = normalize(cross(axisZ, axisX));
	}

	float3x3 transform = float3x3(axisX, axisY, axisZ);

#ifdef VERTEX_NEED_NORMAL
	normal = mul(normal, transform);
#endif

#ifdef VERTEX_NEED_TANGENT
	tangent = mul(tangent, transform);
#endif

	return mul(pos, transform);
}

void ProcessMeshParticle(in ObjectSceneData objData, in PrimitiveSceneData primData, inout VSInput vertex, in int objectIndex)
{
	// if (CullParticle(objData, primData))
	// {
	// 	vertex.position.xyz = 0.0f;
	// 	return;
	// }

	float3 particle_Position;
	float3 particle_SizeScale;
	float3 particle_Velocity;
	float4 particle_Rotation;
	float3 tilePosition;

#if defined(USED_WITH_NPARTICLE_MESH)
	int instanceID = objectIndex - globalInstanceOffset;
	particle_Position = NParticleGetPosition(instanceID, float3(0, 0, 0));
	particle_SizeScale = NParticleGetScale(instanceID, float3(1, 1, 1));
	particle_Rotation = NParticleGetRotation(instanceID, float4(0, 0, 0, 1));
	particle_Velocity = NParticleGetVelocity(instanceID, float3(1, 0, 0));
	tilePosition = primData.ce_TilePosition;
#else
	particle_Position = objData.particle_Position;
	particle_SizeScale = objData.particle_SizeScale.xyz;
	particle_Rotation = float4(objData.particle_Rotation, 0);

	#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
		tilePosition = objData.ce_TilePosition;
	#else
		tilePosition = primData.ce_TilePosition;
	#endif

#endif

	float4x4 scaleMat;
	scaleMat[0].xyzw = float4(particle_SizeScale.x, 0, 0, 0);
	scaleMat[1].xyzw = float4(0, particle_SizeScale.y, 0, 0);
	scaleMat[2].xyzw = float4(0, 0, particle_SizeScale.z, 0);
	scaleMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(scaleMat, vertex.position);

	float4x4 rotMat;
	float3 normal, tangent;

#if defined(USED_WITH_NPARTICLE_MESH)
	rotMat = MatrixUE2CE(QuaternionToMatrix4x4(particle_Rotation));
#else
	float sx, cx;
	sincos(particle_Rotation.x, sx, cx);
	float sy, cy;
	sincos(particle_Rotation.y, sy, cy);
	float sz, cz;
	sincos(particle_Rotation.z, sz, cz);
	rotMat[0].xyzw = float4(cz * cy, cz * sy * sx - sz * cx, cz * sy * cx + sz * sx, 0);
	rotMat[1].xyzw = float4(sz * cy, cz * cx + sz * sy * sx, sz * sy * cx - sx * cz, 0);
	rotMat[2].xyzw = float4(-sy, cy * sx, cy * cx, 0);
	rotMat[3].xyzw = float4(0, 0, 0, 1);
#endif
	vertex.position = mul(rotMat, vertex.position);

#ifdef VERTEX_NEED_NORMAL
	normal = vertex.normal;
	normal = mul(rotMat, float4(normal, 0)).xyz;
#endif

#ifdef VERTEX_NEED_TANGENT
	tangent = vertex.tangent;
	tangent = mul(rotMat, float4(tangent, 0)).xyz;
#endif

	float3x3 model = transpose(inverse(primData.ce_World));

    vertex.position = float4(vertex.position.xyz, 1.0);
#if defined(USED_WITH_NPARTICLE_MESH)
	vertex.position.xyz = FacingMode(primData, vertex.position.xyz, particle_Position, particle_Velocity, tilePosition, normal, tangent);
#endif

	vertex.position.xyz += particle_Position;
	vertex.position = mul(primData.ce_World, vertex.position);


#ifdef VERTEX_NEED_NORMAL
	vertex.normal.xyz = mul(model, normal);
#endif

#ifdef VERTEX_NEED_TANGENT
	vertex.tangent.xyz = mul(model, tangent);
	vertex.tangent.w = 0;
#endif
}

#endif

#endif