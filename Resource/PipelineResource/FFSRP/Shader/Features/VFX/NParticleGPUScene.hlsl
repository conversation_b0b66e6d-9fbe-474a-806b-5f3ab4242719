#ifndef NPARTICLE_GPU_SCENE
#define NPARTICLE_GPU_SCENE

#include "/ShaderLibrary/Common.hlsl"

struct LocalSpaceObjectSceneData
{
    uint ce_PrimitiveIndex;
    uint ce_ObjectCullingGUID;

    uint   particle_CullMask;
    float3 particle_Position;
    float3 particle_AnimatedVelocity;
    float3 particle_SizeScale;
    float3 particle_Rotation;
    float4 particle_UVScale;
    float4 particle_Color;
    float3 particle_SpriteAlignment;
    float3 particle_SpriteFacing;

#ifdef ADDTIONAL_OBJECT_SCENE_DATA
    ADDTIONAL_OBJECT_SCENE_DATA
#endif
};

struct GlobalSpaceObjectSceneData
{
    float3 ce_TilePosition;
    float3 ce_PreTilePosition;

    uint ce_PrimitiveIndex;
    uint ce_ObjectCullingGUID;

    uint   particle_CullMask;
    float3 particle_Position;
    float3 particle_AnimatedVelocity;
    float3 particle_SizeScale;
    float3 particle_Rotation;
    float4 particle_UVScale;
    float4 particle_Color;
    float3 particle_SpriteAlignment;
    float3 particle_SpriteFacing;

#ifdef ADDTIONAL_OBJECT_SCENE_DATA
    ADDTIONAL_OBJECT_SCENE_DATA
#endif
};

RWStructuredBuffer<LocalSpaceObjectSceneData> _LocalSpaceGPUSceneBuffer;
RWStructuredBuffer<GlobalSpaceObjectSceneData> _GlobalSpaceGPUSceneBuffer;

void UpdateLocalSpaceGPUScene(int instanceIndex, in FSimulationContext context)
{
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_CullMask = 0;
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Position = Float3UE2CE(context.MapUpdate.Particles.Position);
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SizeScale = float3(context.MapUpdate.Particles.SpriteSize, 1.0); //context.particleSimulationState.particle_SizeScale;
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_UVScale = float4(1, 1, 1, 1);//context.particleSimulationState.particle_UVScale;
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Color = context.MapUpdate.Particles.Color;//context.particleSimulationState.particle_Color;

#if NPARTICLE_WRITE_VELOCITY
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_AnimatedVelocity = Float3UE2CE(context.MapUpdate.Particles.Velocity);
#else
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_AnimatedVelocity = float3(0, 0, 0);
#endif

#if NPARTICLE_WRITE_ROTATION
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Rotation = float3(context.MapUpdate.Particles.SpriteRotation, 0, 0);
#else
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Rotation = float3(0, 0, 0);
#endif

#if NPARTICLE_WRITE_SPRITE_ALIGNMENT
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteAlignment = Float3UE2CE(context.MapUpdate.Particles.SpriteAlignment);
#endif

#if NPARTICLE_WRITE_SPRITE_FACING
    _LocalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteFacing = Float3UE2CE(context.MapUpdate.Particles.SpriteFacing);
#endif
}

void UpdateGlobalSpaceGPUScene(int instanceIndex, in FSimulationContext context)
{
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_CullMask = 0;
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Position = Float3UE2CE(context.MapUpdate.Particles.Position);
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SizeScale = float3(context.MapUpdate.Particles.SpriteSize, 1.0);//float3(context.MapUpdate.Particles.SpriteSize, 1.0); //context.particleSimulationState.particle_SizeScale;
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_UVScale = float4(1, 1, 1, 1);//context.particleSimulationState.particle_UVScale;
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Color = context.MapUpdate.Particles.Color;//context.particleSimulationState.particle_Color;
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].ce_TilePosition = float3(0, 0, 0);//context.particleSimulationState.particle_TilePosition;

#if NPARTICLE_WRITE_VELOCITY
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_AnimatedVelocity = Float3UE2CE(context.MapUpdate.Particles.Velocity);
#else
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_AnimatedVelocity = float3(0, 0, 0);
#endif

#if NPARTICLE_WRITE_ROTATION
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Rotation = float3(context.MapUpdate.Particles.SpriteRotation, 0, 0);
#else
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_Rotation = float3(0, 0, 0);
#endif

#if NPARTICLE_WRITE_SPRITE_ALIGNMENT
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteAlignment = Float3UE2CE(context.MapUpdate.Particles.SpriteAlignment);
#else
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteAlignment = float3(0, 0, 0);
#endif

#if NPARTICLE_WRITE_SPRITE_FACING
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteFacing = Float3UE2CE(context.MapUpdate.Particles.SpriteFacing);
#else
    _GlobalSpaceGPUSceneBuffer[instanceIndex + _GPUSceneBufferOffset].particle_SpriteFacing = float3(0, 0, 0);
#endif
}

#endif