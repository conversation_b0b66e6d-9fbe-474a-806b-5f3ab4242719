// #pragma enable debug_symbol
#pragma compute LocalSpaceParticleSimulationTick
#pragma compute GlobalSpaceParticleSimulationTick
#pragma only_renderers vulkan
#include "/Features/VFX/ParticleSystemUtils.hlsl"
#include "/Features/VFX/ParticleSystemGPUDriven.hlsl"

#define OVERRIDE_DASHBOARD

cbuffer cbPassMeta : register(space0)
{
    matrix ce_View0;
    matrix ce_InvView0;

    matrix ce_PreView0;
    matrix ce_PreInvView0;

    float3 ce_CameraTilePosition0;
    float3 ce_PreCameraTilePosition0;
}

void OverrideDashboard()
{
}

void OverrideInitStage(const uint particleIndex, inout ParticleGPUSimulationContext context)
{
}

void OverrideUpdateStage(const uint particleIndex, inout ParticleGPUSimulationContext context)
{
    //local space
    float4 posW = mul(_SystemRelativeMatrix, float4(context.particleSimulationState.particle_Position, 1));
    float4 prePos = float4(GetLargeCoordinateReltvPosition(posW.xyz, _SystemTilePosition, ce_PreCameraTilePosition0), 1);
    prePos = mul(ce_PreView0, prePos);
    float4 pos = float4(GetLargeCoordinateReltvPosition(posW.xyz, _SystemTilePosition, ce_CameraTilePosition0), 1);
    pos = mul(ce_View0, pos);

    float3 mv = mul(_SystemInverseRelativeMatrix, mul(ce_InvView0, pos - prePos)).xyz;
    context.particleSimulationState.particle_AnimatedVelocity += mv * 60;
}

#include "/Features/VFX/ParticleGPUSimulationCS.hlsl"