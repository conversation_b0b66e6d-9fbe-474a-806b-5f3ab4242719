#pragma compute HybridRadiosityCS
#pragma compute HybridInjectLightingFromPrevFrameCS

//#pragma enable debug_symbol
#define USE_CLIPMAP_WRAP 1
#include "ScreenTextures.hlsl"
#include "SceneView.hlsl"
#include "Common.hlsl"
#include "FinalGatherCommon.hlsl"
// #include "SHCommon.hlsl"
#include "FinalGatherInterpolation.hlsl"
#include "HybridTracingCommon.hlsl"
#include "MonteCarlo.hlsl"

// [Frisvad 2012, "Building an Orthonormal Basis from a 3D Unit Vector Without Normalization"]
// Discontinuity at TangentZ.z < -0.9999999f
float3x3 GetTangentBasisFrisvad(float3 TangentZ)
{
	float3 TangentX;
	float3 TangentY;

	if (TangentZ.z < -0.9999999f)
	{
		TangentX = float3(0, -1, 0);
		TangentY = float3(-1, 0, 0);
	}
	else
	{
		float A = 1.0f / (1.0f + TangentZ.z);
		float B = -TangentZ.x * TangentZ.y * A;
		TangentX = float3(1.0f - TangentZ.x * TangentZ.x * A, B, -TangentZ.x);
		TangentY = float3(B, 1.0f - TangentZ.y * TangentZ.y * A, -TangentZ.y);
	}

	return float3x3( TangentX, TangentY, TangentZ );
}

SHADER_CONST(uint, RaysPerVoxel2D, 3);

void GetRadiosityRay(float3 WorldNormal, float2 ProbeUV, out float3 WorldRayDirection, out float ConeHalfAngle, out float PDF)
{
	float4 Sample = CosineSampleHemisphere(ProbeUV);
	float3 LocalRayDirection = Sample.xyz;
	PDF = Sample.w;

    uint NumTracesPerProbe = RaysPerVoxel2D * RaysPerVoxel2D;

	float3x3 TangentBasis = GetTangentBasisFrisvad(WorldNormal);
	WorldRayDirection = mul(LocalRayDirection, TangentBasis);
	ConeHalfAngle = acosFast(1.0f - 1.0f / (float)(NumTracesPerProbe));
}

float3 Cutoff(float3 value, float maxbound)
{
	float Intensity = value.x + value.y + value.z;
    float3 result = select(value, Intensity < maxbound, value / Intensity * maxbound);
    return result;
}

cbuffer cbTraceVoxels
{
	float MaxTraceDistance;
	float StepFactor;
	float MinTraceDistance;
	float SurfaceBias;

	float DecayRate;
}

RWTexture3D<float4> RWLightingTexture;

StructuredBuffer<uint> VoxelVisBuffer;

groupshared float3 SharedLightings[THREADGROUP_SIZE * THREADGROUP_SIZE];

[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, 1)]
void HybridRadiosityCS(uint GroupIndex:SV_GroupIndex,
	uint3 GroupId : SV_GroupID,
	uint3 GroupThreadId : SV_GroupThreadID,
	uint3 DispatchThreadId : SV_DispatchThreadID)
{
    if (any(GroupThreadId >= RaysPerVoxel2D))
        return;

    uint3 DispatchCoord = UnpackVoxelInfo(VoxelVisBuffer[GroupId.x]);

    uint3 TexelCoord = DispatchCoord + uint3(0, ClipmapIndex * ClipmapResolution.y, 0);

    uint3 VoxelCoord = uint3(DispatchCoord.x, DispatchCoord.y, DispatchCoord.z / 6);
    uint Direction = (DispatchCoord.z % 6) / 3;

    float voxelSize = ClipmapVoxelSizeAndRadius[ClipmapIndex].x;
	float3 WorldPosition = GetClipmapToWorldPosition(ClipmapIndex, VoxelCoord, Direction);

    float4 WorldNormal = UnpackRGBA8Normal(NormalTexture.Load(uint4(TexelCoord, 0)));
    WorldNormal.xyz = normalize(WorldNormal.xyz * 2.0f - 1.0);
    
    float3 SamplePosition = WorldPosition + WorldNormal.xyz * (1.f - WorldNormal.w) * voxelSize;

    uint TraceResolution = RaysPerVoxel2D;

    float2 ProbeUV = (GroupThreadId.xy + float2(.5f, .5f)) / float(TraceResolution);
	float ConeHalfAngle;
    float3 WorldConeDirection;
    float PDF;
    GetRadiosityRay(WorldNormal.xyz,ProbeUV,WorldConeDirection,ConeHalfAngle,PDF);

    uint ThreadIndex = GroupThreadId.y * RaysPerVoxel2D + GroupThreadId.x;

    float NoL = dot(WorldConeDirection,WorldNormal.xyz);

    if(NoL > 0.0f)
    {
        float TraceDistance = MaxTraceDistance;
        FConeTraceInput TraceInput = (FConeTraceInput)0;
        SetupTraceInput(TraceInput,SamplePosition, WorldConeDirection, ConeHalfAngle, MinTraceDistance, TraceDistance, StepFactor);

        FConeTraceResult TraceResult = (FConeTraceResult)0;
        SetupTraceResult(TraceResult,TraceInput);
        ConeTraceVoxels(true, TraceInput, TraceResult);

        if (TraceResult.Transparency <= .5f)
        {
             SharedLightings[ThreadIndex] = TraceResult.Lighting * NoL / PDF;
        }
        else
        {
            EvaluateSkyRadianceForCone(WorldConeDirection, tan(ConeHalfAngle), TraceResult);
            SharedLightings[ThreadIndex] = TraceResult.Lighting * NoL / PDF;
            //SharedLightings[ThreadIndex] = 0.0;
        }       
    }
    else
    {
        SharedLightings[ThreadIndex] = 0.0;
    }    

    GroupMemoryBarrierWithGroupSync();

    if(GroupThreadId.x == 0 && GroupThreadId.y == 0)
    {
        float3 Irradiance = 0.0;

        // UNROLL
        for(uint Index = 0; Index < RaysPerVoxel2D * RaysPerVoxel2D; ++Index)
        {
            Irradiance += SharedLightings[Index];
        }

        Irradiance = Irradiance / (TraceResolution * TraceResolution);

        // Hard Cut Off method
        //Irradiance = Cutoff(Irradiance, MaxIntensity);
        
        //Recursive Decay method
        Irradiance *= DecayRate;
		
        float4 DiffuseColor = UnpackRGBA8Color(AlbedoTexture.Load(uint4(TexelCoord, 0)));
#if DEBUG_VXGI_DIFFUSE_LAMBERT_TEST
        Irradiance *= Diffuse_Lambert(DiffuseColor.xyz);
#else
        Irradiance *= DiffuseColor.rgb;
#endif
        RWLightingTexture[DispatchCoord] = float4(Irradiance, 0.0f);
    }
}

cbuffer _cbInjectLightingFromPrevFrame
{
    uint ResolutionDownsampleFactor;
    uint FrameIndex;
    RWTexture2D<float4> RWDebugTex;
}

#define ENABLE_SKY_LIGHT_FOR_COMPENSATE 0
#define REPROJECT_USE_MOTION_VECTOR 0

float3 PrevDiffuseOutputToIrradiance(float3 PreRadiance)
{
    float3 Ret = sqrt(PreRadiance * PI);

    Ret = -min(-Ret, 0.f);
    return Ret;
}

[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, 1)]
void HybridInjectLightingFromPrevFrameCS(
    uint3 GroupId : SV_GroupID,
    uint3 DispatchThreadId : SV_DispatchThreadID,
    uint3 GroupThreadId : SV_GroupThreadID)
{
    uint2 BasePixelCoord = DispatchThreadId.xy * ResolutionDownsampleFactor;
    uint2 SampledPixelCoord = BasePixelCoord + uint2(
        FrameIndex % ResolutionDownsampleFactor, (FrameIndex >> 2) % ResolutionDownsampleFactor
    );

    float2 SampleUV = (SampledPixelCoord + .5f) * _View_BufferSizeAndInvSize.zw;
    float2 ScreenPosition = (SampleUV.xy - _View_ScreenPositionScaleBias.zw) / _View_ScreenPositionScaleBias.xy;

    float4 DiffuseIndirect;
    float DeviceZ;

#if REPROJECT_USE_MOTION_VECTOR
    float2 PreUV = SampleUV + SampleMotionVector(SampleUV);
    if (any(PreUV < 0.f) || any(PreUV) > 1.f)
        return;
    
    float2 PreScreenPosition = (PreUV.xy - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;
    DeviceZ = _PrevDepthMap.SampleLevel(ce_Sampler_Point, PreUV, 0.f).r;
    float3 LastFrameWorldPosition = GetWorldPositionFromScreenPosition(PreScreenPosition, DeviceZ);

    DiffuseIndirect = _PrevDiffuseOutput.SampleLevel(ce_Sampler_Clamp, PreUV, 0.f);
#else
    DeviceZ = _PrevDepthMap.SampleLevel(ce_Sampler_Point, SampleUV, 0.f).r;
    float4 LastFrameWorldPosition = mul(ce_PreInvViewProjMatrix, float4(ScreenPosition, DeviceZ, 1.f));
    LastFrameWorldPosition /= LastFrameWorldPosition.w;
    DiffuseIndirect = _PrevDiffuseOutput.SampleLevel(ce_Sampler_Clamp, SampleUV, 0.f);
#endif

    uint3 VoxelCoord = GetGlobalVoxelCoord(ClipmapIndex, LastFrameWorldPosition.xyz);
    VoxelCoord = clamp(VoxelCoord, 0, GetClipmapResolution() - 1);

    // TODO: Use ce_PreCameraPos
    float3 LastFrame_V = normalize(ce_CameraPos - LastFrameWorldPosition);

    float3 FaceDirections[6] = {
        float3(1, 0, 0),
        float3(0, 1, 0),
        float3(0, 0, 1),
        float3(-1, 0, 0),
        float3(0, -1, 0),
        float3(0, 0, -1)
    };

    for (int i = 0; i < 6; i++)
    {
        uint FaceDirection = i;
        uint3 TexCoord = uint3(VoxelCoord.xy, VoxelCoord.z * 6 + FaceDirection);
        uint3 MaterialTexQueryCoord = GetClipmapCoordinate(ClipmapIndex, FaceDirection, VoxelCoord);
        float4 DiffuseColor = UnpackRGBA8Color(AlbedoTexture.Load(uint4(MaterialTexQueryCoord, 0)));
        float4 WorldNormal = UnpackRGBA8Normal(NormalTexture.Load(uint4(MaterialTexQueryCoord, 0)));
        WorldNormal.xyz = normalize(WorldNormal.xyz * 2.0f - 1.0);

        // TODO: Distance, Normal Weighted
        float3 DiffuseIndirectRadiance = DiffuseIndirect.rgb * DiffuseColor.rgb;
        if (DiffuseColor.w > 0.f && dot(LastFrame_V, WorldNormal) > 0.f)
        {
            RWLightingTexture[TexCoord] = float4(PrevDiffuseOutputToIrradiance(DiffuseIndirectRadiance), 1.f);
        }

#if ENABLE_SKY_LIGHT_FOR_COMPENSATE
        if (DiffuseColor.w > 0.f)
        {
            float4 WorldNormal = UnpackRGBA8Normal(NormalTexture.Load(uint4(TexCoord, 0)));
            WorldNormal.xyz = normalize(WorldNormal.xyz * 2.0f - 1.0);
            float3 SkyLightIrradiance = 0.f;
            const uint TraceResolution = 8;
            for (int i = 0; i < TraceResolution; i++)
            {
                for (int j = 0; j < TraceResolution; j++)
                {
                    float2 ProbeUV = (uint2(i, j) + .5f) / (float)TraceResolution;
                    float ConeHalfAngle;
                    float3 WorldConeDirection;
                    float PDF;
                    GetRadiosityRay(WorldNormal.xyz, ProbeUV, WorldConeDirection, ConeHalfAngle, PDF);
                    float NoL = dot(WorldConeDirection, WorldNormal.xyz);
                    if (NoL > 0.f)
                    {
                        FConeTraceResult TraceResult;
                        TraceResult.Transparency = 1.f;
                        EvaluateSkyRadianceForCone(WorldConeDirection, tan(ConeHalfAngle), TraceResult);

                        SkyLightIrradiance += TraceResult.Lighting * NoL / PDF;
                    }
                }
            }

            float3 SkyLightRadiance = SkyLightIrradiance / (TraceResolution * TraceResolution) * Diffuse_Lambert(DiffuseColor.rgb);

            if (all(DiffuseIndirectRadiance < 1e-4f))
            {
                RWLightingTexture[TexCoord].rgb = clamp(SkyLightRadiance, 0, 5.f);
            }
            else
            {
                RWLightingTexture[TexCoord].rgb = DiffuseIndirectRadiance;
            }
        }
#endif

    }
}
