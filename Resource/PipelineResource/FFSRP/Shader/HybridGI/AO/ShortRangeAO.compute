#pragma compute ScreenSpaceShortRangeAOCS

#pragma enable debug_symbol

//#define HZB_TRACE_FULL_RES_DEPTH_OPT 1

#include "../../ShaderLibrary/Common.hlsl"
#include "../../Material/BSDF.hlsl"
#include "../MonteCarlo.hlsl"
//#include "../BlueNoise.ush"
#include "../ShadingModelsSampling.hlsl"
#include "../ScreenTextures.hlsl"

// to do screen space tracing
#include "../Common.hlsl"
#include "../FinalGatherCommon.hlsl"
#include "../SSRTRayCast.hlsl"
#include "../../Lighting/LightMap.h"
#include "../../Lighting/LightLoop/IndirectDiffuse.hlsl"
#include "ScreenSpaceBentNormal.hlsl"
//#include "../ShaderPrint.ush"

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 1
#endif



SHADER_CONST(bool, HORIZON_SEARCH_USE_HZB, true)
SHADER_CONST(bool, HORIZON_SEARCH_VISIBILITY_BITMASK, true)
SHADER_CONST(bool, OUTPUT_BENT_NORMAL, true)
SHADER_CONST(bool, HORIZON_SEARCH, true)
SHADER_CONST(uint, NUM_PIXEL_RAYS, 4)
SHADER_CONST(uint, DOWNSAMPLE_FACTOR, 1)


cbuffer cbShortRangeAO
{
    
    float ForegroundSampleRejectDistanceFraction;
    float ForegroundSampleRejectPower;
    
    float HistoryDepthTestRelativeThickness;
    float2 MaxScreenFractionForAO;

    float SlopeCompareToleranceScale;

    uint2 ShortRangeAOViewMin;
    uint2 ShortRangeAOViewSize;

    uint SliceCount;
    uint StepsPerSliceForAO;

    float TraceDistanceScale;

};

#define FurthestHZBTextureSampler ce_Sampler_Point
#define GlobalPointClampedSampler ce_Sampler_Point

Texture2D FurthestHZBTexture;

// "Efficiently building a matrix to rotate one vector to another"
float3x3 BuildRotationMatrix(float3 FromDirection, float3 ToDirection)
{
    const float e = dot(FromDirection, ToDirection);
    const float f = abs(e);

    if (f > float(1.0f - 0.0003f))
    {
        return float3x3(1, 0, 0, 0, 1, 0, 0, 0, 1);
    }

    const float3 v = cross(FromDirection, ToDirection);
    const float h = (1.0) / (1.0 + e);
    const float hvx = h * v.x;
    const float hvz = h * v.z;
    const float hvxy = hvx * v.y;
    const float hvxz = hvx * v.z;
    const float hvyz = hvz * v.y;

    float3x3 mtx;
    mtx[0][0] = e + hvx * v.x;
    mtx[0][1] = hvxy - v.z;
    mtx[0][2] = hvxz + v.y;

    mtx[1][0] = hvxy + v.z;
    mtx[1][1] = e + h * v.y * v.y;
    mtx[1][2] = hvyz - v.x;

    mtx[2][0] = hvxz - v.y;
    mtx[2][1] = hvyz + v.x;
    mtx[2][2] = e + hvz * v.z;

    return mtx;
}


float3 ScreenToViewPos(float2 ViewportUV, float SceneDepth)
{
	float2 ProjViewPos;

	ProjViewPos.x =  ViewportUV.x * _View_ScreenToViewSpace.x  + _View_ScreenToViewSpace.z;
	ProjViewPos.y =  ViewportUV.y * _View_ScreenToViewSpace.y  + _View_ScreenToViewSpace.w;
	return float3(ProjViewPos * SceneDepth, SceneDepth);
}

float2 acosFast_vec2(float2 x)
{
	return float2(acosFast(x.x), acosFast(x.y));
}

float3 acosFast_vec3(float3 x)
{
	return float3(acosFast(x.x), acosFast(x.y), acosFast(x.z));
}



// Number of bits in the visibility bitmask
#define BITMASK_SECTOR_COUNT 32

// Converts a min and max horizon angle into a bitmask
uint GetOccludedBitmaskFromHorizonAngles(/*inout FShaderPrintContext Context,*/ float MinHorizon, float MaxHorizon)
{
    uint StartHorizonInt = MinHorizon * BITMASK_SECTOR_COUNT;
	// Note: ceil errs on the side of over-occlusion, the smallest positive angle will still occlude an entire sector
    uint AngleHorizonInt = ceil((MaxHorizon - MinHorizon) * BITMASK_SECTOR_COUNT);
    uint AngleHorizonBitfield = AngleHorizonInt > 0u ? (0xFFFFFFFFu >> (BITMASK_SECTOR_COUNT - AngleHorizonInt)) : 0u;
    uint StepOccludedBitmask = AngleHorizonBitfield << StartHorizonInt;
    return StepOccludedBitmask;
}

// return Linear depth
float SampleSceneDepth(float2 ScreenUV)
{
    if (HORIZON_SEARCH_USE_HZB)
    {
        float2 HZBUV = (ScreenUV - _HZBUvToScreenUVScaleBias.zw) / _HZBUvToScreenUVScaleBias.xy;
        float DeviceHZB = FurthestHZBTexture.SampleLevel(GlobalPointClampedSampler, HZBUV, 0.0).x;
        return ConvertFromDeviceZ(DeviceHZB, ce_Projection);
    }
    float deviceZ = SceneDepthTexture.SampleLevel(GlobalPointClampedSampler, ScreenUV, 0.0).x;
    return ConvertFromDeviceZ(deviceZ, ce_Projection);
}

void UpdateOccludedBitmaskForStep(
	//inout FShaderPrintContext Context,
	float2 SampleScreenPos,
	float3 ViewSpacePosition,
	float3 ViewVector,
	float3 ViewNormal,
	float SampleThickness,
	float InvForegroundDistance,
	float SamplingDirection,
	float NormalAngle,
	bool bUpdateAO,
	inout uint OccludedBitmask,
	inout float3 Lighting)
{
    float2 SampleScreenPosUV = SampleScreenPos * _View_ScreenPositionScaleBias.xy + _View_ScreenPositionScaleBias.wz;
    float SampleDepth = SampleSceneDepth(SampleScreenPosUV);
    float3 SampleViewPos = ScreenToViewPos(SampleScreenPosUV, SampleDepth);
    float3 SampleDelta = SampleViewPos - ViewSpacePosition;
    float3 SampleDeltaBackface = SampleDelta - ViewVector * SampleThickness;
    float3 ToSample = normalize(SampleDelta);

    float2 HorizonAngles = acosFast_vec2(float2(dot(ToSample, ViewVector), dot(normalize(SampleDeltaBackface), ViewVector)));
    HorizonAngles = saturate((SamplingDirection * -HorizonAngles + NormalAngle + .5f * PI) / PI);
    HorizonAngles = select(SamplingDirection > 0, HorizonAngles.yx, HorizonAngles.xy);

    uint StepOccludedBitmask = GetOccludedBitmaskFromHorizonAngles(/*Context,*/ HorizonAngles.x, HorizonAngles.y);

    if (bUpdateAO)
    {
        OccludedBitmask |= StepOccludedBitmask;
    }
}

void UpdateOccludedHorizonForStep(
	//inout FShaderPrintContext Context,
	float2 SampleScreenPos,
	float3 ViewSpacePosition,
	float3 ViewVector,
	float3 ViewNormal,
	float InvForegroundDistance,
	float LowHorizonCos,
	bool bUpdateAO,
	inout float HorizonCos,
	inout float3 Lighting)
{
    float2 SampleScreenPosUV = SampleScreenPos * _View_ScreenPositionScaleBias.xy + _View_ScreenPositionScaleBias.wz;
    float SampleDepth = SampleSceneDepth(SampleScreenPosUV);
    float3 SampleViewPos = ScreenToViewPos(SampleScreenPosUV, SampleDepth);
    float3 SampleDelta = SampleViewPos - ViewSpacePosition;

    float SampleDist = length(SampleDelta);
    float3 ToSample = SampleDelta / SampleDist;

    float NewHorizonCos = dot(ToSample, ViewVector);
	
    if (bUpdateAO)
    {
		// Fade out occlusion from samples that are in front of the search area
        NewHorizonCos = lerp(NewHorizonCos, LowHorizonCos, saturate(pow(abs(SampleDelta.z) * InvForegroundDistance, ForegroundSampleRejectPower)));
        HorizonCos = max(HorizonCos, NewHorizonCos);
    }
}



void CalculateAOHorizonSearch(
    uint2 PixelCoord,
	float SceneDepth,
	float3 WorldNormal,
	out float OutAO,
	out float3 OutBentNormal,
	out float3 OutLighting)
{
    const float2 ScreenUV = (PixelCoord + 0.5f) * _View_BufferSizeAndInvSize.zw;
    // from uv space to NDC space
    float2 ScreenPos = (ScreenUV - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;

	// Used by horizon angle search to reject samples in the foreground 
    float InvForegroundDistance = 1.0f / (SceneDepth * ForegroundSampleRejectDistanceFraction);
	// Used by bitmask search to give thickness to depth buffer samples
    float SampleThickness = SceneDepth * ForegroundSampleRejectDistanceFraction * .3f;

    float3 ViewSpacePosition = ScreenToViewPos(ScreenUV, SceneDepth);
    float DistanceToCamera = length(ViewSpacePosition);

    float3 ViewVector = -ViewSpacePosition / DistanceToCamera;
    float3 ViewNormal = mul(float4(WorldNormal, 0), _View_TranslatedWorldToView).xyz;

    // TODO will use STBN later
    float2 UniformRandom = RandomWithFrame(PixelCoord, _View_StateFrameIndex, 0);
    //float2 UniformRandom = BlueNoiseVec2(PixelCoord, _View_StateFrameIndex);
	
    float Visibility = 0;
    float3 ViewBentNormal = 0;  
    float3 Lighting = 0;

    float EffectiveSliceCount = SliceCount;

	// "Algorithm 1" in "Practical Real-Time Strategies for Accurate Indirect Occlusion"
    for (float SliceIndex = 0; SliceIndex < EffectiveSliceCount; SliceIndex++)
    {
        float SliceFraction = (SliceIndex + UniformRandom.x) / EffectiveSliceCount;
        float Phi = SliceFraction * PI;
        float CosPhi = cos(Phi);
        float SinPhi = sin(Phi);
        float3 SliceDirection = float3(CosPhi, SinPhi, 0);
			
        float3 OrthogonalSliceDirection = SliceDirection - (dot(SliceDirection, ViewVector) * ViewVector);
        float3 AxisVector = normalize(cross(OrthogonalSliceDirection, ViewVector));
        float3 ProjectedNormal = ViewNormal - AxisVector * dot(ViewNormal, AxisVector);
        float SignNorm = sign(dot(OrthogonalSliceDirection, ProjectedNormal));
        float ProjectedNormalLength = length(ProjectedNormal);
        float CosNormal = (float) saturate(dot(ProjectedNormal, ViewVector) / ProjectedNormalLength);
        float NormalAngle = SignNorm * acosFast(CosNormal);

        float StepsPerSlice = StepsPerSliceForAO;

        float LowHorizonCos0 = cos(NormalAngle + .5f * PI);
        float LowHorizonCos1 = cos(NormalAngle - .5f * PI);
        float HorizonCos0 = LowHorizonCos0;
        float HorizonCos1 = LowHorizonCos1;
        uint OccludedBitmask = 0;

        for (float StepIndex = 0; StepIndex < StepsPerSlice; StepIndex++)
        {
            // R1 sequence
            float StepBaseNoise = float(SliceIndex + StepIndex * StepsPerSlice) * 0.6180339887498948482f;
            float StepNoise = frac(UniformRandom.y + StepBaseNoise);

            float StepFraction = (StepIndex + StepNoise) / StepsPerSliceForAO;
			// More samples near the start, scale to sample radius as a fraction of the viewport
            float2 StepRadius = StepFraction * StepFraction * MaxScreenFractionForAO;
			
            bool bUpdateAO =  true;
			// Offset by a texel to avoid self-occlusion (only happens with HZB disabled), transform to screen space 
            float2 SampleOffset = 2.0f * (StepRadius + _View_SizeAndInvSize.zw) * SliceDirection.xy;

            float2 SampleScreenPos0 = ScreenPos + SampleOffset;
            //float FadeForGI = 1.0f - saturate(StepRadius.x * FadeMulForGI + FadeAddForGI);

            if (all(SampleScreenPos0 > -1) && all(SampleScreenPos0 < 1))
            {
                if (HORIZON_SEARCH_VISIBILITY_BITMASK)
                {
                    UpdateOccludedBitmaskForStep(/*Context, */SampleScreenPos0, ViewSpacePosition, ViewVector, ViewNormal, SampleThickness, InvForegroundDistance, 1.0f, NormalAngle, bUpdateAO, OccludedBitmask, Lighting);
                }
                else
                {
                    UpdateOccludedHorizonForStep( /*Context,*/SampleScreenPos0, ViewSpacePosition, ViewVector, ViewNormal, InvForegroundDistance, LowHorizonCos0, bUpdateAO, HorizonCos0, Lighting);
                }
            }

            float2 SampleScreenPos1 = ScreenPos - SampleOffset;

            if (all(SampleScreenPos1 > -1) && all(SampleScreenPos1 < 1))
            {
                if (HORIZON_SEARCH_VISIBILITY_BITMASK)
                {
					UpdateOccludedBitmaskForStep(/*Context, */SampleScreenPos1, ViewSpacePosition, ViewVector, ViewNormal, SampleThickness, InvForegroundDistance, -1.0f, NormalAngle, bUpdateAO, OccludedBitmask, Lighting);
                }
                else
                {
                    UpdateOccludedHorizonForStep(/*Context,*/ SampleScreenPos1, ViewSpacePosition, ViewVector, ViewNormal, InvForegroundDistance, LowHorizonCos1, bUpdateAO, HorizonCos1, Lighting);
                }

            }
        }

        float SliceVisibility;
        if (HORIZON_SEARCH_VISIBILITY_BITMASK)
        {
            SliceVisibility = 1.0f - (float) countbits(OccludedBitmask) / (float) BITMASK_SECTOR_COUNT;
            if (OUTPUT_BENT_NORMAL)
            {
                // Not implemented, visual assert
                ViewBentNormal = float3(1, 0, 0);
            }

        }
        else
        {
            float H0 = -acosFast(HorizonCos1);
            float H1 = acosFast(HorizonCos0);
				
			// Analytical integral of the cosine weighted visibility between horizon angles
            float IntegralArc0 = (CosNormal + 2 * H0 * sin(NormalAngle) - cos(2 * H0 - NormalAngle)) / 4.0f;
            float IntegralArc1 = (CosNormal + 2 * H1 * sin(NormalAngle) - cos(2 * H1 - NormalAngle)) / 4.0f;
            SliceVisibility = ProjectedNormalLength * (IntegralArc0 + IntegralArc1);
				
            if (OUTPUT_BENT_NORMAL)
            {
                // "Algorithm 2" in "Practical Real-Time Strategies for Accurate Indirect Occlusion"
                float t0 = (6 * sin(H0 - NormalAngle) - sin(3 * H0 - NormalAngle) + 6 * sin(H1 - NormalAngle) - sin(3 * H1 - NormalAngle) + 16 * sin(NormalAngle) - 3 * (sin(H0 + NormalAngle) + sin(H1 + NormalAngle))) / 12.0f;
                float t1 = (-cos(3 * H0 - NormalAngle) - cos(3 * H1 - NormalAngle) + 8 * cos(NormalAngle) - 3 * (cos(H0 + NormalAngle) + cos(H1 + NormalAngle))) / 12.0f;
                float3 LocalBentNormal = float3(SliceDirection.x * t0, SliceDirection.y * t0, -t1);
                LocalBentNormal = mul(BuildRotationMatrix(float3(0, 0, -1), ViewVector), LocalBentNormal) * ProjectedNormalLength;
                ViewBentNormal += LocalBentNormal;
            }
        }
        
        Visibility += SliceVisibility;
    }

    Visibility = max(Visibility / EffectiveSliceCount, 0.03f);


    if (OUTPUT_BENT_NORMAL)
    {
        // Overwrite the length of the bent normal with AO for consistency
        OutBentNormal = mul(normalize(ViewBentNormal), (float3x3) _View_ViewToTranslatedWorld) * Visibility;
    }
    else
    {
        OutBentNormal = 0;
    }

    OutAO = Visibility;
    OutLighting = Lighting * (PI / EffectiveSliceCount);
}



void CalculateAOStochasticHemisphere(
	float2 ScreenUV,
    uint2 PixelCoord,
	float SceneDepth,
	float3 WorldPosition,
	float3 WorldNormal,
	float TraceDistance,
	out float OutAO,
	out float3 OutBentNormal)
{
    float3x3 TangentBasis = GetTangentBasis(WorldNormal);
    float DepthThresholdScale = GetScreenTraceDepthThresholdScale(ScreenUV);
    uint NumPixelSamples = NUM_PIXEL_RAYS;

    float3 UnoccludedSum = 0;
    OutBentNormal = 0;
    OutAO = 0.0f;

    UNROLL_N(NUM_PIXEL_RAYS)
    for (uint PixelRayIndex = 0; PixelRayIndex < NUM_PIXEL_RAYS; PixelRayIndex++)
    {
        float2 UniformRandom = RandomWithFrame(PixelCoord, _View_StateFrameIndex * NUM_PIXEL_RAYS + PixelRayIndex, 1);
        //float2 UniformRandom = BlueNoiseVec2(Coord.SvPosition, (_View_StateFrameIndex * NumPixelSamples + PixelRayIndex));

		//@todo - other shading models
        float4 HemisphereSample = CosineSampleHemisphere(UniformRandom);
        float3 RayDirection = mul(HemisphereSample.xyz, TangentBasis);
        float DirectionVisible = 1;

		#define TRACE_SCREEN 1
		#if TRACE_SCREEN
		{
            uint NumSteps = 4;
            float StartMipLevel = 0;

            uint2 NoiseCoord = PixelCoord * uint2(NumPixelSamples, 1) + uint2(PixelRayIndex, 0);
            float StepOffset = InterleavedGradientNoise(NoiseCoord + 0.5f, _View_StateFrameIndex);

            float RayRoughness = .2f;

            bool bHit = false;
            float Level;
            float3 HitUVz;
            bool bRayWasClipped;

            float _View_ProjectionDepthThicknessScale = 1.f;

            FSSRTRay Ray = InitScreenSpaceRayFromWorldSpace(
            _View_TranslatedWorldToView,
                _View_TranslatedWorldToClip,
                _View_ViewToClip,
				WorldPosition, RayDirection,
				/* WorldTMax = */ TraceDistance,
				/* SceneDepth = */ SceneDepth,
				/* SlopeCompareToleranceScale */ SlopeCompareToleranceScale * DepthThresholdScale * (float) NumSteps * _View_ProjectionDepthThicknessScale,
				/* bExtendRayToScreenBorder = */ false,
				/* out */ bRayWasClipped);

            bool bUncertain;
            float3 DebugOutput;

            CastScreenSpaceRay(
				FurthestHZBTexture, FurthestHZBTextureSampler,
				StartMipLevel,
				Ray, RayRoughness, NumSteps, StepOffset -.9f, // why 0.9f?
				_HZBUvFactorAndInvFactor, 
                true,
				/* out */ HitUVz,
				/* out */ Level,
				/* out */ bHit,
				/* out */ bUncertain);


            bHit = bHit && !bUncertain;

            DirectionVisible = bHit ? 0.0f : 1.0f;
        }
        #endif

        UnoccludedSum += RayDirection;
        OutBentNormal += RayDirection * DirectionVisible;
        OutAO += DirectionVisible;
    }

    OutAO /= NumPixelSamples;

    float NormalizeFactor = length(OutBentNormal);

    if (NormalizeFactor > 0)
    {
        OutBentNormal = OutBentNormal * (OutAO / NormalizeFactor);
    }
}

// we don't need 2d array(it's used for substrate closure)
RWTexture2D<uint> RWShortRangeAO;
RWTexture2D<float4> RWShortRangeAODebug;

Texture2D<float> DownsampledSceneDepth;
Texture2D<float3> DownsampledWorldNormal;


[numthreads(THREADGROUP_SIZE, THREADGROUP_SIZE, 1)]
void ScreenSpaceShortRangeAOCS(
	uint2 GroupId : SV_GroupID,
	uint2 DispatchThreadId : SV_DispatchThreadID,
	uint2 GroupThreadId : SV_GroupThreadID)
{
    const uint2 ShortRangeAOCoord = DispatchThreadId.xy;

    if (all(ShortRangeAOCoord >= ShortRangeAOViewMin) && all(ShortRangeAOCoord < ShortRangeAOViewMin + ShortRangeAOViewSize))
    {
        const float2 ScreenUV = (ShortRangeAOCoord + .5f) * _View_BufferSizeAndInvSize.zw;
        float SceneDepth = -1.0f;
        float3 WorldNormal = float3(0, 0, 1);

        if (DOWNSAMPLE_FACTOR == 2)
        {
            SceneDepth = DownsampledSceneDepth[ShortRangeAOCoord.xy];
            WorldNormal = normalize(DownsampledWorldNormal[ShortRangeAOCoord.xy]);
        }
		else
		{
            // what's this used for?
            const float DummyMaxRoughnessToTrace = 0.5f;

		    SceneDepth = _DepthMap[ShortRangeAOCoord.xy];
            // get world normal from gbuffer
            float3 normalData;
            DecodeFromNormalBuffer(_GBuffer1.Load(int3(ShortRangeAOCoord.xy, 0)), normalData);
            WorldNormal = normalize(normalData);
            
        }

        float SceneLinearDepth = ConvertToScreenDepth(SceneDepth);

        float AmbientOcclusion = 0.0f;
        float3 BentNormal = WorldNormal;
        float3 Lighting = 0.0f;

        if (SceneDepth >= 0.0f)
        {
            if (HORIZON_SEARCH)
            {
                CalculateAOHorizonSearch(ShortRangeAOCoord, SceneLinearDepth, WorldNormal, AmbientOcclusion, BentNormal, Lighting);
            }
            else
            {
                float3 WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, SceneLinearDepth);
                float TraceDistance = MaxScreenFractionForAO.x * 2.0 * GetScreenRayLengthMultiplierForProjectionType(SceneLinearDepth).x;
                TraceDistance *= TraceDistanceScale;
                CalculateAOStochasticHemisphere(ScreenUV, ShortRangeAOCoord, SceneLinearDepth, WorldPosition, WorldNormal, TraceDistance, AmbientOcclusion, BentNormal);
            }
            
        }

        if (OUTPUT_BENT_NORMAL)
        {
            RWShortRangeAO[ShortRangeAOCoord] = PackScreenBentNormal(BentNormal);
            RWShortRangeAODebug[ShortRangeAOCoord] = float4(BentNormal, 1.f);

        }
        else
        {
            RWShortRangeAO[ShortRangeAOCoord] = AmbientOcclusion;
            RWShortRangeAODebug[ShortRangeAOCoord] = float4(AmbientOcclusion.xxx, 1.f);
        }
    }
}