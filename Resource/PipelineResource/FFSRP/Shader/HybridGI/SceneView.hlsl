#ifndef HYBRIDGI_SCENE_VIEW_HLSL
#define HYBRIDGI_SCENE_VIEW_HLSL

cbuffer _cbView
{
	matrix ce_View;
	matrix ce_Projection;
	matrix ce_InvView;
	matrix ce_InvProjection;
	matrix ce_InvViewProjMatrix;
	float3 ce_CameraPos;
    float3 ce_CameraTilePosition;

    // Last Frame
    matrix ce_PreViewMatrix;
    matrix ce_PreProjMatrix;
    matrix ce_PreInvViewMatrix;
    matrix ce_PreInvProjMatrix;
    matrix ce_PreInvViewProjMatrix;
    matrix ce_ClipToPrevClipMat;
    matrix ce_ClipToPrevClipMatNoJitter;
    float4 ce_TemporalJitter;
    float3 ce_PreCameraPos;

	matrix _View_ClipToPrevClip;
	matrix _View_ScreenToTranslatedWorld;
	matrix _LastFrame_View;
	matrix _LastFrame_Projection;

    float4 _View_SizeAndInvSize;
    float4 _View_BufferSizeAndInvSize;
	float4 _View_ScreenPositionScaleBias;
	uint2  _View_RectMin;
    // a lot of params from UE
    uint2  _View_RectMax;
    float2 _View_SceneColorBlinearUVMin;
    float2 _View_SceneColorBlinearUVMax;
    float4 _View_ScreenRayLengthMultiplier;
    float4 _HZBUvFactorAndInvFactor;
    float4 _HZBUvToScreenUVScaleBias;

    uint _View_StateFrameIndex;
	uint _View_StateFrameIndexMod8;

    matrix _View_TranslatedWorldToView;
    matrix _View_ViewToTranslatedWorld;
    matrix _View_TranslatedWorldToClip;
    matrix _View_ViewToClip;
	float3 _View_PrevPreViewTranslation;
	float3 _View_PreViewTranslation;
	float _View_PreExposure;
    bool _View_UseReverseZ;
    float4 _View_ScreenToViewSpace;
}

// inverse operation of ConvertFromDeviceZ()
// @param SceneDepth (linear in world units, W)
// @return DeviceZ (Z/W)
float ConvertToDeviceZ(float SceneDepth)
{
	// Perspective
	// Row major to column major.
	float m32 = ce_Projection[2][3];
	float m22 = ce_Projection[2][2];
    return m22 + m32 / SceneDepth;
}

float ConvertToScreenDepth(float deviceZ)
{
    if ((_View_UseReverseZ && deviceZ <= 0.0f) || (!_View_UseReverseZ && deviceZ >= 1.0f))
        return 0.0f;
	
	// Row major to column major.
    float m32 = ce_Projection[2][3];
    float m22 = ce_Projection[2][2];
    return m32 / (deviceZ - m22);
}

float ConvertToScreenDepthLastFrame(float lastFrameDeviceZ)
{
	// Row major to column major.
	float m32 = _LastFrame_Projection[2][3];
	float m22 = _LastFrame_Projection[2][2];
	return m32 / (lastFrameDeviceZ - m22);
}
// input is linear depth
float3 GetWorldPositionFromScreenUV(float2 ScreenUV, float sceneDepth) 
{
	float deviceDepth = ConvertToDeviceZ(sceneDepth);
	float4 positionCS = float4(ScreenUV.x * 2.0 - 1.0, 1.0 - ScreenUV.y * 2.0, deviceDepth, 1.0);
	float4 resutl = mul(ce_InvViewProjMatrix, positionCS);
    return resutl.xyz / resutl.w;
}

// ScreenPosition is NDC not pixel position
float3 GetWorldPositionFromScreenPosition(float2 ScreenPosition, float deviceZ)
{
	float4 positionCS = float4(ScreenPosition, deviceZ, 1.0);
	float4 result = mul(ce_InvViewProjMatrix, positionCS);
	return result.xyz / result.w;
}

float3 GetViewPositonFromScreenUV(float2 ScreenUV, float linearDepth)
{
    float deviceDepth = ConvertToDeviceZ(linearDepth);
	float4 positionCS = float4(ScreenUV.x * 2.0 - 1.0, 1.0 - ScreenUV.y * 2.0, deviceDepth, 1.0);
	float4 resutl = mul(ce_InvProjection, positionCS);
    return resutl.xyz / resutl.w;
}

float GetSceneDepthFromDeviceDepth(uint2 ScreenPosition, float deviceDepth)
{
    float linearDepth = ConvertToScreenDepth(deviceDepth);
	float2 ScreenUV = float2(ScreenPosition) * _View_BufferSizeAndInvSize.zw;
	return GetViewPositonFromScreenUV(ScreenUV, linearDepth).z;
}

// In many locations we calculate screen ray length using scene depth, this does not work for Ortho
// This code moves that calculation to the view buffer and reduces the SceneDepth option to 1 MAD
float2 GetScreenRayLengthMultiplierForProjectionType(float SceneDepth)
{
    return _View_ScreenRayLengthMultiplier.xy * SceneDepth + _View_ScreenRayLengthMultiplier.zw;
}

#endif