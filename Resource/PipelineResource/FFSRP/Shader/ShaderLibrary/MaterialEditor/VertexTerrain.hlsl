#ifndef VERTEX_TERRAIN_HLSL
#define VERTEX_TERRAIN_HLSL

#define SURFACE_FLAT        0
#define SURFACE_SPHERICAL   1
#define SURFACE_WGS84       2
static const float TERRAIN_Y_SCALE = 1.f / 128.0f;

struct TerrainData
{
    float4 GridDim;
    float4 WorldTranslation;
    float4 WorldScale;
    float4 PatchOffsetAndScale;
    uint SlotIndex;
    uint LodInfo;
    uint PatchSize;
};

struct TerrainVSToPS
{
    float4 Position;
    float4 Normal;
    float4 Tangent;
    float4 Bitangent;
    float2 TexCoord;
};

float4 GetHeightmapData(uint2 Location, uint Mip, uint Index)
{
#if !defined(TERRAIN_CUSTOM) && !defined(TERRAIN_WEIGHT_BLEND)
    return _TerrainHeightmapStore.Load(uint4(Location, Index >> 16U, Mip));
#else
    return Heightmap.Load(uint3(Location, Mip));
#endif
}

float4 DecodeHeightmap(float4 Value, uint SurfaceType)
{
    Value.xy *= 255.f;
    Value.zw *= 2.f;
    Value.zw -= 1.f;
    float Height = (Value.y * 256.f + Value.x);
    Height = Height == 0.f ? 0.f / 0.f : Height - 32768.f;
    if (SurfaceType != SURFACE_WGS84)
    {
        Height *= TERRAIN_Y_SCALE;
    }
    return float4(Value.z, sqrt(1.f - saturate(dot(Value.zw, Value.zw))), -Value.w, Height);
}

void GetWorldTBN(float3 P, float3 N, out float4 Tangent, out float4 Bitangent, out float4 Normal)
{
    Normal = float4(normalize(P), 0.f);
    Tangent = float4(normalize(cross(Normal.xyz, float3(0.f, 1.f, 0.f))), 0.f);
    Bitangent = float4(normalize(cross(Tangent.xyz, Normal.xyz)), 0.f);

#ifdef TERRAIN_USE_WORLD_NORMAL
    Normal = float4(N * float3(1.f, sign(P.y), 1.f), 0.f);
#else
    Normal = Tangent * N.x + Normal * N.y + Bitangent * N.z;
#endif
}

float2 CalculateLodTransition(float2 PositionXZ, uint LodInfo, uint PatchSize)
{
    int HalfPatchSize = PatchSize >> 1U;
    int2 Coord = PositionXZ;
    int2 Neighbor = HalfPatchSize < PositionXZ;
    int2 Index = (Neighbor.yx << 1) + int2(0, 1);
    int2 CoordOffset = HalfPatchSize - abs(Coord - HalfPatchSize);
    int2 LoDOffset = (LodInfo >> 8 * Index) & 0xFF;
    int2 Mask = (1U << max(LoDOffset - CoordOffset.yx, 0)) - 1;
    return Coord & ~Mask;
}

TerrainVSToPS GetTerrainVSToPS(float3 Position, TerrainData Input, uint SurfaceType)
{
    TerrainVSToPS Output = (TerrainVSToPS)0;
    uint slotIndex = 0u;
    uint4 lodInfo = 0.xxxx;
    slotIndex = Input.SlotIndex;
    lodInfo = Input.LodInfo;

#ifdef TERRAIN_CUSTOM
    float2 LocalPosition = Position.xz * Input.PatchOffsetAndScale.zw + Input.PatchOffsetAndScale.xy;
    Output.Position = float4(LocalPosition.x, Position.y, LocalPosition.y, 1.f);
#else
    Position.xz = CalculateLodTransition(Position.xz, lodInfo, Input.PatchSize);
    Output.Position = float4(mad(Input.PatchOffsetAndScale.w, Position, Input.PatchOffsetAndScale.xyz), 1.f);
#endif

    float4 NormalAndHeight = DecodeHeightmap(GetHeightmapData(Position.xz, 0U, slotIndex), SurfaceType);
    NormalAndHeight.w *= Input.WorldScale.y;
    
#ifdef TERRAIN_CUSTOM
    Output.TexCoord = (Position.xz + .5f) / (float2(Input.PatchSize & 0xFFFFU, Input.PatchSize >> 16U) - 1.f);
#elif defined(TERRAIN_OCEAN)
    Output.TexCoord = float2((Output.Position.x + .5f) * Input.GridDim.z, (Output.Position.z + .5f) * Input.GridDim.w);
#elif defined(TERRAIN_WEIGHT_BLEND)
    Output.TexCoord = float2(Output.Position.x, Input.GridDim.y - Output.Position.z);
#else
    float HalfPatchSize = Input.PatchSize * .5f;
    float2 Distance = float2(Position.x - HalfPatchSize, HalfPatchSize - Position.z);
    Output.TexCoord = (Position.xz + .5f * (1.f + step(HalfPatchSize, abs(Distance)) * sign(Distance))) / (Input.PatchSize + 1.f);
#endif

    if (SurfaceType == SURFACE_FLAT)
    {
        Output.Position.xz = mad(Output.Position.xz, Input.WorldScale.xz, Input.WorldTranslation.xz);
        Output.Position.y = NormalAndHeight.w + Input.WorldTranslation.y;
        Output.Normal.xyz = NormalAndHeight.xyz;
    }

    if (SurfaceType == SURFACE_SPHERICAL)
    {
        float Phi = M_PI * (1.f - Output.Position.z * Input.GridDim.w);
        float Theta = 2.f * M_PI * Output.Position.x * Input.GridDim.z;
        float Radius = Input.WorldScale.x + NormalAndHeight.w;
        Output.Position.xyz = float3(sin(Phi) * cos(Theta), cos(Phi), sin(Phi) * sin(Theta)) * Radius;
        Output.Position.xyz += Input.WorldTranslation.xyz;
        Output.Position.w = 1.f;
        Output.Normal = float4(NormalAndHeight.xyz * float3(1.f, sign(.5f * M_PI - Phi), 1.f), 0.f);
    }

    if (SurfaceType == SURFACE_WGS84)
    {
        const float A = 1.f;
        const float B = .9966471893f;
        const float B2 = B * B;
        const float E2 = 1.f - B2;

        float Longitude = M_PI * (2.f * Output.Position.x * Input.GridDim.z - 1.f);
        float Latitude = .5f * M_PI * (2.f * Output.Position.z * Input.GridDim.w - 1.f);
        float Radius = Input.WorldScale.x;
        float SinLat = sin(Latitude);
        float CosLat = cos(Latitude);

        float N = Radius * rsqrt(1.f - E2 * SinLat * SinLat);
        float Height = NormalAndHeight.w;
        float NH = N + Height;

        Output.Position.xyz = float3(NH * CosLat * sin(Longitude), (B2 * N + Height) * SinLat, -NH * CosLat * cos(Longitude));

        GetWorldTBN(Output.Position.xyz, NormalAndHeight.xyz, Output.Tangent, Output.Bitangent, Output.Normal);
    #ifdef TERRAIN_OCEAN
        Output.Normal.xyz = normalize(Output.Position.xyz);
    #endif
        Output.Position.xyz += Input.WorldTranslation.xyz;
        Output.Position.w = 1.f;
    }

    return Output;
}

VSOutput TerrainVSInputToVSOutput(VSInput Input)
{
    uint instanceIndex = Input.instanceIDOffset + Input.instanceID;
    uint objectIndex = _ObjectIndexBuffer[instanceIndex];
    ObjectSceneData objectSceneData = ce_PerObject[objectIndex];
    PrimitiveSceneData primitiveData = ce_PerPrimitive[objectSceneData.ce_PrimitiveIndex];

    TerrainData Data;
    {
#ifdef MATERIAL_EDITOR
        PrimitiveSceneData primitiveData = ce_PerPrimitive[objectSceneData.ce_PrimitiveIndex];
        MaterialData matData = ce_PerMaterial[ce_MaterialIndex];
    
        float4 GridDim = matData.GridDim;
        float4 WorldTranslation = matData.WorldTranslation;
        float4 WorldScale = matData.WorldScale;
        uint PatchSize = matData.PatchSize;
#endif
        
        Data.GridDim = GridDim;
        Data.WorldTranslation = WorldTranslation;
        Data.WorldScale = WorldScale;
        Data.PatchSize = PatchSize;
        Data.PatchOffsetAndScale = objectSceneData.PatchOffsetAndScale;
        Data.SlotIndex = objectSceneData.SlotIndex; 
        Data.LodInfo = objectSceneData.LodInfo;
    }
    
    TerrainVSToPS TerrainOutput = GetTerrainVSToPS(Input.position, Data, SURFACE_TYPE);
    float4 prevPosition = TerrainOutput.Position;
    TerrainOutput.Position.xyz = GetLargeCoordinateReltvPosition(TerrainOutput.Position.xyz, float3(0.0, 0.0, 0.0), ce_CameraTilePosition); 
    prevPosition.xyz = GetLargeCoordinateReltvPosition(prevPosition.xyz, float3(0.0, 0.0, 0.0), ce_PrevCameraTilePosition); 

    VSOutput Output = (VSOutput)0;
    {
        float3 positionWS = TerrainOutput.Position.xyz;
        Output.instanceID = instanceIndex;
        Output.normalWS = TerrainOutput.Normal.xyz;
        Output.tangentWS = TerrainOutput.Tangent;
        Output.binormalWS = TerrainOutput.Bitangent.xyz;
        Output.uvs[0].xy = TerrainOutput.TexCoord;

        float3 reservedPositionWS = positionWS;
        //Output = ExecuteVertexOut(Output, Input);
        positionWS = GetWorldPositionOffset(primitiveData, objectSceneData, reservedPositionWS, Output, Input);
        VSOutput reservedVSOut = Output;
        float4 reservedPositionNDC = mul(ce_Projection, mul(ce_View, float4(reservedPositionWS, 1.0)));
        reservedVSOut.positionNDC = reservedPositionNDC;

        #ifdef WORLD_POSITION_OFFSET_PREVIOUS
            prevPosition.xyz = GetPreviousWorldPositionOffset(prevPosition.xyz, reservedVSOut, Input);
        #else
            prevPosition.xyz = GetWorldPositionOffset(primitiveData, objectSceneData, prevPosition.xyz, reservedVSOut, Input);
        #endif

#if WRITES_VELOCITY_TO_GBUFFER
        Output.prePositionNDC = mul(ce_PreProjMatrix, mul(ce_PreViewMatrix, prevPosition));
        Output.nowPositionNDC = mul(ce_ProjectionNoJitter, mul(ce_View, float4(positionWS, 1.0f)));
        Output.nowPositionNDC.z = distance(positionWS, prevPosition.xyz);
#endif
        Output.positionNDC = mul(ce_Projection, mul(ce_View, float4(positionWS, 1.0)));
    }
    return Output;
}

#endif
